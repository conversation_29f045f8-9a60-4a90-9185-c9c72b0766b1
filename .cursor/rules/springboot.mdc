---
description: 
globs: 
alwaysApply: true
---
# Spring Boot 3 企业级最佳实践规范

## 1. 配置管理模块

### 1.1 配置文件组织
- **主配置文件**：`application.yml` 包含通用配置
- **环境配置**：`application-{profile}.yml` 按环境分离
- **配置优先级**：命令行参数 > 环境变量 > 配置文件
- **敏感信息**：使用环境变量或配置中心，禁止硬编码

### 1.2 配置属性绑定
- 使用 `@ConfigurationProperties` 进行类型安全的配置绑定
- 配置类使用 `@Validated` 进行参数校验
- 复杂配置使用嵌套类结构
- 提供默认值和配置文档

### 1.3 多环境管理
- **开发环境**：本地数据库，详细日志，热重载
- **测试环境**：内存数据库，模拟外部服务
- **生产环境**：外部配置，最小日志级别，性能监控

### 1.4 配置最佳实践
- 配置项命名使用 kebab-case
- 布尔值配置明确语义（enabled/disabled）
- 数值配置包含单位说明
- 定期审查和清理无用配置

## 2. 依赖注入模块

### 2.1 Bean 定义策略
- **组件扫描**：使用 `@Component`、`@Service`、`@Repository`、`@Controller`
- **配置类**：复杂 Bean 使用 `@Configuration` + `@Bean`
- **条件注册**：使用 `@ConditionalOn*` 注解进行条件装配
- **作用域管理**：明确 Bean 的生命周期和作用域

### 2.2 依赖注入方式
- **构造器注入**：推荐方式，保证依赖不可变
- **字段注入**：仅在测试中使用 `@Autowired`
- **Setter注入**：可选依赖使用
- **避免循环依赖**：重构代码结构，使用事件驱动

### 2.3 Bean 生命周期管理
- 使用 `@PostConstruct` 和 `@PreDestroy` 管理生命周期
- 实现 `InitializingBean` 和 `DisposableBean` 接口
- 资源清理在销毁方法中进行
- 异步初始化使用 `@Async` 注解

### 2.4 依赖注入最佳实践
- 接口编程，面向抽象依赖
- 使用 `@Qualifier` 解决多实现问题
- 避免过度依赖，保持类的单一职责
- 使用 `@Primary` 指定默认实现

## 3. 安全模块

### 3.1 认证机制
- **JWT 认证**：无状态认证，适合分布式应用
- **OAuth2 集成**：第三方登录和授权
- **多因素认证**：提高安全级别
- **会话管理**：合理设置超时和并发控制

### 3.2 授权策略
- **基于角色**：RBAC 模型，角色权限分离
- **基于资源**：细粒度权限控制
- **方法级安全**：使用 `@PreAuthorize` 和 `@PostAuthorize`
- **URL 级安全**：配置路径访问规则

### 3.3 数据安全
- **输入验证**：所有外部输入必须验证
- **SQL 注入防护**：使用参数化查询
- **XSS 防护**：输出编码和 CSP 策略
- **CSRF 防护**：API 使用 Token 验证

### 3.4 安全配置最佳实践
- 最小权限原则，默认拒绝访问
- 敏感操作记录审计日志
- 定期更新安全依赖
- 使用 HTTPS 和安全头配置

## 4. 性能优化模块

### 4.1 应用层优化
- **连接池配置**：数据库、Redis、HTTP 客户端
- **线程池调优**：异步任务和定时任务
- **JVM 参数**：堆内存、GC 策略、监控参数
- **启动优化**：延迟初始化、条件装配

### 4.2 缓存策略
- **本地缓存**：Caffeine 用于热点数据
- **分布式缓存**：Redis 用于共享数据
- **缓存层次**：L1（本地）+ L2（分布式）
- **缓存更新**：写入时更新、定时刷新、事件驱动

### 4.3 数据库优化
- **连接池配置**：HikariCP 参数调优
- **查询优化**：索引使用、分页查询、批量操作
- **事务管理**：只读事务、事务传播、超时设置
- **读写分离**：主从配置、路由策略

### 4.4 监控和诊断
- **应用指标**：JVM、业务指标、自定义指标
- **性能分析**：慢查询、热点方法识别
- **告警机制**：阈值监控、异常告警
- **健康检查**：Actuator 端点监控应用状态

## 5. 数据访问模块

### 5.1 JPA 最佳实践
- **实体设计**：合理的表关系、字段映射、索引策略
- **Repository 模式**：继承 JpaRepository，自定义查询方法
- **查询优化**：使用 `@Query` 注解、原生 SQL、Specification
- **懒加载策略**：避免 N+1 问题，合理使用 `@EntityGraph`

### 5.2 事务管理
- **声明式事务**：`@Transactional` 注解配置
- **事务传播**：根据业务场景选择传播行为
- **只读事务**：查询操作使用 `readOnly = true`
- **事务超时**：设置合理的超时时间

### 5.3 数据库连接管理
- **连接池配置**：最大连接数、超时设置、健康检查
- **多数据源**：主从分离、分库分表支持
- **连接泄漏检测**：监控长时间占用的连接
- **数据库监控**：连接数、慢查询、死锁检测

### 5.4 数据访问安全
- **参数化查询**：防止 SQL 注入
- **数据脱敏**：敏感数据加密存储
- **访问控制**：数据库用户权限最小化
- **审计日志**：记录数据变更操作

## 6. API 设计模块（RESTful）

### 6.1 URL 设计规范
- **资源命名**：使用名词复数形式，避免动词
- **层次结构**：体现资源间的关系
- **版本控制**：URL 路径或请求头中包含版本信息
- **查询参数**：过滤、排序、分页使用查询参数

### 6.2 HTTP 方法使用
- **GET**：获取资源，幂等操作
- **POST**：创建资源，非幂等操作
- **PUT**：完整更新资源，幂等操作
- **PATCH**：部分更新资源
- **DELETE**：删除资源，幂等操作

### 6.3 响应设计
- **状态码**：正确使用 HTTP 状态码
- **响应格式**：统一的 JSON 响应结构
- **错误处理**：标准化错误响应格式
- **分页响应**：包含总数、页码、页大小信息

### 6.4 API 文档和测试
- **OpenAPI 规范**：使用 Swagger 生成文档
- **接口测试**：单元测试、集成测试、契约测试
- **版本兼容**：向后兼容性保证
- **性能测试**：接口响应时间和并发测试

## 7. 异常处理模块

### 7.1 异常分类
- **业务异常**：可预期的业务逻辑异常
- **系统异常**：不可预期的技术异常
- **验证异常**：参数校验失败异常
- **外部服务异常**：第三方服务调用异常

### 7.2 异常处理策略
- **全局异常处理**：使用 `@ControllerAdvice` 统一处理
- **异常转换**：将底层异常转换为业务异常
- **异常日志**：记录异常堆栈和上下文信息
- **用户友好**：返回用户可理解的错误信息

### 7.3 异常响应格式
- **错误码**：业务错误码和 HTTP 状态码
- **错误信息**：简洁明了的错误描述
- **详细信息**：开发环境提供详细错误信息
- **请求追踪**：包含请求 ID 便于问题定位

### 7.4 异常监控
- **异常统计**：异常类型、频率统计
- **告警机制**：异常阈值告警
- **异常分析**：定期分析异常趋势
- **异常恢复**：自动重试和降级策略

## 8. 测试模块

### 8.1 测试分层策略
- **单元测试**：测试单个类或方法，使用 Mock
- **集成测试**：测试组件间交互，使用 TestContainers
- **端到端测试**：完整业务流程测试
- **性能测试**：负载测试、压力测试

### 8.2 测试工具和框架
- **JUnit 5**：测试框架，支持参数化测试
- **Mockito**：Mock 框架，模拟依赖对象
- **TestContainers**：集成测试中使用真实数据库
- **WireMock**：模拟外部 HTTP 服务

### 8.3 测试数据管理
- **测试数据隔离**：每个测试独立的数据环境
- **数据准备**：使用 `@Sql` 或 Builder 模式
- **数据清理**：测试后清理数据，避免影响其他测试
- **测试数据工厂**：统一的测试数据创建

### 8.4 测试质量保证
- **代码覆盖率**：目标覆盖率 80% 以上
- **测试命名**：清晰的测试方法命名
- **断言明确**：使用有意义的断言消息
- **测试维护**：定期更新和重构测试代码

## 9. 日志记录模块

### 9.1 日志级别管理
- **ERROR**：系统错误，需要立即处理
- **WARN**：警告信息，需要关注
- **INFO**：重要业务信息，正常流程记录
- **DEBUG**：调试信息，开发环境使用

### 9.2 日志内容规范
- **结构化日志**：使用 JSON 格式，便于解析
- **上下文信息**：包含用户 ID、请求 ID、业务标识
- **敏感信息**：避免记录密码、身份证等敏感数据
- **性能信息**：记录关键操作的执行时间

### 9.3 日志输出配置
- **控制台输出**：开发环境使用，格式化显示
- **文件输出**：生产环境使用，按日期滚动
- **远程日志**：集中式日志收集，如 ELK Stack
- **日志压缩**：历史日志压缩存储

### 9.4 日志监控和分析
- **日志聚合**：统一收集和存储
- **实时监控**：关键错误实时告警
- **日志分析**：业务指标分析、异常趋势分析
- **日志检索**：快速定位问题日志

## 10. 应用监控模块

### 10.1 Spring Boot Actuator
- **端点配置**：暴露必要的监控端点
- **健康检查**：自定义健康指示器
- **指标收集**：JVM、应用、业务指标
- **信息端点**：应用版本、构建信息

### 10.2 自定义监控
- **业务指标**：使用 Micrometer 收集业务数据
- **性能监控**：方法执行时间、数据库查询性能
- **错误监控**：异常统计和分析
- **用户行为**：关键业务操作追踪

### 10.3 日志与监控集成
- **结构化日志**：便于监控系统解析
- **关键事件记录**：业务关键节点日志
- **性能日志**：慢操作和资源使用情况
- **告警配置**：基于日志和指标的告警

### 10.4 生产环境监控
- **应用状态**：启动、运行、关闭状态监控
- **资源使用**：内存、CPU、线程池状态
- **外部依赖**：数据库、缓存、第三方服务状态
- **业务监控**：核心业务指标实时监控

## 11. 代码质量模块

### 11.1 编码规范
- **命名规范**：类名、方法名、变量名清晰表达意图
- **代码结构**：合理的包结构和类层次
- **注释规范**：必要的类和方法注释
- **代码复用**：避免重复代码，提取公共方法

### 11.2 设计原则
- **SOLID 原则**：单一职责、开闭原则等
- **DRY 原则**：不重复自己
- **KISS 原则**：保持简单
- **YAGNI 原则**：你不会需要它

### 11.3 代码审查
- **Pull Request**：代码合并前必须审查
- **审查清单**：功能、性能、安全、可维护性
- **自动化检查**：静态代码分析工具
- **知识分享**：通过代码审查传播最佳实践

### 11.4 重构策略
- **持续重构**：小步快跑，持续改进
- **测试保护**：重构前确保测试覆盖
- **重构时机**：新功能开发时同步重构
- **技术债务**：定期评估和偿还技术债务
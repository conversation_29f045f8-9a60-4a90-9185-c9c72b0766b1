@startuml
title 价格申请分页列表查询 GET /price/apply/list

start

:创建分页对象 Page<PriceApplyEntity>;
:调用 feignCmsRpc.getUserDataAccessV2() 获取用户权限数据;
if (权限数据 userDataAccessV2 是否为空?) then (是)
    :返回空 PageResult;
    stop
else (否)
    :调用 listPage(req, userDataAccessV2, page) 查询分页数据;
    note right: 不查草稿的数据
    :获取 entityPage 分页结果;

    :获取 records 列表;
    if (records 是否为空?) then (是)
        :返回空 PageResult;
        stop
    else (否)
        :转换为 PriceApplyListDto 列表;

        :提取申请人编码 createCodeList;
        :调用 sysUserService.listUserByEmpCode(createCodeList);
        if (loginUsers 非空?) then (是)
            :构建 userNameMap 用户名映射表;
        endif

        :提取商机编码 businessCodeList;
        :调用 businessOpportunityService.list(...) 查询商机信息;
        if (businessOpportunityEntityList 非空?) then (是)
            :构建 businessOpportunityMap 商机映射表;
        endif

        while (循环priceApplyList)
            :设置创建人姓名 createName;
            :设置是否本人申请 isSelfApply;
            if (存在对应商机信息?)
                :设置商机名称 businessName;
            endif
            :解密地址信息 mapAddress;
        endwhile
    endif
endif

:返回填充后的 PageResult;
stop

@enduml
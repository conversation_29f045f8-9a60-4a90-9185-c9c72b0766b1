package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.model.v2.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentTerminationEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSnapshotEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentTerminationService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSnapshotService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ILedgerService;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.PointContractVO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectPointsDTO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractWithPointsVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.PointsContractStatusVO;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.BusinessOpportunityStatusVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentBelongEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessChangeStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAssistantEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PaymentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PropertyTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.SnapshotSourceTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.building.BusinessPointParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.BuildingVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.VerifyRatingDTO;
import com.coocaa.ad.cheese.cms.venue.service.helper.ContractApplyServiceHelper;
import com.coocaa.ad.cheese.cms.venue.service.helper.LargeScreenHelper;
import com.coocaa.ad.cheese.cms.venue.vo.ConfigVO;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingGeneVO;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingRatingDTO;
import com.coocaa.ad.cheese.cms.venue.vo.building.CmsResult;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDto;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentChangeTypeVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentTerminationVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailSimplifyVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDeviceDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDevicePointDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPaymentPeriodDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPointVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPriceApplyDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPricePeriodDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractProjectDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSimpleAbnormalVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSnapshotRecordVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierBankDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.SubContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.SubContractFeeDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.user.SysUserApproveVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.core.util.SecurityUtils;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.CommonException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.ad.translate.TranslatorFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.flipkart.zjsonpatch.JsonDiff;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 合同查询服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractReadService extends BaseContractService {
    private final ContractConvert contractConvert;
    private final TranslatorFactory translatorFactory;
    private final LargeScreenHelper largeScreenHelper;
    private final ContractFillingService contractFillingService;
    private final IContractService contractService;
    private final IContractSupplierService contractSupplierService;
    private final IContractSupplierBankService contractSupplierBankService;
    private final IContractProjectService projectService;
    private final IContractPriceApplyService priceApplyService;
    private final IContractDeviceService deviceService;
    private final IContractDevicePointService devicePointService;
    private final IContractPricePeriodService pricePeriodService;
    private final IContractPaymentPeriodService paymentPeriodService;
    private final IContractDepositSupplierService depositSupplierService;
    private final IContractSubService contractSubService;
    private final IContractAttachmentService attachmentService;
    private final IContractSubService subContractService;
    private final FeignMethWebRpc feignMethWebRpc;
    private final FeignMethH5Rpc feignMethH5Rpc;
    private final ConfigService configService;
    private final ILedgerService ledgerService;
    private final StringRedisTemplate stringRedisTemplate;
    private final IContractAmendmentChangeTypeService contractAmendmentChangeTypeService;
    private final IContractAmendmentTerminationService contractAmendmentTerminationService;
    private final IContractSnapshotService contractSnapshotService;
    private final DataAccessService dataAccessService;
    private final ContractApplyServiceHelper contractApplyServiceHelper;
    private final ContractAbnormalService contractAbnormalService;
    private final IContractDepositSupplierService iContractDepositSupplierService;

    @Value("#{'${contract.venue.template.large-screen.dict-codes:}'.split(',')}")
    private Set<String> largeScreenSizeCodes;

    // 变更触发的快照类型
    private static final Set<String> SNAPSHOT_CHANGE_SOURCE_TYPES = Set.of(SnapshotSourceTypeEnum.MODIFY_APPROVAL.getCode(), SnapshotSourceTypeEnum.SCHEDULE.getCode(), SnapshotSourceTypeEnum.ARCHIVE.getCode(), SnapshotSourceTypeEnum.AGREEMENT_SCHEDULE.getCode(), SnapshotSourceTypeEnum.AGREEMENT_ARCHIVE.getCode());
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            // 注册模块
            .registerModule(new JavaTimeModule())
            // 禁用时间戳
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // 设置时间格式
            .setDateFormat(new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN));

    private static final Set<Integer> CONTRACT_CHANGE_TYPES = Set.of(ContractTypeEnum.AGREEMENT.getCode(), ContractTypeEnum.AMENDMENT.getCode(), ContractTypeEnum.MODIFY.getCode());

    /**
     * 允许再次提交的状态
     */
    private final List<String> contractApplyStatusResubmit = Arrays.asList(ContractApplyStatusEnum.REJECT.getCode(), ContractApplyStatusEnum.WITHDRAW.getCode(), ContractApplyStatusEnum.DRAFT.getCode(), ContractApplyStatusEnum.RETURN.getCode());

    /**
     * 补充协议申请单(编辑)详情
     */
    public ContractEditDetailVO getAgreementApplyDetail(Integer contractId, boolean force) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("申请单不存在"));

        // 验证申请单
        validateApplyContract(contract, force);

        // 合同基本信息
        ContractEditDetailVO detail = contractConvert.toEditDetailVO(contract);
        fillingAttachments(contract, null, detail::setAttachments);

        // 项目、子合同、价格申请、终端信息、终端安装位置、单价周期、付款周期、供应商
        fillingProjects(contract, detail::setProjects);
        fillingPriceApplies(contract, detail::getProjects);
        fillingDevices(contract, detail::getProjects);
        fillingPoints(contract, detail::getProjects);
        fillingMainContractPricePeriods(contract, detail::getProjects);
        fillingMainContractPaymentPeriods(contract, detail::getProjects);
        fillingMainContractSuppliers(contract, false, detail::setSuppliers);

        fillingSubContracts(contract, detail::getProjects);
        fillingSubContractPricePeriods(contract, detail::getProjects);
        fillingSubContractPaymentPeriods(contract, detail::getProjects);
        fillingSubContractSuppliers(contract, false, detail::getProjects);

        // 翻译有些特定信息
        translatorFactory.translate(detail.getProjects());

        return detail;
    }

    /**
     * 申请单(编辑)详情
     */
    public ContractEditDetailVO getApplyDetail(Integer contractId, boolean force) {
        return getApplyDetail(contractId, force, Sets.newHashSet(AttachmentTypeEnum.MAIN_SEAL_PENDING.getCode(), AttachmentTypeEnum.MAIN_REFERENCE.getCode(), AttachmentTypeEnum.SUB_REFERENCE.getCode(), AttachmentTypeEnum.FEISHU_CONTRACT.getCode(), AttachmentTypeEnum.SUB_SEAL_PENDING.getCode()), null);
    }

    public ContractEditDetailVO getApplyDetail(Integer contractId, boolean force, Set<Integer> attachmentTypes, Map<String, Boolean> fillOptionMap) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("申请单不存在"));

        // 验证申请单
        validateApplyContract(contract, force);

        fillOptionMap = Optional.ofNullable(fillOptionMap).orElse(Map.of());

        // 合同基本信息
        ContractEditDetailVO detail = contractConvert.toEditDetailVO(contract);
        fillingAttachments(contract, attachmentTypes, detail::setAttachments);
        fillingMainContractDeposit(detail);

        // 项目、子合同、价格申请、终端信息、终端安装位置、单价周期、付款周期、供应商
        contractApplyServiceHelper.fillingProjects(contract, detail::setProjects);
        fillingPriceApplies(contract, detail::getProjects);
        fillingDevices(contract, detail::getProjects);
        fillingPoints(contract, detail::getProjects);
        fillingMainContractPricePeriods(contract, detail::getProjects);
        fillingMainContractPaymentPeriods(contract, detail::getProjects, fillOptionMap.getOrDefault("ledger", false), fillOptionMap.getOrDefault("planPaymentDateOrder", true));
        fillingMainContractSuppliers(contract, false, detail::setSuppliers);

        fillingSubContracts(contract, detail::getProjects);
        fillingSubContractPricePeriods(contract, detail::getProjects);
        fillingSubContractPaymentPeriods(contract, detail::getProjects, fillOptionMap.getOrDefault("subLedger", false), fillOptionMap.getOrDefault("planPaymentDateOrder", true));
        fillingSubContractSuppliers(contract, false, detail::getProjects);

        // 填充合同变更信息
        if (Stream.of(ContractTypeEnum.NORMAL, ContractTypeEnum.CHANGE, ContractTypeEnum.HISTORY)
                .noneMatch(e -> e.getCode().equals(contract.getContractType()))) {
            fillingAmendments(contractId, detail::setTermination, detail::setChangeType, detail::setChangeTypeName, detail::setSubChangeType, detail::setSubChangeTypeName);
        }

        // 翻译有些特定信息
        translatorFactory.translate(detail.getProjects());

        // 再次发起缓存，提交时快照使用
        String initiateKey = "cms:venue:initiate:" + detail.getApplyCode();
        stringRedisTemplate.opsForValue().set(initiateKey, JSON.toJSONString(detail));
        return detail;
    }

    /**
     * 申请单列表(分页)
     */
    public PageResponseVO<ContractPageVO> pageListApplies(PageRequestVO<ContractQueryParam> pageRequest) {
        return pageListContacts(null, null, pageRequest);
    }

    /**
     * 补充协议列表(分页)
     */
    public PageResponseVO<ContractPageVO> pageListSupplements(PageRequestVO<ContractQueryParam> pageRequest) {
        PageResponseVO<ContractPageVO> pageResponse = pageListContacts(null, null, pageRequest);

        // 补充协议记录补充原合同数据
        var rows = pageResponse.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return pageResponse;
        }
        // 查询补充协议记录补充原合同记录
        List<Integer> contractIds = rows.stream()
                .filter(e -> Objects.nonNull(e.getParentId()) && ContractTypeEnum.AGREEMENT.getCode()
                        .equals(e.getContractType())).map(ContractPageVO::getParentId).distinct().toList();
        if (CollectionUtils.isEmpty(contractIds)) {
            return pageResponse;
        }
        Map<Integer, ContractEntity> contractMap = contractService.lambdaQuery().in(ContractEntity::getId, contractIds)
                .list().stream().collect(Collectors.toMap(ContractEntity::getId, Function.identity(), (v1, v2) -> v1));
        if (MapUtils.isEmpty(contractMap)) {
            return pageResponse;
        }
        // 填充数据
        for (ContractPageVO row : rows) {
            if (Objects.isNull(row.getParentId())) {
                continue;
            }
            if (!ContractTypeEnum.AGREEMENT.getCode().equals(row.getContractType())) {
                continue;
            }
            //
            ContractEntity contract = contractMap.get(row.getParentId());
            if (Objects.isNull(contract)) {
                continue;
            }
            row.setParent(contractConvert.toDetailSimplifyVO(contract));
        }

        return pageResponse;
    }

    /**
     * 代理商审核列表(分页)
     */
    public PageResponseVO<ContractPageVO> pageListAgentAudit(PageRequestVO<ContractQueryParam> pageRequest) {
        return pageListContacts(BooleFlagEnum.NO, true, pageRequest);
    }

    /**
     * 合同展示详情
     *
     * @param contractId 合同ID
     * @return 展示详情
     */
    public ContractDetailVO getContractDetail(Integer contractId) {
        return getContractDetail(contractId, false);
    }

    /**
     * 对详情结果数据进行对比
     */
    public ContractDetailDiffVO contractDetailDiffWrapper(ContractDetailVO detailVO) {
        // 填充变更信息
        ContractSnapshotEntity snapshot = contractSnapshotService.lambdaQuery()
                .select(ContractSnapshotEntity::getId, ContractSnapshotEntity::getSnapshotVo)
                .eq(ContractSnapshotEntity::getContractId, detailVO.getId())
                .eq(ContractSnapshotEntity::getInvolvedId, 0)
                .notIn(ContractSnapshotEntity::getSourceType, SNAPSHOT_CHANGE_SOURCE_TYPES)
                .eq(ContractSnapshotEntity::getDeleted, BooleFlagEnum.NO.getCode()).last("ORDER BY id DESC LIMIT 1,1")
                .one();

        return contractDetailDiffHandle(snapshot, detailVO);
    }

    /**
     * 对详情结果数据进行对比（修改）
     */
    public ContractDetailDiffVO contractDetailDiffWrapper(ContractDetailVO detailVO, String sourceType) {
        ContractSnapshotEntity snapshot;
        // 未生效时，查询原合同的快照
        if (Objects.isNull(sourceType)) {
            snapshot = contractSnapshotService.lambdaQuery()
                    .select(ContractSnapshotEntity::getId, ContractSnapshotEntity::getSnapshotVo)
                    .eq(ContractSnapshotEntity::getContractId, detailVO.getParentId())
                    .notIn(ContractSnapshotEntity::getSourceType, SNAPSHOT_CHANGE_SOURCE_TYPES)
                    .eq(ContractSnapshotEntity::getDeleted, BooleFlagEnum.NO.getCode()).last("ORDER BY id DESC LIMIT 1")
                    .one();
        }
        // 生效时，查询生效日志
        else {
            snapshot = contractSnapshotService.lambdaQuery()
                    .select(ContractSnapshotEntity::getId, ContractSnapshotEntity::getSnapshotVo)
                    .eq(ContractSnapshotEntity::getContractId, detailVO.getParentId())
                    .eq(ContractSnapshotEntity::getInvolvedId, detailVO.getId())
                    .eq(ContractSnapshotEntity::getSourceType, sourceType)
                    .eq(ContractSnapshotEntity::getDeleted, BooleFlagEnum.NO.getCode()).last("ORDER BY id DESC LIMIT 1")
                    .one();
        }

        return contractDetailDiffHandle(snapshot, detailVO);
    }

    /**
     * 对比数据差异
     */
    public ContractDetailDiffVO contractDetailDiffHandle(ContractSnapshotEntity snapshot, ContractDetailVO detailVO) {
        ContractDetailDiffVO detailDiffVO = BeanUtil.toBean(detailVO, ContractDetailDiffVO.class);
        if (Objects.isNull(snapshot)) {
            return detailDiffVO;
        }

        try {
            JsonNode lastNode = OBJECT_MAPPER.readTree(snapshot.getSnapshotVo()).get("data");
            if (Objects.isNull(lastNode)) {
                log.warn("获取[{}]合同的变化详情：缺少最新的快照数据", detailDiffVO.getId());
                return detailDiffVO;
            }
            JsonNode currNode = OBJECT_MAPPER.readTree(OBJECT_MAPPER.writeValueAsString(detailVO));
            JsonNode patch = JsonDiff.asJson(normalizeNumbers(lastNode), normalizeNumbers(currNode));
            // 仅保留“replace”和“add”的操作
            List<String> changePaths = Lists.newArrayListWithExpectedSize(patch.size());
            // List<String> replacePaths = Lists.newArrayListWithExpectedSize(patch.size());
            // List<String> addPaths = Lists.newArrayListWithExpectedSize(patch.size());
            patch.findParents("op").forEach(op -> {
                String opType = op.get("op").asText();
                String path = op.get("path").asText();
                // 忽略附件非url的字段信息
                if (path.contains("/attachments/") && !path.contains("url")) {
                    return;
                }
                // 忽略id字段
                if (path.endsWith("/id")) {
                    return;
                }
                // 忽略parentId字段
                if (path.endsWith("/parentId")) {
                    return;
                }
                // 忽略applyCode字段
                if (path.endsWith("/applyCode")) {
                    return;
                }
                // 忽略（台账）已付金额
                if (path.contains("paymentPeriods") && path.endsWith("/paidAmount")) {
                    return;
                }
                // 记录所有内容，不区分替换、新增和移除等类型
                changePaths.add(path);
            });
            detailDiffVO.setReplaceItems(changePaths);
            detailDiffVO.setSnapshotId(AesUtils.encryptHex(String.valueOf(snapshot.getId())));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return detailDiffVO;
    }

    /**
     * 统一数字格式
     */
    private JsonNode normalizeNumbers(JsonNode node) {
        if (node.isObject()) {
            ObjectNode result = OBJECT_MAPPER.createObjectNode();
            node.fieldNames().forEachRemaining(field -> {
                result.set(field, normalizeNumbers(node.get(field)));
            });
            return result;
        } else if (node.isArray()) {
            ArrayNode result = OBJECT_MAPPER.createArrayNode();
            node.forEach(child -> result.add(normalizeNumbers(child)));
            return result;
        } else if (node.isNumber()) {
            // 全部转为 BigDecimal 保留数值一致性（不区分 0 vs 0.0）
            return OBJECT_MAPPER.getNodeFactory().numberNode(node.decimalValue());
        }
        return node;
    }

    /**
     * 合同展示详情
     *
     * @param contractId            合同ID
     * @param fillingBuildingRating 填充楼宇相关信息
     * @return 展示详情
     */
    public ContractDetailVO getContractDetail(Integer contractId, boolean fillingBuildingRating) {
        // 获取原合同详情信息
        ContractDetailVO contractDetailVO = getContractDetail(contractId, Map.of("buildingRating", fillingBuildingRating, "ledger", true, "subLedger", true));

        // 原合同附件typeName处理
        if (CollectionUtils.isNotEmpty(contractDetailVO.getAttachments())) {
            contractDetailVO.getAttachments().forEach(attachment -> {
                attachment.setTypeName(AttachmentTypeEnum.getDesc(attachment.getType()));
            });
        } else {
            contractDetailVO.setAttachments(List.of());
        }

        // 获取生效的变更合同记录
        List<ContractEntity> amendmentContracts = contractService.lambdaQuery()
                .eq(ContractEntity::getParentId, contractId)
                .eq(ContractEntity::getContractType, ContractTypeEnum.AMENDMENT.getCode())
                .eq(ContractEntity::getEffectFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).select(ContractEntity::getId).list();
        if (CollectionUtils.isEmpty(amendmentContracts)) {
            return contractDetailVO;
        }

        // 填充变更合同附件：变更（归档的文件，即已签章协议）的附件（在归档生效后展示）
        List<ContractAttachmentVO> amendmentAttachments = attachmentService.lambdaQuery()
                .in(ContractAttachmentEntity::getContractId, amendmentContracts.stream().map(ContractEntity::getId)
                        .toList()).eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_SEALED.getCode())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractAttachmentEntity::getBelongTo, BooleFlagEnum.YES.getCode()).list().stream()
                .map(contractConvert::toVO)
                .peek(vo -> vo.setTypeName("合同变更-" + AttachmentTypeEnum.getDesc(vo.getType()))).toList();
        if (CollectionUtils.isNotEmpty(amendmentAttachments)) {
            contractDetailVO.setAttachments(Stream.concat(Optional.ofNullable(contractDetailVO.getAttachments())
                    .orElse(List.of()).stream(), amendmentAttachments.stream()).toList());
        }

        return contractDetailVO;
    }

    /**
     * 合同展示详情
     *
     * @param contractId       合同ID
     * @param fillingOptionMap 填充选项
     * @return 展示详情
     */
    public ContractDetailVO getContractDetail(Integer contractId, Map<String, Boolean> fillingOptionMap) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        fillingOptionMap = Optional.ofNullable(fillingOptionMap).orElse(Map.of());

        // 转换合同详情
        ContractDetailVO detail = contractConvert.toDetailVO(contract);
        fillingAttachments(contract, null, detail::setAttachments);

        // 填充是否大屏
        detail.setLargeScreen(largeScreenHelper.isLargeScreen(contractId));

        // 填充所有扩展信息
        fillingContractDeposit(detail);
        fillingProjects(contract, detail::setProjects);
        fillingPriceApplies(contract, detail::getProjects);
        fillingDevices(contract, detail::getProjects);
        fillingPoints(contract, detail::getProjects);
        fillingMainContractPricePeriods(contract, detail::getProjects);
        fillingMainContractPaymentPeriods(contract, detail::getProjects, fillingOptionMap.getOrDefault("ledger", true));
        fillingMainContractSuppliers(contract, true, detail::setSuppliers);

        // 填充异常申请信息
        fillingAbnormal(detail);

        fillingSubContracts(contract, detail::getProjects);
        fillingSubContractPricePeriods(contract, detail::getProjects);
        fillingSubContractPaymentPeriods(contract, detail::getProjects, fillingOptionMap.getOrDefault("subLedger", true));
        fillingSubContractSuppliers(contract, true, detail::getProjects);

        // 填充合同变更信息
        if (Stream.of(ContractTypeEnum.NORMAL, ContractTypeEnum.CHANGE, ContractTypeEnum.HISTORY)
                .noneMatch(e -> e.getCode().equals(contract.getContractType()))) {
            fillingAmendments(contractId, detail::setTermination, detail::setChangeType, detail::setChangeTypeName, detail::setSubChangeType, detail::setSubChangeTypeName);
        }

        // 填充楼宇相关信息
        if (Boolean.TRUE.equals(fillingOptionMap.get("buildingRating"))) {
            fillingBuildingRating(contract, detail::getProjects);
        }

        // 设置城市水位价格
        Optional.ofNullable(contract.getCityId()).ifPresent(cityId -> {
            String configValue = configService.getConfigValue(String.valueOf(cityId));
            detail.setWatermarkPrice(StringUtils.isNotBlank(configValue) ? new BigDecimal(configValue) : BigDecimal.ZERO);
        });

        // 翻译有些特定信息
        translatorFactory.translate(Collections.singletonList(detail));
        translatorFactory.translate(detail.getProjects());

        return detail;
    }

    /**
     * 填充合同的押金信息
     */
    private void fillingContractDeposit(ContractDetailVO detail) {
        if (!BooleFlagEnum.isYes(detail.getDepositFlag())) {
            return;
        }
        List<ContractDepositSupplierEntity> depositSupplierEntities = iContractDepositSupplierService.lambdaQuery()
                .eq(ContractDepositSupplierEntity::getContractId, detail.getId())
                .eq(ContractDepositSupplierEntity::getSubContractId, VenueConstants.ZERO)
                .orderByDesc(ContractDepositSupplierEntity::getId).list();
        if (CollectionUtils.isEmpty(depositSupplierEntities)) {
            return;
        }
        ContractDepositSupplierEntity depositSupplierEntity = depositSupplierEntities.get(VenueConstants.ZERO);
        detail.setDepositId(depositSupplierEntity.getId());
        detail.setDepositSupplierId(depositSupplierEntity.getSupplierId());
        detail.setDepositSupplierName(depositSupplierEntity.getSupplierName());
        detail.setDepositSupplierAccountNo(depositSupplierEntity.getAccountNo());
        detail.setDepositSupplierBankId(depositSupplierEntity.getSupplierBankId());
    }

    /**
     * 填充合同变更信息
     *
     * @param contractId                合同ID
     * @param terminationConsumer       终止信息
     * @param changeTypeConsumer        一级变更类型
     * @param changeTypeNameConsumer    一级变更类型名称
     * @param subChangeTypeConsumer     二级变更类型
     * @param subChangeTypeNameConsumer 二级变更类型名称
     */
    private void fillingAmendments(Integer contractId, Consumer<ContractAmendmentTerminationVO> terminationConsumer, Consumer<String> changeTypeConsumer, Consumer<String> changeTypeNameConsumer, Consumer<List<String>> subChangeTypeConsumer, Consumer<List<String>> subChangeTypeNameConsumer) {
        // 查询变更记录
        List<ContractAmendmentChangeTypeEntity> entities = contractAmendmentChangeTypeService.lambdaQuery()
                .eq(ContractAmendmentChangeTypeEntity::getContractId, contractId)
                .eq(ContractAmendmentChangeTypeEntity::getDeleted, BooleFlagEnum.NO.getCode()).list();
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        // 补全“变更类型列表”
        List<ContractAmendmentChangeTypeVO> changeTypeVOS = entities.stream().map(contractConvert::toVO).toList();
        translatorFactory.translate(changeTypeVOS);
        List<String> subChangeTypes = Lists.newArrayListWithExpectedSize(entities.size());
        List<String> subChangeTypeNames = Lists.newArrayListWithExpectedSize(entities.size());
        changeTypeVOS.forEach(vo -> {
            changeTypeConsumer.accept(vo.getChangeType());
            changeTypeNameConsumer.accept(vo.getChangeTypeName());
            subChangeTypes.add(vo.getSubChangeType());
            subChangeTypeNames.add(vo.getSubChangeTypeName());
        });
        subChangeTypeConsumer.accept(subChangeTypes.stream().filter(StringUtils::isNotBlank).toList());
        subChangeTypeNameConsumer.accept(subChangeTypeNames.stream().filter(StringUtils::isNotBlank).toList());

        // 补全“终止信息”
        if (entities.stream().anyMatch(e -> ContractChangeTypeEnum.TERMINATE.getCode().equals(e.getChangeType()))) {
            terminationConsumer.accept(contractConvert.toDetailVO(contractAmendmentTerminationService.lambdaQuery()
                    .eq(ContractAmendmentTerminationEntity::getContractId, contractId)
                    .eq(ContractAmendmentTerminationEntity::getDeleted, BooleFlagEnum.NO.getCode()).last("LIMIT 1")
                    .one()));
        }
    }

    private void fillingAbnormal(ContractDetailVO detailVO) {
        List<ContractSimpleAbnormalVO> simpleAbnormalVOS = contractAbnormalService.getByContract(detailVO.getId());
        translatorFactory.translate(simpleAbnormalVOS);
        detailVO.setAbnormals(simpleAbnormalVOS);
    }

    /**
     * 历史合同展示详情
     *
     * @param contractId 合同ID
     * @return 展示详情
     */
    public ContractDetailVO getContractHistoricDetail(Integer contractId) {
        // 获取原始合同详情信息
        ContractDetailVO contractDetailVO = getContractDetail(contractId, Map.of("buildingRating", false, "ledger", true, "subLedger", true));

        // 原合同附件typeName处理
        if (CollectionUtils.isNotEmpty(contractDetailVO.getAttachments())) {
            contractDetailVO.getAttachments().forEach(attachment -> {
                attachment.setTypeName("原始合同-" + AttachmentTypeEnum.getDesc(attachment.getType()));
            });
        } else {
            contractDetailVO.setAttachments(List.of());
        }

        /* 填充补充协议信息 */
        List<ContractEntity> contractEntities = contractService.lambdaQuery()
                .eq(ContractEntity::getParentId, contractId)
                .eq(ContractEntity::getContractType, ContractTypeEnum.AGREEMENT.getCode())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .ne(ContractEntity::getApplyStatus, ContractApplyStatusEnum.DRAFT.getCode())
                .select(ContractEntity::getId, ContractEntity::getApplyCode, ContractEntity::getArchiveFlag, ContractEntity::getEffectFlag)
                .list();
        if (CollectionUtils.isEmpty(contractEntities)) {
            return contractDetailVO;
        }
        contractDetailVO.setSupplements(contractEntities.stream().map(contractConvert::toDetailSimplifyVO).toList());

        // 填充已生效补充协议的归档附件信息 - 归档附件：指的是双章附件
        List<Integer> archivedAgreementIds = contractEntities.stream()
                .filter(e -> BooleFlagEnum.isYes(e.getArchiveFlag()) && BooleFlagEnum.isYes(e.getEffectFlag()))
                .map(ContractEntity::getId).toList();
        if (CollectionUtils.isEmpty(archivedAgreementIds)) {
            return contractDetailVO;
        }
        // 获取已生效补充协议的归档附件
        List<ContractAttachmentEntity> entities = attachmentService.lambdaQuery()
                .in(ContractAttachmentEntity::getContractId, archivedAgreementIds)
                .eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_SEALED.getCode())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractAttachmentEntity::getBelongTo, BooleFlagEnum.YES.getCode()).list().stream()
                .sorted(Comparator.comparing(ContractAttachmentEntity::getCreateTime)).toList();
        if (CollectionUtils.isEmpty(entities)) {
            return contractDetailVO;
        }
        List<ContractAttachmentVO> attachments = Lists.newArrayListWithCapacity(contractDetailVO.getAttachments()
                .size() + entities.size());
        if (CollectionUtils.isNotEmpty(contractDetailVO.getAttachments())) {
            attachments.addAll(contractDetailVO.getAttachments());
        }
        // 补充协议附件typeName处理
        attachments.addAll(entities.stream().map(contractConvert::toVO)
                .peek(vo -> vo.setTypeName("补充协议-" + AttachmentTypeEnum.getDesc(vo.getType()))).toList());

        contractDetailVO.setAttachments(attachments);
        return contractDetailVO;
    }

    /**
     * 历史合同申请单信息
     */
    public ContractEditDetailVO getHistoricApplyDetail(Integer contractId, boolean force, boolean agreement) {
        // 获取原始合同申请单信息
        ContractEditDetailVO applyDetailVO = getApplyDetail(contractId, force, Sets.newHashSet(AttachmentTypeEnum.MAIN_SEAL_PENDING.getCode(), AttachmentTypeEnum.MAIN_REFERENCE.getCode(), AttachmentTypeEnum.SUB_REFERENCE.getCode(), AttachmentTypeEnum.FEISHU_CONTRACT.getCode(), AttachmentTypeEnum.SUB_SEAL_PENDING.getCode()), Map.of("ledger", true, "subLedger", true, "planPaymentDateOrder", false));

        // 发起补充时：不返回原合附件信息
        if (agreement) {
            applyDetailVO.setAttachments(List.of());
            return applyDetailVO;
        }

        // 查询contractId下所有的附件
        List<ContractAttachmentVO> attachmentVOS = attachmentService.lambdaQuery()
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractAttachmentEntity::getBelongTo, BooleFlagEnum.YES.getCode()).list().stream()
                .map(contractConvert::toVO).peek(attachment -> {
                    attachment.setTypeName("原始合同-" + AttachmentTypeEnum.getDesc(attachment.getType()));
                }).toList();
        if (CollectionUtils.isNotEmpty(attachmentVOS)) {
            applyDetailVO.setAttachments(attachmentVOS);
        }

        return applyDetailVO;
    }


    /**
     * 合同列表(分页)
     */
    public PageResponseVO<ContractPageVO> pageListContacts(PageRequestVO<ContractQueryParam> pageRequest) {
        return pageListContacts(BooleFlagEnum.YES, null, pageRequest);
    }

    /**
     * 合同  相关字段模糊查询  并返回前十条
     */
    public PageResponseVO<CodeNameVO> listFiled(String tableName, String query, String filed, Integer pageSize, Integer pageNumber) {
        return contractService.listFiled(tableName, query, filed, pageSize, pageNumber);
    }


    /**
     * 根据商机编码查询合同列表
     *
     * @param businessCodes 商机编码集合
     */
    public List<ContractPointVO> listContactByBusinessCodes(Collection<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return Collections.emptyList();
        }

        // 根据商机编码从Project
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId, ContractProjectEntity::getContractId, ContractProjectEntity::getProjectCode)
                .in(ContractProjectEntity::getProjectCode, businessCodes).list();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }

        // 查询合同信息
        Map<Integer, String> contractBusinessMapping = projects.stream()
                .collect(Collectors.toMap(ContractProjectEntity::getContractId, ContractProjectEntity::getProjectCode, (o, n) -> n));
        Set<Integer> contractIds = contractBusinessMapping.keySet();

        log.info("通过商机编码({})查询到合同({})", StringUtils.join(businessCodes, ","), StringUtils.join(contractIds, ","));
        List<ContractEntity> contracts = contractService.lambdaQuery()
                .select(ContractEntity::getId, ContractEntity::getContractCode, ContractEntity::getCityId, ContractEntity::getContractType)
                .eq(ContractEntity::getFormalFlag, BooleFlagEnum.YES.getCode()).in(ContractEntity::getId, contractIds)
                .in(ContractEntity::getFormalStatus, ContractStatusEnum.WAIT_EXECUTE.getCode(), ContractStatusEnum.EXECUTING.getCode())
                .in(ContractEntity::getContractType, ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode(), ContractTypeEnum.HISTORY.getCode())
                .list();
        if (CollectionUtils.isEmpty(contracts)) {
            return Collections.emptyList();
        }


        // 查询项目信息
        Set<Integer> projectIds = projects.stream().map(ContractProjectEntity::getId).collect(Collectors.toSet());
        log.info("通过商机编码({})查询到项目({})", StringUtils.join(businessCodes, ","), StringUtils.join(projectIds, ","));
        Map<Integer, List<ContractDevicePointEntity>> contractPointMapping = devicePointService.lambdaQuery()
                .select(ContractDevicePointEntity::getContractId, ContractDevicePointEntity::getCode, ContractDevicePointEntity::getName)
                .in(ContractDevicePointEntity::getContractId, contractIds)
                .in(ContractDevicePointEntity::getProjectId, projectIds).list().stream()
                .collect(Collectors.groupingBy(ContractDevicePointEntity::getContractId));

        List<ContractPointVO> contractPoints = contracts.stream()
                .filter(contract -> contractPointMapping.containsKey(contract.getId())).map(contract -> {
                    List<ContractDevicePointEntity> points = contractPointMapping.get(contract.getId());
                    log.info("通过合同({})查询到点位({})", contract.getId(), points.stream()
                            .map(ContractDevicePointEntity::getCode).collect(Collectors.joining(", ")));
                    return new ContractPointVO().setId(contract.getId()).setContractCode(contract.getContractCode())
                            .setBusinessCode(contractBusinessMapping.get(contract.getId()))
                            .setCityId(contract.getCityId())
                            .setOldFlag(Objects.equals(ContractTypeEnum.HISTORY.getCode(), contract.getContractType()) ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode())
                            .setPoints(points.stream().map(point -> {
                                ContractDevicePointDetailVO pointVo = new ContractDevicePointDetailVO();
                                pointVo.setCode(point.getCode());
                                pointVo.setName(point.getName());
                                return pointVo;
                            }).toList());
                }).toList();

        // 翻译城市名称
        translatorFactory.translate(contractPoints);
        return contractPoints;
    }

    /**
     * 获取提交过合同的商机编码
     *
     * @param businessCodes 商机编码
     * @return 已签约合同的商机编码集合
     */
    public List<String> getSignedBusinessCodes(Collection<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return Collections.emptyList();
        }

        // 根据商机编码从Project查询合同ID
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId, ContractProjectEntity::getContractId, ContractProjectEntity::getProjectCode)
                .in(ContractProjectEntity::getProjectCode, businessCodes).list();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }

        // 查询合同信息
        Map<Integer, List<String>> contractBusinessCodeMapping = projects.stream()
                .collect(Collectors.groupingBy(ContractProjectEntity::getContractId, Collectors.mapping(ContractProjectEntity::getProjectCode, Collectors.toList())));

        log.info("通过商机编码({})查询到的合同({})", StringUtils.join(businessCodes, ","), StringUtils.join(contractBusinessCodeMapping.keySet(), ","));

        // 申请时间不是数据库默认值的都认为提交过合同
        Set<Integer> existedContractIds = contractService.lambdaQuery().select(ContractEntity::getId)
                .in(ContractEntity::getId, contractBusinessCodeMapping.keySet())
                .ne(ContractEntity::getApplyTime, LocalDateTime.of(1900, 1, 1, 0, 0, 0)).list().stream()
                .map(ContractEntity::getId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(existedContractIds)) {
            return Collections.emptyList();
        }

        // 返回找到提交过合同的商机编码
        return existedContractIds.stream().flatMap(contractId -> contractBusinessCodeMapping.get(contractId).stream())
                .toList();
    }

    /**
     * 获取有履约中合同的商机编码
     *
     * @param businessCodes 商机编码
     * @return 有履约中合同的商机编码
     */
    public List<String> getSigningBusinessCodes(Set<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return Collections.emptyList();
        }

        // 根据商机编码从Project查询合同ID
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId, ContractProjectEntity::getContractId, ContractProjectEntity::getProjectCode)
                .in(ContractProjectEntity::getProjectCode, businessCodes).list();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }

        // 查询合同信息
        Map<Integer, List<String>> contractBusinessCodeMapping = projects.stream()
                .collect(Collectors.groupingBy(ContractProjectEntity::getContractId, Collectors.mapping(ContractProjectEntity::getProjectCode, Collectors.toList())));

        log.info("通过商机编码({})查询到合同({})", StringUtils.join(businessCodes, ","), StringUtils.join(contractBusinessCodeMapping.keySet(), ","));

        // 正式合同合同状态为待归档，正式，待执行，执行中的为履约中的合同
        List<String> validStatus = List.of(ContractStatusEnum.PENDING.getCode(), ContractStatusEnum.FORMAL.getCode(), ContractStatusEnum.WAIT_EXECUTE.getCode(), ContractStatusEnum.EXECUTING.getCode());
        Set<Integer> existedContractIds = contractService.lambdaQuery().select(ContractEntity::getId)
                .in(ContractEntity::getId, contractBusinessCodeMapping.keySet())
                .eq(ContractEntity::getFormalFlag, BooleFlagEnum.YES.getCode())
                .in(ContractEntity::getFormalStatus, validStatus).list().stream().map(ContractEntity::getId)
                .collect(Collectors.toSet());

        // 合同申请单申请状态为提交审批，审核中，审核通过的为履约中的合同
        List<String> validApplyStatus = List.of(ContractApplyStatusEnum.SUBMIT.getCode(), ContractApplyStatusEnum.APPROVING.getCode(), ContractApplyStatusEnum.APPROVED.getCode());
        existedContractIds.addAll(contractService.lambdaQuery().select(ContractEntity::getId)
                .in(ContractEntity::getId, contractBusinessCodeMapping.keySet())
                .eq(ContractEntity::getFormalFlag, BooleFlagEnum.NO.getCode())
                .in(ContractEntity::getApplyStatus, validApplyStatus).list().stream().map(ContractEntity::getId)
                .collect(Collectors.toSet()));

        if (CollectionUtils.isEmpty(existedContractIds)) {
            return Collections.emptyList();
        }

        // 返回找到履约中合同的商机编码
        return existedContractIds.stream().flatMap(contractId -> contractBusinessCodeMapping.get(contractId).stream())
                .toList();
    }

    /**
     * 合同或申请单列表(分页)
     */
    public PageResponseVO<ContractPageVO> pageListContacts(BooleFlagEnum formalFlag, Boolean agentAudit, PageRequestVO<ContractQueryParam> pageRequest) {
        // 分页查询合同列表，自定义统计SQL
        Page<ContractPageDTO> page = new Page<>(Optional.ofNullable(pageRequest.getCurrentPage())
                .orElse(1L), Optional.ofNullable(pageRequest.getPageSize()).orElse(10));
        page.setCountId("pageListContracts_COUNT");
        List<OrderItem> orderItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageRequest.getAscColumnNames())) {
            for (String ascColumnName : pageRequest.getAscColumnNames()) {
                OrderItem orderItem = new OrderItem();
                orderItem.setAsc(Boolean.TRUE);
                orderItem.setColumn(ascColumnName);
                orderItems.add(orderItem);
            }
        }

        if (CollectionUtils.isNotEmpty(pageRequest.getDescColumnNames())) {
            for (String descColumnName : pageRequest.getDescColumnNames()) {
                OrderItem orderItem = new OrderItem();
                orderItem.setAsc(Boolean.FALSE);
                orderItem.setColumn(descColumnName);
                orderItems.add(orderItem);
            }
        }

        if (CollectionUtils.isNotEmpty(orderItems)) {
            page.setOrders(orderItems);
        }

        // 转换查询参数
        ContractQueryDTO queryDto = toQueryDto(pageRequest.getQuery(), agentAudit);
        Optional.ofNullable(formalFlag).ifPresent(flag -> queryDto.setFormalFlag(flag.getCode()));

        // 代理商审核
        if (Optional.ofNullable(agentAudit).orElse(false)) {
            queryDto.setUserIds(Collections.emptyList());
            queryDto.setFormalFlag(BooleFlagEnum.NO.getCode());
            queryDto.setApplyStatus(Collections.singletonList(ContractApplyStatusEnum.PRE_APPROVING.getCode()));
            queryDto.setAgentApprover(UserThreadLocal.getUser().getWno());
        }

        // 分页查询
        log.info("分页查询合同列表参数, queryDto: {}", queryDto);
        IPage<ContractPageDTO> pagedContracts = contractService.pageListContracts(page, queryDto);
        PageResponseVO<ContractPageVO> pageResponse = new PageResponseVO<>();
        pageResponse.setCurrentPage(pagedContracts.getCurrent());
        pageResponse.setTotalRows(pagedContracts.getTotal());
        pageResponse.setTotal(pagedContracts.getTotal());
        pageResponse.setPageSize(pagedContracts.getSize());
        pageResponse.setTotalPages(pagedContracts.getPages());
        log.info("分页查询合同列表结果, pageResponse: {}", pageResponse);
        if (CollectionUtils.isNotEmpty(pagedContracts.getRecords())) {
            // 填充扩展信息
            pageResponse.setRows(toVos(pagedContracts.getRecords()));

            // 有效合同需要返回子合同状态,检查合同是否有子合同
            Set<Integer> contractIds = pageResponse.getRows().stream().map(ContractPageVO::getId)
                    .collect(Collectors.toSet());
            Set<Integer> hasSubContractIds = subContractService.lambdaQuery().select(ContractSubEntity::getContractId)
                    .in(ContractSubEntity::getContractId, contractIds).list().stream()
                    .map(ContractSubEntity::getContractId).collect(Collectors.toSet());

            // 检查合同是否有押金
            Set<Integer> hasDepositContractIds = projectService.lambdaQuery()
                    .in(ContractProjectEntity::getContractId, contractIds)
                    .gt(ContractProjectEntity::getDepositAmount, BigDecimal.ZERO)
                    .select(ContractProjectEntity::getContractId, ContractProjectEntity::getDepositAmount).list()
                    .stream().map(ContractProjectEntity::getContractId).collect(Collectors.toSet());

            // 检查合同是否是大屏合同
            Set<Integer> largeScreenContractIds = largeScreenHelper.getLargeScreenContracts(contractIds);

            pageResponse.getRows().forEach(contract -> {
                contract.setHasSubContract(hasSubContractIds.contains(contract.getId()));
                contract.setHasDeposit(hasDepositContractIds.contains(contract.getId()));
                contract.setLargeScreen(largeScreenContractIds.contains(contract.getId()));
            });


            // 处理申请时间
            pageResponse.getRows().forEach(contract -> {
                // 比较申请时间是否小于"2000-01-01 00:00:00"
                if (contract.getApplyTime()
                        .isBefore(LocalDateTime.parse("2000-01-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))) {
                    contract.setApplyTime(null);
                }
            });
        }

        return pageResponse;
    }


    /**
     * 转换成VO列表
     */
    private List<ContractPageVO> toVos(List<ContractPageDTO> contracts) {
        if (CollectionUtils.isEmpty(contracts)) {
            return Collections.emptyList();
        }

        // 提取合同ID
        Set<Integer> contractIds = contracts.stream().map(ContractPageDTO::getId).collect(Collectors.toSet());

        // 查询供应商信息
        Map<Integer, List<Integer>> contractSupplierMap = contractSupplierService.lambdaQuery()
                .select(ContractSupplierEntity::getContractId, ContractSupplierEntity::getSupplierId)
                .in(ContractSupplierEntity::getContractId, contractIds).eq(ContractSupplierEntity::getSubContractId, 0)
                .list().stream()
                .collect(Collectors.groupingBy(ContractSupplierEntity::getContractId, Collectors.mapping(ContractSupplierEntity::getSupplierId, Collectors.toList())));


        // 转换成VO
        List<ContractPageVO> vos = contracts.stream().map(contractConvert::toVO).peek(vo -> {
            List<Integer> supplierIds = contractSupplierMap.getOrDefault(vo.getId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(supplierIds)) vo.setSupplier1(supplierIds.get(0));
            if (supplierIds.size() > 1) vo.setSupplier2(supplierIds.get(1));
            vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
            vo.setApplyStatusName(ContractApplyStatusEnum.getDesc(vo.getApplyStatus()));
            vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatus()));

            // 查找签约数
            int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                    .eq(ContractDeviceEntity::getContractId, vo.getId()).list().stream()
                    .mapToInt(ContractDeviceEntity::getSignCount).sum();
            vo.setSignCount(signCount);
            vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));

        }).toList();


        // 填充字典、用户信息
        contractFillingService.fillingProject(vos);
        contractFillingService.fillingSupplier(vos);
        contractFillingService.fillingCity(vos);
        translatorFactory.translate(vos);

        return vos;
    }

    /**
     * 填充附件
     */
    private void fillingAttachments(ContractEntity contract, Collection<Integer> attachmentTypes, Consumer<List<ContractAttachmentVO>> attachmentConsumer) {
        List<ContractAttachmentEntity> entities = attachmentService.lambdaQuery()
                .eq(ContractAttachmentEntity::getContractId, contract.getId())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractAttachmentEntity::getBelongTo, AttachmentBelongEnum.CONTRACT.getCode())
                .in(CollectionUtils.isNotEmpty(attachmentTypes), ContractAttachmentEntity::getType, attachmentTypes)
                .list();
        if (CollectionUtils.isEmpty(entities)) return;

        attachmentConsumer.accept(entities.stream().map(contractConvert::toVO)
                .peek(vo -> vo.setTypeName(AttachmentTypeEnum.getDesc(vo.getType()))).toList());
    }

    /**
     * 填充主合同押金数据
     */
    private void fillingMainContractDeposit(ContractEditDetailVO detail) {
        // 有押金，需要填充押金收款方
        ContractDepositSupplierEntity depositSupplier = depositSupplierService.lambdaQuery()
                .eq(ContractDepositSupplierEntity::getContractId, detail.getId())
                .eq(ContractDepositSupplierEntity::getSubContractId, 0).one();
        if (Objects.isNull(depositSupplier)) {
            return;
        }
        //
        detail.setDepositId(depositSupplier.getId());
        detail.setDepositSupplierId(depositSupplier.getSupplierId());
        detail.setDepositSupplierName(depositSupplier.getSupplierName());
        detail.setDepositSupplierAccountNo(depositSupplier.getAccountNo());
        detail.setDepositSupplierBankId(depositSupplier.getSupplierBankId());
    }


    /**
     * 填充项目信息
     */
    private void fillingProjects(ContractEntity contract, Consumer<List<ContractProjectDetailVO>> projectConsumer) {
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .eq(ContractProjectEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(projects)) return;

        // 项目列表
        List<ContractProjectDetailVO> projectVos = projects.stream().map(contractConvert::toEditDetailVO).toList();

        List<String> buildingNos = projectVos.stream().filter(item -> StringUtils.isNotEmpty(item.getProjectCode()))
                .map(e -> e.getProjectCode().split("-")[0]).distinct().toList();
        Result<List<BuildingGeneVO>> result = feignMethH5Rpc.buildingGeneByNo(buildingNos);
        Map<String, BigDecimal> buildingFinalCoefficientMap = new HashMap<>();
        if (result.getCode() == HttpStatus.OK.value() || CollectionUtils.isNotEmpty(result.getData())) {
            buildingFinalCoefficientMap = result.getData().stream()
                    // valueMapper不能为null
                    .filter(e -> Objects.nonNull(e.getFinalCoefficient()))
                    .collect(Collectors.toMap(BuildingGeneVO::getBuildingRatingNo, BuildingGeneVO::getFinalCoefficient));
        }

        // 翻译物业类型名称及获取大屏系数
        for (ContractProjectDetailVO project : projectVos) {
            project.setPropertyTypeName(PropertyTypeEnum.getDesc(project.getPropertyType()));
            project.setPaymentTypeName(PaymentTypeEnum.getDesc(project.getPaymentType()));
            project.setLargeScreenCoefficient(buildingFinalCoefficientMap.get(project.getProjectCode().split("-")[0]));
        }

        // // 有押金，需要填充押金收款方
        // Map<Integer, ContractDepositSupplierEntity> depositSupplierEntityMap = depositSupplierService.lambdaQuery()
        //         .eq(ContractDepositSupplierEntity::getContractId, contract.getId()).list().stream()
        //         .collect(Collectors.toMap(ContractDepositSupplierEntity::getProjectId, depositSupplier -> depositSupplier));
        //
        // projectVos.stream().filter(project -> BooleFlagEnum.isYes(project.getDepositFlag())).forEach(project -> {
        //     ContractDepositSupplierEntity depositSupplierEntity = depositSupplierEntityMap.get(project.getId());
        //     if (Objects.isNull(depositSupplierEntity)) {
        //         return;
        //     }
        //     project.setDepositId(depositSupplierEntity.getId());
        //     project.setDepositSupplierId(depositSupplierEntity.getSupplierId());
        //     project.setDepositSupplierName(depositSupplierEntity.getSupplierName());
        //     project.setDepositSupplierAccountNo(depositSupplierEntity.getAccountNo());
        //     project.setDepositSupplierBankId(depositSupplierEntity.getSupplierBankId());
        // });

        // 填充到返回结果上
        projectConsumer.accept(projectVos);
    }

    /**
     * 填充价格申请信息
     */
    private void fillingPriceApplies(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查询项目下价格申请，按项目分组
        Map<Integer, List<ContractPriceApplyEntity>> priceAppliyMap = priceApplyService.lambdaQuery()
                .eq(ContractPriceApplyEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractPriceApplyEntity::getProjectId));
        if (MapUtils.isEmpty(priceAppliyMap)) return;

        // 填充价格申请
        for (ContractProjectDetailVO project : projects) {
            List<ContractPriceApplyEntity> priceApplies = priceAppliyMap.getOrDefault(project.getId(), Collections.emptyList());
            project.setPriceApplies(priceApplies.stream().map(contractConvert::toEditDetailVO).toList());
        }
    }

    /**
     * 填充终端信息
     */
    private void fillingDevices(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 找出项目下所有价格申请
        List<ContractPriceApplyDetailVO> priceApplies = projects.stream().map(ContractProjectDetailVO::getPriceApplies)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(priceApplies)) return;

        // 查询项目下终端，按项目、价格申请分组
        Map<Integer, List<ContractDeviceEntity>> deviceMap = deviceService.lambdaQuery()
                .eq(ContractDeviceEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractDeviceEntity::getPriceApplyId));
        if (MapUtils.isEmpty(deviceMap)) return;

        // 为价格申请生成终端信息
        for (ContractPriceApplyDetailVO priceApply : priceApplies) {
            priceApply.setDevices(deviceMap.getOrDefault(priceApply.getId(), Collections.emptyList()).stream()
                    .map(contractConvert::toEditDetailVO).toList());
        }

        // 翻译终端类型名称
        List<ContractDeviceDetailVO> devices = projects.stream().map(ContractProjectDetailVO::getPriceApplies)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .map(ContractPriceApplyDetailVO::getDevices).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).toList();
        translatorFactory.translate(devices);
    }

    /**
     * 填充终端位置信息
     */
    private void fillingPoints(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;
        Integer cityId = contract.getCityId();

        // 找到所有终端信息
        List<ContractDeviceDetailVO> devices = projects.stream()
                .flatMap(project -> project.getPriceApplies().stream().filter(Objects::nonNull)
                        .flatMap(apply -> apply.getDevices().stream()
                                .peek(device -> device.setProjectCode(project.getProjectCode()))))
                .filter(Objects::nonNull).toList();

        if (CollectionUtils.isEmpty(devices)) return;

        // 查询终端点位，按终端分组
        Map<Integer, List<ContractDevicePointEntity>> deviceLocationMap = devicePointService.lambdaQuery()
                .eq(ContractDevicePointEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractDevicePointEntity::getDeviceId));

        // 翻译终端位置名称
        ConfigVO config = configService.getConfig(cityId + "");

        // 为终端生成位置信息
        for (ContractDeviceDetailVO device : devices) {

            Boolean isCoreLocation = isCoreLocation(device.getProjectCode());

            List<ContractDevicePointEntity> points = deviceLocationMap.getOrDefault(device.getId(), Collections.emptyList());

            Set<String> sizeSet = points.stream().map(ContractDevicePointEntity::getSize).collect(Collectors.toSet());

            device.setPoints(points.stream().map(contractConvert::toEditDetailVO).toList());
            device.setCheckedPointCodes(points.stream().map(ContractDevicePointEntity::getCode).toList());

            if (config != null) {
                if (sizeSet.stream().anyMatch(item -> !largeScreenSizeCodes.contains(item))) {
                    // 小屏
                    device.setWaterMarkPriceForSmall(config.getValue() + "元");
                }
                if (sizeSet.stream().anyMatch(largeScreenSizeCodes::contains) && isCoreLocation != null) {
                    // 大屏
                    String price = isCoreLocation ? config.getExt1() + "元(核心)" : config.getExt2() + "元(非核心)";
                    device.setWaterMarkPriceForBig(price);
                }
            }
        }
    }

    private Boolean isCoreLocation(String buildingNo) {
        if (StringUtils.isBlank(buildingNo)) {
            return null;
        }
        CmsResult<BuildingVO> result = feignMethH5Rpc.getBuildingLocation(buildingNo.split("-")[0]);
        if (result == null) {
            return null;
        }
        BuildingVO vo = result.getData();
        if (vo == null) {
            return null;
        }
        String buildingLocation = vo.getBuildingLocation();
        String aiBuildingLocation = vo.getAiBuildingLocation();
        if (StringUtils.isNotBlank(buildingLocation)) {
            return !buildingLocation.contains("非");
        } else if (StringUtils.isNotBlank(aiBuildingLocation)) {
            return !aiBuildingLocation.contains("非");
        }
        return null;
    }

    /**
     * 填充单价周期信息
     */
    private void fillingMainContractPricePeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 找到所有终端信息
        List<ContractDeviceDetailVO> devices = projects.stream().map(ContractProjectDetailVO::getPriceApplies)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .map(ContractPriceApplyDetailVO::getDevices).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(devices)) return;


        // 按终端ID分组
        Map<Integer, List<ContractPricePeriodEntity>> pricePeriodMap = pricePeriodService.lambdaQuery()
                .eq(ContractPricePeriodEntity::getContractId, contract.getId())
                .gt(ContractPricePeriodEntity::getDeviceId, 0).list().stream()
                .collect(Collectors.groupingBy(ContractPricePeriodEntity::getDeviceId));

        // 为终端生成单价周期
        for (ContractDeviceDetailVO device : devices) {
            List<ContractPricePeriodEntity> pricePeriodEntities = pricePeriodMap.getOrDefault(device.getId(), Collections.emptyList());
            List<ContractPricePeriodDetailVO> priceDetails = pricePeriodEntities.stream()
                    .map(contractConvert::toEditDetailVO).toList();
            device.setPricePeriods(priceDetails);
        }
    }

    /**
     * 根据合同类型匹配付款周期的台账数据
     */
    private List<ContractPaymentPeriodDetailVO> paymentPeriodWithLedgerAdapter(ContractEntity contract, List<ContractPaymentPeriodEntity> paymentPeriods, Map<Integer, LedgerEntity> ledgerEntityMap, boolean planPaymentDateOrder) {
        if (CollectionUtils.isEmpty(paymentPeriods)) {
            return null;
        }
        // 中间状态
        Stream<ContractPaymentPeriodDetailVO> transitionStream = paymentPeriods.stream().map(e -> {
            ContractPaymentPeriodDetailVO vo = contractConvert.toEditDetailVO(e);
            Integer key = CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId();
            vo.setPaidAmount(ledgerEntityMap.containsKey(key) ? ledgerEntityMap.get(key)
                    .getPaidAmount() : BigDecimal.ZERO);
            return vo;
        });
        return planPaymentDateOrder ? transitionStream.sorted(Comparator.comparing(ContractPaymentPeriodDetailVO::getPlanPaymentDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList() : transitionStream.sorted(Comparator.comparing(ContractPaymentPeriodDetailVO::getStartDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
    }


    /**
     * 填充付款周期信息
     */
    private void fillingMainContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        fillingMainContractPaymentPeriods(contract, projectSupplier, false);
    }

    private void fillingMainContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger) {
        fillingMainContractPaymentPeriods(contract, projectSupplier, fillingLedger, true);
    }

    private void fillingMainContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger, boolean planPaymentDateOrder) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 找到所有终端信息
        List<ContractDeviceDetailVO> devices = projects.stream().map(ContractProjectDetailVO::getPriceApplies)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .map(ContractPriceApplyDetailVO::getDevices).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }

        // 按终端ID分组
        Map<Integer, List<ContractPaymentPeriodEntity>> paymentPeriodMap = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getContractId, contract.getId())
                .gt(ContractPaymentPeriodEntity::getDeviceId, 0)
                .orderByAsc(ContractPaymentPeriodEntity::getPlanPaymentDate, ContractPaymentPeriodEntity::getId).list()
                .stream().collect(Collectors.groupingBy(ContractPaymentPeriodEntity::getDeviceId));

        // 获取台账数据
        Map<Integer, LedgerEntity> ledgerEntityMap = getLedgerOfPaymentPeriod(paymentPeriodMap.values().stream()
                .flatMap(Collection::stream)
                .map(e -> CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId())
                .collect(Collectors.toSet()), fillingLedger);

        // 为终端生成单价周期
        for (ContractDeviceDetailVO device : devices) {
            device.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, paymentPeriodMap.getOrDefault(device.getId(), Collections.emptyList()), ledgerEntityMap, planPaymentDateOrder));
        }
    }

    /**
     * 获取台账数据
     */
    private Map<Integer, LedgerEntity> getLedgerOfPaymentPeriod(Set<Integer> paymentPeriodIds, boolean fillingLedger) {
        if (Boolean.FALSE.equals(fillingLedger) || CollectionUtils.isEmpty(paymentPeriodIds)) {
            return Map.of();
        }
        //
        return ledgerService.lambdaQuery().in(LedgerEntity::getPaymentPeriodId, paymentPeriodIds)
                .select(LedgerEntity::getPaymentPeriodId, LedgerEntity::getPaidAmount).list().stream()
                .filter(e -> !Objects.equals(e.getPaymentPeriodId(), 0))
                .collect(Collectors.toMap(LedgerEntity::getPaymentPeriodId, Function.identity(), (o, n) -> n));
    }


    /**
     * 填充主合同的供应商信息
     */
    private void fillingMainContractSuppliers(ContractEntity contract, boolean expand, Consumer<List<ContractSupplierDetailVO>> supplierConsumer) {
        // 查询合同-供应商
        List<ContractSupplierEntity> suppliers = contractSupplierService.lambdaQuery()
                .eq(ContractSupplierEntity::getSubContractId, 0)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(suppliers)) {
            return;
        }

        // 查询合同-供应商-银行信息
        Map<Integer, List<ContractSupplierBankEntity>> contractSupplierBankMap = contractSupplierBankService.lambdaQuery()
                .eq(ContractSupplierBankEntity::getSubContractId, 0)
                .eq(ContractSupplierBankEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractSupplierBankEntity::getSupplierId, Collectors.toList()));

        // 返回合同的供应商信息
        supplierConsumer.accept(getContractSuppliers(expand, suppliers, contractSupplierBankMap));
    }

    /**
     * 获取合同供应商信息
     */
    public List<ContractSupplierDetailVO> getContractSuppliers(boolean expand, List<ContractSupplierEntity> suppliers, Map<Integer, List<ContractSupplierBankEntity>> contractSupplierBankMap) {
        if (CollectionUtils.isEmpty(suppliers) || MapUtils.isEmpty(contractSupplierBankMap)) {
            return Collections.emptyList();
        }

        return suppliers.stream().map(supplier -> {
            ContractSupplierDetailVO vo = contractConvert.toEditDetailVO(supplier);
            List<ContractSupplierBankEntity> contractSupplierBankEntities = contractSupplierBankMap.get(supplier.getSupplierId());
            if (CollectionUtils.isNotEmpty(contractSupplierBankEntities)) {
                if (expand) {
                    vo.setBanks(contractSupplierBankEntities.stream().map(item -> {
                        ContractSupplierBankDetailVO bankVo = new ContractSupplierBankDetailVO();
                        bankVo.setId(item.getId());
                        bankVo.setBankName(item.getBankName());
                        bankVo.setAccountName(item.getAccountName());
                        bankVo.setAccountCode(item.getBankAccountCode());
                        return bankVo;
                    }).toList());
                } else {
                    vo.setBankAccountCodes(contractSupplierBankEntities.stream()
                            .map(e -> SecurityUtils.encryptToFront(e.getBankAccountCode()))
                            .filter(StringUtils::isNotBlank).toList());
                }
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 填充单价周期信息
     */
    private void fillingSubContractPricePeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查找子合同
        List<SubContractDetailVO> subContracts = projects.stream().map(ContractProjectDetailVO::getSubContracts)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(subContracts)) return;

        // 子合同, key: 子合同ID, val: feeType -> periods
        Map<Integer, Map<String, List<ContractPricePeriodEntity>>> pricePeriodMap = pricePeriodService.lambdaQuery()
                .eq(ContractPricePeriodEntity::getContractId, contract.getId())
                .gt(ContractPricePeriodEntity::getSubContractId, 0).list().stream()
                .collect(Collectors.groupingBy(ContractPricePeriodEntity::getSubContractId, Collectors.groupingBy(ContractPricePeriodEntity::getFeeType)));
        if (MapUtils.isEmpty(pricePeriodMap)) return;

        for (SubContractDetailVO subContract : subContracts) {
            Map<String, List<ContractPricePeriodEntity>> periodMap = pricePeriodMap.get(subContract.getId());
            if (MapUtils.isEmpty(periodMap)) continue;

            // 分成有费用对象和没有两种情况分别处理，
            if (CollectionUtils.isEmpty(subContract.getFees())) {
                subContract.setFees(Lists.newArrayList());
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = new SubContractFeeDetailVO();
                    feeVo.setFeeType(feeType);
                    feeVo.setInvoiceType(CollectionUtils.isEmpty(periods) ? "" : periods.get(VenueConstants.ZERO)
                            .getInvoiceType());
                    feeVo.setTaxPoint(CollectionUtils.isEmpty(periods) ? "" : periods.get(VenueConstants.ZERO)
                            .getTaxPoint());
                    feeVo.setPricePeriods(periods.stream().map(contractConvert::toEditDetailVO).toList());
                    subContract.getFees().add(feeVo);
                });
            } else {
                Map<String, SubContractFeeDetailVO> feeMap = subContract.getFees().stream()
                        .collect(Collectors.toMap(SubContractFeeDetailVO::getFeeType, Function.identity(), (o, n) -> n));
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = feeMap.get(feeType);
                    if (Objects.isNull(feeVo)) {
                        feeVo = new SubContractFeeDetailVO();
                        feeVo.setFeeType(feeType);
                        subContract.getFees().add(feeVo);
                    }
                    feeVo.setPricePeriods(periods.stream().map(contractConvert::toEditDetailVO).toList());
                });
            }
            // 按字典排序，方便每次看到数据都是一样的顺序
            subContract.getFees().sort(Comparator.comparing(SubContractFeeDetailVO::getFeeType));
        }
    }


    /**
     * 填充付款周期信息
     */
    private void fillingSubContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        fillingSubContractPaymentPeriods(contract, projectSupplier, false);
    }

    private void fillingSubContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger) {
        fillingSubContractPaymentPeriods(contract, projectSupplier, fillingLedger, true);
    }

    private void fillingSubContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger, boolean planPaymentDateOrder) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查找子合同
        List<SubContractDetailVO> subContracts = projects.stream().map(ContractProjectDetailVO::getSubContracts)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(subContracts)) return;


        // 子合同, key: 子合同ID, val: feeType -> periods
        Map<Integer, Map<String, List<ContractPaymentPeriodEntity>>> paymentPeriodMap = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getContractId, contract.getId())
                .gt(ContractPaymentPeriodEntity::getSubContractId, 0).list().stream()
                .collect(Collectors.groupingBy(ContractPaymentPeriodEntity::getSubContractId, Collectors.groupingBy(ContractPaymentPeriodEntity::getFeeType)));
        if (MapUtils.isEmpty(paymentPeriodMap)) return;

        for (SubContractDetailVO subContract : subContracts) {
            Map<String, List<ContractPaymentPeriodEntity>> periodMap = paymentPeriodMap.get(subContract.getId());
            if (MapUtils.isEmpty(periodMap)) continue;

            // 获取台账数据
            Map<Integer, LedgerEntity> ledgerEntityMap = getLedgerOfPaymentPeriod(periodMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(e -> CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId())
                    .collect(Collectors.toSet()), fillingLedger);

            // 分成有费用对象和没有两种情况分别处理，
            if (CollectionUtils.isEmpty(subContract.getFees())) {
                subContract.setFees(Lists.newArrayList());
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = new SubContractFeeDetailVO();
                    feeVo.setFeeType(feeType);
                    feeVo.setInvoiceType(CollectionUtils.isEmpty(periods) ? "" : periods.get(VenueConstants.ZERO)
                            .getInvoiceType());
                    feeVo.setTaxPoint(CollectionUtils.isEmpty(periods) ? "" : periods.get(VenueConstants.ZERO)
                            .getTaxPoint());
                    feeVo.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, periods, ledgerEntityMap, planPaymentDateOrder));
                    subContract.getFees().add(feeVo);
                });
            } else {
                Map<String, SubContractFeeDetailVO> feeMap = subContract.getFees().stream()
                        .collect(Collectors.toMap(SubContractFeeDetailVO::getFeeType, Function.identity(), (o, n) -> n));
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = feeMap.get(feeType);
                    if (Objects.isNull(feeVo)) {
                        feeVo = new SubContractFeeDetailVO();
                        feeVo.setFeeType(feeType);
                        subContract.getFees().add(feeVo);
                    }
                    feeVo.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, periods, ledgerEntityMap, planPaymentDateOrder));
                });
            }
            // 按字典排序，方便每次看到数据都是一样的顺序
            subContract.getFees().sort(Comparator.comparing(SubContractFeeDetailVO::getFeeType));
        }

        // 翻译字典名字
        List<SubContractFeeDetailVO> allFees = subContracts.stream().map(SubContractDetailVO::getFees)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).filter(Objects::nonNull).toList();
        translatorFactory.translate(allFees);
    }

    /**
     * 填充子合同信息
     */
    private void fillingSubContracts(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查询合同下的子合同，按项目分组
        Map<Integer, List<ContractSubEntity>> subContractMap = contractSubService.lambdaQuery()
                .eq(ContractSubEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractSubEntity::getProjectId));
        if (MapUtils.isEmpty(subContractMap)) {
            return;
        }

        // 填充子合同信息
        for (ContractProjectDetailVO project : projects) {
            List<ContractSubEntity> subContracts = subContractMap.getOrDefault(project.getId(), Collections.emptyList());
            project.setSubContracts(subContracts.stream().map(contractConvert::toEditDetailVO).toList());
        }

        fillingSubContractDeposit(projects);

        // 翻译字典名字
        translatorFactory.translate(projects.stream().map(ContractProjectDetailVO::getSubContracts)
                .flatMap(Collection::stream).toList());
    }

    /**
     * 填充子合同的押金信息
     */
    private void fillingSubContractDeposit(List<ContractProjectDetailVO> projects) {
        List<Integer> subContractIds = projects.stream().flatMap(project -> project.getSubContracts().stream())
                .map(SubContractDetailVO::getId).toList();
        Map<Integer, List<ContractDepositSupplierEntity>> depositSupplierMap = iContractDepositSupplierService.lambdaQuery()
                .in(ContractDepositSupplierEntity::getSubContractId, subContractIds)
                .orderByDesc(ContractDepositSupplierEntity::getId).list().stream()
                .collect(Collectors.groupingBy(ContractDepositSupplierEntity::getSubContractId));
        projects.stream().flatMap(project -> project.getSubContracts().stream()).forEach(subContractDetailVO -> {
            if (depositSupplierMap.containsKey(subContractDetailVO.getId())) {
                ContractDepositSupplierEntity depositSupplierEntity = depositSupplierMap.get(subContractDetailVO.getId())
                        .get(VenueConstants.ZERO);
                subContractDetailVO.setDepositId(depositSupplierEntity.getId());
                subContractDetailVO.setDepositSupplierId(depositSupplierEntity.getSupplierId());
                subContractDetailVO.setDepositSupplierName(depositSupplierEntity.getSupplierName());
                subContractDetailVO.setDepositSupplierAccountNo(depositSupplierEntity.getAccountNo());
                subContractDetailVO.setDepositSupplierBankId(depositSupplierEntity.getSupplierBankId());
            }
        });
    }

    /**
     * 填充子合同的供应商信息
     */
    private void fillingSubContractSuppliers(ContractEntity contract, boolean expand, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 查询子合同-供应商, key:项目ID, value: 供应商列表
        Set<Integer> supplierIds = Sets.newHashSet();
        Set<Integer> projectIds = projects.stream().map(ContractProjectDetailVO::getId).collect(Collectors.toSet());
        Map<Integer, List<ContractSupplierEntity>> supplierMap = contractSupplierService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(supplierIds), ContractSupplierEntity::getProjectId, projectIds)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list().stream()
                .peek(supplier -> supplierIds.add(supplier.getSupplierId()))
                .collect(Collectors.groupingBy(ContractSupplierEntity::getProjectId));
        if (MapUtils.isEmpty(supplierMap)) {
            return;
        }

        // 查询合同-供应商-银行信息,  key:子合同ID, value: 供应商ID -> 银行账号列表
        Map<Integer, Map<Integer, List<ContractSupplierBankEntity>>> contractSupplierBankMap = contractSupplierBankService.lambdaQuery()
                .gt(ContractSupplierBankEntity::getSubContractId, 0)
                .eq(ContractSupplierBankEntity::getContractId, contract.getId())
                .in(ContractSupplierBankEntity::getSupplierId, supplierIds).list().stream()
                .collect(Collectors.groupingBy(ContractSupplierBankEntity::getSubContractId, Collectors.groupingBy(ContractSupplierBankEntity::getSupplierId, Collectors.toList())));

        // 返回合同的供应商信息
        for (ContractProjectDetailVO project : projects) {
            if (CollectionUtils.isEmpty(project.getSubContracts())) {
                continue;
            }
            // 找到项目的所有供应商
            Map<Integer, List<ContractSupplierEntity>> subSupplierMap = supplierMap.getOrDefault(project.getId(), Collections.emptyList())
                    .stream().collect(Collectors.groupingBy(ContractSupplierEntity::getSubContractId));
            if (MapUtils.isEmpty(subSupplierMap)) {
                continue;
            }
            for (SubContractDetailVO subContract : project.getSubContracts()) {
                subContract.setSuppliers(getContractSuppliers(expand, subSupplierMap.get(subContract.getId()), contractSupplierBankMap.getOrDefault(subContract.getId(), Collections.emptyMap())));
            }
        }
    }

    /**
     * 填充楼宇相关信息
     */
    public void fillingBuildingRating(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 填充楼宇相关信息
        List<String> businessCodes = projects.stream().map(ContractProjectDetailVO::getProjectCode).distinct().toList();
        if (CollectionUtils.isEmpty(businessCodes)) return;

        // 查询楼宇评级信息
        List<BuildingRatingDTO> buildingRatings = Optional.ofNullable(feignMethWebRpc.listBuildingRatings(businessCodes, null))
                .map(ResultTemplate::getData).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(buildingRatings)) return;

        Map<String, BuildingRatingDTO> buildingRatingMap = Maps.newHashMap();
        for (BuildingRatingDTO buildingRating : buildingRatings) {
            if (CollectionUtils.isEmpty(buildingRating.getBusinessCodes())) continue;
            buildingRating.getBusinessCodes().forEach(businessCode -> {
                buildingRatingMap.put(businessCode.trim().toUpperCase(), buildingRating);
            });
        }

        // 为项目填充经纬度
        for (ContractProjectDetailVO project : projects) {
            if (StringUtils.isBlank(project.getProjectCode())) continue;
            BuildingRatingDTO buildingRating = buildingRatingMap.get(project.getProjectCode().trim().toUpperCase());
            if (Objects.isNull(buildingRating)) continue;

            project.setLatitude(buildingRating.getMapLatitude());
            project.setLongitude(buildingRating.getMapLongitude());

            // 填充楼宇编码
            project.setBuildingNo(buildingRating.getBuildingNo());
        }
    }


    public List<SysUserApproveVO> getAgentOrg(ContractAgentParam agentParam) {
        ResultTemplate<List<SysUserApproveVO>> userApprove = feignMethWebRpc.getAgents(agentParam);
        if (!userApprove.getSuccess() || Objects.isNull(userApprove.getData())) {
            throw new BusinessException("代理商查询失败");
        }
        return userApprove.getData();
    }

    /**
     * 查询合同是否可以发起补充协议
     * 在途: 存在草稿,审批中,审批通过未生效
     * 已关闭的合同不支持
     *
     * @param contractId 合同ID
     * @param id         补充协议ID
     * @return 可创建:true
     */
    public boolean canCreate(Integer contractId, Integer id) {
        ContractEntity contractEntity = contractService.getById(contractId);
        if (contractEntity == null) {
            return false;
        }
        if ("0028-6".equals(contractEntity.getFormalStatus())) {
            return false;
        }

        if (Objects.nonNull(id)) {
            // 有id说明是编辑,判断状态非草稿不能提交
            ContractEntity modify = contractService.getById(id);
            if (!contractApplyStatusResubmit.contains(modify.getApplyStatus())) {
                return false;
            }
        }

        List<String> applyStatusList = Arrays.asList("0025-1", "0025-2", "0025-3", "0025-4");
        List<ContractEntity> list = contractService.lambdaQuery().select(ContractEntity::getId)
                .eq(ContractEntity::getParentId, contractId).in(ContractEntity::getApplyStatus, applyStatusList)
                .eq(ContractEntity::getEffectFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(id), ContractEntity::getId, id).list();
        return CollectionUtils.isEmpty(list);
    }


    /**
     * 获取补充协议详情
     *
     * @param contractId 合同ID
     * @return 补充协议详情
     */
    public ContractDetailVO getSupplementDetail(Integer contractId) {
        return getSupplementDetail(contractId, false);
    }

    /**
     * 获取补充协议详情
     *
     * @param contractId  合同ID
     * @param fillingInfo 是否填充信息
     * @return 补充协议详情
     */
    public ContractDetailVO getSupplementDetail(Integer contractId, boolean fillingInfo) {
        // 获取原始合同详情信息
        ContractDetailVO contractDetailVO = getContractDetail(contractId, fillingInfo);
        if (Objects.isNull(contractDetailVO) || Objects.isNull(contractDetailVO.getParentId())) {
            return contractDetailVO;
        }

        // 补充原合同信息
        ContractEntity entity = Objects.equals(0, contractDetailVO.getParentId()) ? null : contractService.getById(contractDetailVO.getParentId());
        if (Objects.isNull(entity)) {
            contractDetailVO.setParent(new ContractDetailSimplifyVO().setId(contractDetailVO.getParentId()));
            return contractDetailVO;
        }
        //
        ContractDetailSimplifyVO vo = contractConvert.toDetailSimplifyVO(entity);
        if (fillingInfo) {
            vo.setAttachments(attachmentService.listByContractId(entity.getId(), List.of(AttachmentTypeEnum.MAIN_SEALED.getCode(), AttachmentTypeEnum.SUB_SEALED.getCode()))
                    .stream().map(attachment -> {
                        ContractAttachmentVO attachmentVO = contractConvert.toVO(attachment);
                        attachmentVO.setTypeName("%s-%s".formatted(Objects.equals(attachment.getContractId(), entity.getId()) ? "原始合同" : "补充协议", AttachmentTypeEnum.getDesc(attachment.getType())));
                        return attachmentVO;
                    }).sorted(Comparator.comparing(ContractAttachmentVO::getCreateTime)).toList());
        }
        contractDetailVO.setParent(vo);

        return contractDetailVO;
    }

    /**
     * @param contractId 合同ID
     * @param force
     * @return 补充协议申请单详情
     */
    public ContractEditDetailVO getSupplementApplyDetail(Integer contractId, boolean force) {
        // 获取原始合同详情信息
        ContractEditDetailVO contractEditDetailVO = getApplyDetail(contractId, force, null, Map.of("planPaymentDateOrder", false, "ledger", true, "subLedger", true));

        // 补充原合同信息
        if (Objects.nonNull(contractEditDetailVO.getParentId())) {
            ContractEntity entity = null;
            if (contractEditDetailVO.getParentId() != 0) {
                entity = contractService.getById(contractEditDetailVO.getParentId());
            }
            if (Objects.isNull(entity)) {
                contractEditDetailVO.setParent(new ContractDetailSimplifyVO().setId(contractEditDetailVO.getParentId()));
            } else {
                contractEditDetailVO.setParent(contractConvert.toDetailSimplifyVO(entity));
            }
        }

        return contractEditDetailVO;
    }

    public Integer getContractType(Integer contractId) {
        return Optional.ofNullable(contractService.getById(contractId)).map(ContractEntity::getContractType)
                .orElse(null);
    }

    /**
     * 查找项目下的点位
     *
     * @param parentId    原合同id
     * @param projectName 项目名称
     * @return
     */
    public List<ContractDevicePointDetailVO> listProjectPoints(Integer parentId, String projectName) {

        List<ContractProjectEntity> contractProjectEntities = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId).eq(ContractProjectEntity::getContractId, parentId)
                .eq(ContractProjectEntity::getProjectName, projectName).list();
        if (CollectionUtils.isEmpty(contractProjectEntities)) {
            return Collections.emptyList();
        }
        List<ContractDevicePointEntity> contractDevicePointEntities = devicePointService.lambdaQuery()
                .select(ContractDevicePointEntity::getCode, ContractDevicePointEntity::getName)
                .eq(ContractDevicePointEntity::getContractId, parentId)
                .eq(ContractDevicePointEntity::getProjectId, contractProjectEntities.get(0).getId()).list();
        if (CollectionUtils.isEmpty(contractDevicePointEntities)) {
            return Collections.emptyList();
        }
        return contractDevicePointEntities.stream().map(contractConvert::toEditDetailVO).toList();
    }

    public boolean hasContract(List<String> buildingCodes) {
        if (CollectionUtils.isEmpty(buildingCodes)) {
            return false;
        }
        Set<Integer> collect = projectService.lambdaQuery().select(ContractProjectEntity::getContractId)
                .in(ContractProjectEntity::getProjectCode, buildingCodes).list().stream()
                .map(ContractProjectEntity::getContractId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect)) {
            return false;
        }

        Set<String> status = Sets.newHashSet(ContractStatusEnum.PENDING.getCode(), ContractStatusEnum.FORMAL.getCode(), ContractStatusEnum.WAIT_EXECUTE.getCode(), ContractStatusEnum.EXECUTING.getCode());
        List<ContractEntity> list = contractService.lambdaQuery().select(ContractEntity::getId)
                .eq(ContractEntity::getApplyStatus, ContractApplyStatusEnum.APPROVED.getCode())
                .in(ContractEntity::getId, collect).in(ContractEntity::getFormalStatus, status).list();

        return CollectionUtils.isNotEmpty(list);
    }


    public String hasContractStatus(List<String> buildingCodes) {
        String flag = ContractAssistantEnum.REPORT.getDesc();
        if (CollectionUtils.isEmpty(buildingCodes)) return flag;
        Set<Integer> collect = projectService.lambdaQuery().select(ContractProjectEntity::getContractId)
                .in(ContractProjectEntity::getProjectCode, buildingCodes).list().stream()
                .map(ContractProjectEntity::getContractId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect)) return flag;
        Set<String> status = Sets.newHashSet(ContractApplyStatusEnum.DRAFT.getCode());

        List<ContractEntity> list = contractService.lambdaQuery().select(ContractEntity::getId)
                .notIn(ContractEntity::getApplyStatus, status).in(ContractEntity::getId, collect).list();

        if (CollectionUtils.isNotEmpty(list)) {
            flag = ContractAssistantEnum.SIGN_UP.getDesc();
        }

        return flag;
    }


    /**
     * 根据合同编码模糊查询
     */
    public PageResponseVO<CodeNameVO> queryContractCode(String keyword, List<Integer> contractTypes, Integer pageSize) {
        pageSize = Optional.ofNullable(pageSize).orElse(20);
        // 获取用户数据权限
        UserDataAccessV2DTO userDataAccessV2 = dataAccessService.getUserDataAccessV2();
        if (Objects.isNull(userDataAccessV2)) {
            log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
            throw new CommonException("用户数据权限不足");
        }

        List<Integer> cityIds = userDataAccessV2.getCityIds();
        List<Integer> userIds = userDataAccessV2.getUserIds();
        List<Integer> agentIds = userDataAccessV2.getAgentIds();

        List<CodeNameVO> contractCodes = contractService.lambdaQuery().select(ContractEntity::getContractCode)
                .isNotNull(ContractEntity::getContractCode)
                .eq(ContractEntity::getFormalFlag, BooleFlagEnum.YES.getCode())
                .like(StringUtils.isNotBlank(keyword), ContractEntity::getContractCode, keyword)
                .in(CollectionUtils.isNotEmpty(contractTypes), ContractEntity::getContractType, contractTypes)
                .in(CollectionUtils.isNotEmpty(userIds), ContractEntity::getFollower, userIds)
                .in(CollectionUtils.isNotEmpty(cityIds), ContractEntity::getCityId, cityIds)
                .in(CollectionUtils.isNotEmpty(agentIds), ContractEntity::getAgentId, agentIds)
                .orderByDesc(ContractEntity::getId).last("LIMIT " + pageSize).list().stream()
                .map(item -> new CodeNameVO(null, item.getContractCode(), item.getContractCode())).toList();
        // 没查到数据
        if (CollectionUtils.isEmpty(contractCodes)) {
            return new PageResponseVO<>();
        }

        return new PageResponseVO<>(1L, Long.valueOf(pageSize), (long) contractCodes.size(), contractCodes);
    }

    /**
     * 根据项目名称模糊查询
     */
    public PageResponseVO<CodeNameVO> queryProjectName(String keyword, List<Integer> contractTypes, Integer pageSize) {
        // 获取用户数据权限
        UserDataAccessV2DTO userDataAccessV2 = dataAccessService.getUserDataAccessV2();
        if (Objects.isNull(userDataAccessV2)) {
            log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
            throw new CommonException("用户数据权限不足");
        }

        ContractQueryDTO queryCondition = new ContractQueryDTO();
        queryCondition.setContractTypes(contractTypes);
        queryCondition.setCityIds(userDataAccessV2.getCityIds());
        queryCondition.setUserIds(userDataAccessV2.getUserIds());
        queryCondition.setAgentIds(userDataAccessV2.getAgentIds());
        queryCondition.setProjectName(keyword);
        List<CodeNameVO> projects = projectService.listProjectCodes(queryCondition).stream()
                .map(item -> new CodeNameVO(null, item.getProjectCode(), item.getProjectName())).toList();

        // 没查到数据
        if (CollectionUtils.isEmpty(projects)) {
            return new PageResponseVO<>();
        }

        pageSize = Optional.ofNullable(pageSize).orElse(20);
        int count = projects.size();
        if (projects.size() > pageSize) {
            projects = projects.subList(0, pageSize);
        }

        return new PageResponseVO<>(1L, Long.valueOf(pageSize), (long) count, projects);
    }

    /**
     * 获取合同变更记录
     *
     * @param contractId 合同ID
     * @return 合同变更记录
     */
    public List<ContractSnapshotRecordVO> getContractSnapshots(Integer contractId) {
        // 查询合同所有的快照记录
        List<ContractSnapshotEntity> snapshots = contractSnapshotService.lambdaQuery()
                .eq(ContractSnapshotEntity::getContractId, contractId)
                .in(ContractSnapshotEntity::getSourceType, List.of(SnapshotSourceTypeEnum.SCHEDULE.getCode(), SnapshotSourceTypeEnum.ARCHIVE.getCode(), SnapshotSourceTypeEnum.AGREEMENT_SCHEDULE.getCode(), SnapshotSourceTypeEnum.AGREEMENT_ARCHIVE.getCode()))
                .eq(ContractSnapshotEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .select(ContractSnapshotEntity::getId, ContractSnapshotEntity::getSourceType, ContractSnapshotEntity::getContractId, ContractSnapshotEntity::getInvolvedId, ContractSnapshotEntity::getCreator, ContractSnapshotEntity::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(snapshots)) {
            return List.of();
        }

        // 获取变更发起人ID
        Map<Integer, Integer> snapshotInvolvedIdUserMap = getSnapshotInvolvedMap(snapshots);

        // 转换为VO对象
        List<Integer> snapshotIds = Lists.newArrayListWithExpectedSize(snapshots.size() + 1);
        Map<Integer, Integer> snapshotInvolvedIdMap = Maps.newHashMapWithExpectedSize(snapshots.size());
        snapshots.forEach(e -> {
            snapshotIds.add(e.getId());
            snapshotInvolvedIdMap.put(e.getInvolvedId(), e.getContractId());
        });
        snapshotIds.add(null);
        // 查询关联变更类型
        Map<Integer, String> changeTypeMap = MapUtils.isEmpty(snapshotInvolvedIdMap) ? Map.of() : contractAmendmentChangeTypeService.lambdaQuery()
                .in(ContractAmendmentChangeTypeEntity::getContractId, snapshotInvolvedIdMap.keySet())
                .eq(ContractAmendmentChangeTypeEntity::getDeleted, BooleFlagEnum.NO.getCode()).list().stream()
                .collect(Collectors.toMap(ContractAmendmentChangeTypeEntity::getContractId, ContractAmendmentChangeTypeEntity::getChangeType, (v1, v2) -> v1));
        // 查询变更的生效时间
        Map<Integer, LocalDate> changeEffectDateMap = MapUtils.isEmpty(snapshotInvolvedIdMap) ? Map.of() : contractService.lambdaQuery()
                .in(ContractEntity::getId, snapshotInvolvedIdMap.keySet())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).list().stream()
                .collect(Collectors.toMap(ContractEntity::getId, ContractEntity::getEffectDate));
        //
        List<ContractSnapshotRecordVO> vos = Lists.newArrayListWithExpectedSize(snapshots.size());
        for (int i = 0; i < snapshots.size(); i++) {
            ContractSnapshotEntity snapshot = snapshots.get(i);
            ContractSnapshotRecordVO vo = contractConvert.toVO(snapshot);
            // 设置变更人
            if (!Objects.equals(vo.getCreator(), 0)) {
                Optional.ofNullable(snapshotInvolvedIdUserMap.get(snapshot.getInvolvedId())).ifPresent(vo::setCreator);
            }
            // 设置变更类型
            String changeTypeName = Optional.ofNullable(changeTypeMap.get(snapshot.getInvolvedId()))
                    .map(ContractChangeTypeEnum::getDesc).orElse("");
            // 区分补充协议和其他类型
            if (Set.of(SnapshotSourceTypeEnum.AGREEMENT_SCHEDULE.getCode(), SnapshotSourceTypeEnum.AGREEMENT_ARCHIVE.getCode())
                    .contains(vo.getSourceType())) {
                vo.setChangeTypeName("补充协议");
            } else {
                vo.setChangeTypeName(StringUtils.isBlank(changeTypeName) ? "" : "合同" + changeTypeName);
            }
            // 设置变更时间
            vo.setChangeDate(changeEffectDateMap.get(snapshot.getInvolvedId()));
            // 设置变更前后快照ID
            vo.setBefore(snapshotIds.get(i));
            vo.setAfter(snapshotIds.get(i + 1));
            vos.add(vo);
        }

        translatorFactory.translate(vos);
        return vos;
    }

    /**
     * 获取快照涉及的变更发起人ID信息
     */
    private Map<Integer, Integer> getSnapshotInvolvedMap(List<ContractSnapshotEntity> snapshots) {
        if (CollectionUtils.isEmpty(snapshots)) {
            return Map.of();
        }
        return contractService.lambdaQuery().select(ContractEntity::getId, ContractEntity::getCreator)
                .in(ContractEntity::getId, snapshots.stream().map(ContractSnapshotEntity::getInvolvedId)
                        .collect(Collectors.toSet())).eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list().stream().collect(Collectors.toMap(ContractEntity::getId, ContractEntity::getCreator));
    }

    public PageResponseVO<CodeNameVO> queryContractCode(PageRequestVO<String> requestVo) {
        Page<ContractEntity> page = contractService.query().select(" distinct contract_code")
                .like(StringUtils.isNotBlank(requestVo.getQuery()), "contract_code", requestVo.getQuery())
                .page(new Page<>(requestVo.getCurrentPage(), requestVo.getPageSize()));
        if (null != page && CollUtil.isNotEmpty(page.getRecords())) {
            List<CodeNameVO> res = page.getRecords().stream()
                    .map(item -> new CodeNameVO(null, item.getContractCode(), item.getContractCode())).toList();
            return new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), res, page.getTotal());
        }
        return new PageResponseVO<>();
    }

    /**
     * 提交时进行拦截：提交合同时,包含大屏点位,判断是否已完成大屏评级,包含小屏点位,判断是否已完成小屏评级
     */
    public String checkContractDeviceFromH5(Map<String, Set<String>> projectWithPointCodesMap) {
        // 数据转换
        List<BusinessPointParam> businessPoints = Lists.newArrayListWithExpectedSize(projectWithPointCodesMap.size());
        projectWithPointCodesMap.forEach((projectCode, pointCodes) -> {
            businessPoints.add(new BusinessPointParam().setBusinessCode(projectCode)
                    .setCodes(pointCodes.stream().toList()));
        });
        //
        Map<String, String> pointCodeSizeFromH5Map;
        if (CollectionUtils.isNotEmpty(businessPoints)) {
            pointCodeSizeFromH5Map = Optional.ofNullable(feignMethWebRpc.listPointSizeByBusinessCodes(businessPoints))
                    .map(ResultTemplate::getData).orElse(Collections.emptyList()).stream()
                    .filter(point -> StringUtils.isNotBlank(point.getCode()))
                    .collect(Collectors.toMap(PointDto::getCode, PointDto::getDeviceSize, (o, n) -> o));
        } else {
            pointCodeSizeFromH5Map = Map.of();
        }
        List<VerifyRatingDTO> verifyRatingDtos = Lists.newArrayListWithExpectedSize(projectWithPointCodesMap.size());
        projectWithPointCodesMap.forEach((projectCode, pointCodes) -> {
            verifyRatingDtos.add(new VerifyRatingDTO().setBusinessCode(projectCode).setContainsLarge(pointCodes.stream()
                    .anyMatch(pointCode -> largeScreenSizeCodes.contains(pointCodeSizeFromH5Map.getOrDefault(pointCode, "")))));
        });
        Result<Integer> result = feignMethH5Rpc.isFinishRating(verifyRatingDtos);
        if (!Objects.equals(result.getCode(), HttpStatus.OK.value())) {
            log.error("checkContractDeviceFromH5:入参：{}；返回：{}", verifyRatingDtos, result);
        }
        Integer data = result.getData();
        if (Objects.isNull(data)) {
            return "数据异常！";
        }
        if (Objects.equals(data, 2)) {
            return "包含大屏点位,请前往媒资工作台手机版我的楼宇-重新评级完成大屏评级!";
        }
        if (Objects.equals(data, 3)) {
            return "缺少评级信息,请前往媒资工作台手机版我的楼宇-重新评级完成楼宇评级!";
        }
        if (Objects.equals(data, 0)) {
            return "数据异常！";
        }
        return null;
    }

    /**
     * 根据合同状态返回商机状态
     */
    public List<BusinessOpportunityStatusVO> queryBusinessOpportunityStatus(List<String> buildingCodes) {
        if (CollectionUtils.isEmpty(buildingCodes)) {
            return new ArrayList<>();
        }

        // 根据
        List<BusinessOpportunityStatusVO> vos = contractService.queryBusinessOpportunityStatus(buildingCodes);


        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }

        // 根据 projectCode 去重， projectCode相同的取合同ID最大的
        Map<String, BusinessOpportunityStatusVO> voMap = vos.stream()
                .collect(Collectors.toMap(BusinessOpportunityStatusVO::getProjectCode, Function.identity(), (o, n) -> {
                    // 取合同ID最大的
                    if (o.getContractId() >= n.getContractId()) {
                        return o;
                    }
                    return n;
                }));

        // 取出 voMap中的值
        List<BusinessOpportunityStatusVO> result = new ArrayList<>(voMap.values());

        for (BusinessOpportunityStatusVO vo : result) {
            vo.setStatus(getOpportunityStatus(vo));
        }

        return result;
    }

    private String getOpportunityStatus(BusinessOpportunityStatusVO vo) {

        if (vo == null) {
            return null;
        }

        if (StringUtils.isBlank(vo.getApplyStatus()) || StringUtils.isBlank(vo.getFormalStatus())) {
            return null;
        }

        // 方案报价
        if (isProposalQuotation(vo)) {
            return BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode();
        }

        // 成交
        if (isDeal(vo)) {
            return BusinessChangeStatusEnum.DEAL.getCode();
        }

        // 合同流程
        return BusinessChangeStatusEnum.CONTRACT_PHASE.getCode();
    }

    /**
     * 成交
     * <p>
     * 条件：审核通过，且合同状态为非待归档、非作废状态
     */
    private boolean isDeal(BusinessOpportunityStatusVO vo) {
        String applyStatus = vo.getApplyStatus();
        String formalStatus = vo.getFormalStatus();

        return StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.APPROVED.getCode()) && !StringUtils.equalsIgnoreCase(formalStatus, ContractStatusEnum.PENDING.getCode()) && !StringUtils.equalsIgnoreCase(formalStatus, ContractStatusEnum.INVALID.getCode());
    }

    /**
     * 方案报价状态
     * <p>
     * 条件：审核状态为草稿、审核不通过、撤回、退回、作废
     */
    private boolean isProposalQuotation(BusinessOpportunityStatusVO vo) {
        String applyStatus = vo.getApplyStatus();
        return StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.DRAFT.getCode()) || StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.REJECT.getCode()) || StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.INVALID.getCode()) || StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.WITHDRAW.getCode()) || StringUtils.equalsIgnoreCase(applyStatus, ContractApplyStatusEnum.RETURN.getCode());
    }

    public Boolean checkResubmit(Integer id) {
        ContractEntity contract = contractService.getById(id);
        if (Objects.isNull(contract)) {
            log.info("合同申请单({})不存在", id);
            return false;
        }

        // 登录人
        CachedUser user = UserThreadLocal.getUser();
        if (Objects.isNull(user)) {
            log.info("登录人信息不存在");
            return false;
        }

        if (!Objects.equals(user.getId(), contract.getFollower())) {
            log.info("登录人({})不是合同({})的跟进人({})", user.getId(), id, contract.getFollower());
            return false;
        }

        // 项目
        List<ContractProjectEntity> projects = projectService.lambdaQuery().eq(ContractProjectEntity::getContractId, id)
                .list();
        if (CollUtil.isEmpty(projects)) {
            log.info("合同({})没有项目", id);
            return false;
        }

        List<String> projectCodes = projects.stream().map(ContractProjectEntity::getProjectCode).distinct().toList();

        return doCheckResubmit(user, projectCodes);
    }

    public boolean doCheckResubmit(CachedUser user, List<String> projectCodes) {
        // 查询是否有不属于的项目
        ResultTemplate<List<String>> result = feignMethH5Rpc.responsibilityCheck(user.getWno(), projectCodes);
        if (!result.getSuccess()) {
            log.warn("查询是否有不属于的项目失败，projectCodes：{}", projectCodes);
            throw new RuntimeException("查询是否有不属于的项目失败");
        }

        List<String> notBelongCodes = result.getData();
        log.info("不属于的项目，notBelongCodes：{}", notBelongCodes);
        return CollUtil.isEmpty(notBelongCodes);
    }

    /**
     * 获取用户的原始合同编码列表
     */
    public List<String> getUserContractCodes() {
        return contractService.lambdaQuery().select(ContractEntity::getContractCode)
                .eq(ContractEntity::getFollower, UserThreadLocal.getUserId())
                .in(ContractEntity::getFormalStatus, Set.of(ContractStatusEnum.WAIT_EXECUTE.getCode(), ContractStatusEnum.EXECUTING.getCode(), ContractStatusEnum.EXPIRED.getCode()))
                .in(ContractEntity::getContractType, Set.of(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode(), ContractTypeEnum.HISTORY.getCode()))
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).list().stream()
                .map(ContractEntity::getContractCode).filter(StringUtils::isNotBlank).distinct().toList();
    }

    /**
     * 根据项目编码获取此项目下待执行、执行中的合同及点位编码信息
     *
     * @param buildingCode 项目编码
     */
    public List<ContractWithPointsVO> queryProjectContractPoints(String buildingCode) {
        if (StringUtils.isBlank(buildingCode)) {
            return Collections.emptyList();
        }

        // 根据项目编码获取此项目下待执行、执行中的合同
        List<ContractProjectPointsDTO> contracts = projectService.queryProjectContractPoints(buildingCode);
        if (CollUtil.isEmpty(contracts)) {
            return Collections.emptyList();
        }

        Map<String, List<ContractProjectPointsDTO>> businessCodePoints = contracts.stream()
                .collect(Collectors.groupingBy(bean -> bean.getContractCode() + "&" + bean.getFormalStatus() + "&" + bean.getProjectCode()));

        List<ContractWithPointsVO> result = new ArrayList<>();
        businessCodePoints.entrySet().forEach(entry -> {
            String key = entry.getKey();
            List<ContractProjectPointsDTO> points = entry.getValue();
            ContractWithPointsVO vo = new ContractWithPointsVO();
            vo.setContractCode(key.split("&")[0]);
            vo.setContractStatus(key.split("&")[1]);
            vo.setBusinessCode(key.split("&")[2]);
            vo.setPoints(points.stream().map(bean -> bean.getPointCode()).collect(Collectors.toList()));
            result.add(vo);
        });
        return result;
    }

    /**
     * 查询点位最新合同状态
     *
     * @param pointCodes 点位编码
     * @return
     */
    public List<PointsContractStatusVO> queryPointNewestContractStatus(List<String> pointCodes) {

        if (CollectionUtils.isEmpty(pointCodes)) {
            return Collections.emptyList();
        }

        // 获取点位的所有合同状态
        Map<String, Set<String>> pointStatusMap = devicePointService.queryPointContractStatus(pointCodes).stream()
                .collect(Collectors.groupingBy(PointContractVO::getPointCode,
                        Collectors.mapping(PointContractVO::getContractStatus, Collectors.toSet())));

        return pointCodes.stream().map(pointCode -> {
            PointsContractStatusVO vo = new PointsContractStatusVO();
            vo.setPointCode(pointCode);
            vo.setContractStatus(getContractNewStatus(pointStatusMap.get(pointCode)));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取点位最新合同状态
     *
     * @param statusSet 点位合同状态集合
     * @return 最新的合同状态
     */
    private String getContractNewStatus(Set<String> statusSet) {
        if (CollectionUtils.isEmpty(statusSet)) {
            return "无合同";
        }

        // 待执行 执行中  则合同履约中
        if (statusSet.contains(ContractStatusEnum.WAIT_EXECUTE.getCode())
                || statusSet.contains(ContractStatusEnum.EXECUTING.getCode())) {
            return "合同履约中";
        }

        // 已到期、已关闭 则合作终止
        if (statusSet.contains(ContractStatusEnum.EXPIRED.getCode())
                || statusSet.contains(ContractStatusEnum.CLOSED.getCode())) {
            return "合作终止";
        }

        return "无合同";
    }
}

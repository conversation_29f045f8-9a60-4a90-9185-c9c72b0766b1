<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractProjectMapper">
    <!-- 同步合同下的签约数到项目下 -->
    <update id="syncContractSignNumToProject">
        UPDATE venue_contract_project A
        JOIN (
        SELECT b.contract_id,b.project_id,SUM(b.sign_count) sign_count FROM venue_contract_project a
        JOIN venue_contract_device b ON a.id=b.project_id AND a.contract_id=b.contract_id
        WHERE a.contract_id IN
        <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        GROUP BY b.contract_id,b.project_id
        ) B ON A.contract_id=B.contract_id AND A.id=B.project_id
        SET A.last_sign_count=B.sign_count
    </update>

    <select id="selectProjectDepositPaidInfo"
            resultType="com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO">
        select vcp.deposit_flag,
               vcp.deposit_amount,
               vcp.deposit_payment_date,
               vs.supplier_name,
               vcp.id as 'project_id', vcp.project_name
        from venue_contract_project vcp
                 left join venue_contract_deposit_supplier vcds on vcp.id = vcds.project_id
                 left join venue_supplier vs on vs.id = vcds.supplier_id
                 join venue_ledger vl on vl.project_id = vcp.id
        where vcp.contract_id = #{contractId}
          and vcp.deposit_flag = 1
          and vl.payment_period_id = 0
          and vl.paid_amount > 0
    </select>


    <!-- 根据条件查询项目 -->
    <select id="listProjectCodes" resultType="com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity">
        SELECT distinct  p.project_name
        FROM venue_contract_project p
        LEFT JOIN venue_contract c ON p.contract_id = c.id AND c.formal_flag = 1
        <where>
            <if test="condition.contractTypes != null and condition.contractTypes.size > 0">
                AND c.contract_type IN
                <foreach collection="condition.contractTypes" item="item" open="(" close=")" separator=",">#{item} </foreach>
            </if>

            <if test="condition.projectName != null and condition.projectName != ''">
                AND p.project_name LIKE CONCAT('%',#{condition.projectName},'%')
            </if>

            <if test="condition.userIds != null and condition.userIds.size > 0">
                AND c.follower IN
                <foreach collection="condition.userIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>

            <if test="condition.cityIds != null and condition.cityIds.size > 0">
                AND c.city_id IN
                <foreach collection="condition.cityIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>

            <if test="condition.agentIds != null and condition.agentIds.size > 0">
                AND c.agent_id IN
                <foreach collection="condition.agentIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>

        </where>
        GROUP BY p.project_name
    </select>
    <select id="queryProjectContractPoints" resultType="com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectPointsDTO"
            parameterType="java.lang.String">
        SELECT c.id            AS contractId,
               c.contract_code AS contractCode,
               c.formal_status AS formalStatus,
               p.project_code  AS projectCode,
               d.`code`        AS pointCode
        FROM venue_contract c
                 INNER JOIN venue_contract_project p ON c.id = p.contract_id
                 INNER JOIN venue_contract_device_point d ON d.project_id = p.id AND d.contract_id = p.contract_id
        WHERE SUBSTRING_INDEX(p.project_code, '-', 1) = #{projectCode}
          AND c.formal_status IN ('0028-3', '0028-4')
          AND c.contract_type IN (1, 2, 3)
          AND c.delete_flag = 0
        ORDER BY c.contract_code DESC
    </select>

</mapper>

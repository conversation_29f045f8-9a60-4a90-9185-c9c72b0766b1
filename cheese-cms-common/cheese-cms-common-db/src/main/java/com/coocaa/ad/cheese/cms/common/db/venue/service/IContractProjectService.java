package com.coocaa.ad.cheese.cms.common.db.venue.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectPointsDTO;

import java.util.List;

/**
 * 合同-项目 服务类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface IContractProjectService extends IService<ContractProjectEntity> {
    /**
     * 同步合同下的签约数到项目下
     *
     * @param contractIds 合同ID列表
     * @return 成功与否
     */
    boolean syncContractSignNumToProject(List<Integer> contractIds);

    /**
     * 查找指定合同下已付押金的项目
     * @param contractId
     * @return
     */
    List<ContractProjectDepositPaidDTO> selectProjectDepositPaidInfo(Integer contractId);

    /**
     * 根据条件查项目编码
     */
    List<ContractProjectEntity> listProjectCodes(ContractQueryDTO condition);

    /**
     * 查询指定楼宇下的项目合同
     * @param buildingCode 楼宇编码
     */
    List<ContractProjectPointsDTO> queryProjectContractPoints(String buildingCode);
}

package com.coocaa.ad.cheese.cms.common.db.venue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectPointsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同-项目 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Mapper
public interface ContractProjectMapper extends BaseMapper<ContractProjectEntity> {
    /**
     * 同步合同下的签约数到项目下
     *
     * @param contractIds 合同ID列表
     */
    int syncContractSignNumToProject(@Param("contractIds") List<Integer> contractIds);

    List<ContractProjectDepositPaidDTO> selectProjectDepositPaidInfo(@Param("contractId") Integer contractId);


    /**
     * 根据条件查项目编码
     */
    List<ContractProjectEntity> listProjectCodes(@Param("condition") ContractQueryDTO condition);

    /**
     * 根据项目编码查询项目合同
     */
    List<ContractProjectPointsDTO> queryProjectContractPoints(String projectCode);
}

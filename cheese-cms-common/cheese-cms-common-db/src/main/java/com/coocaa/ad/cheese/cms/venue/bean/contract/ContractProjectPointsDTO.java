package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ChangeExtract;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 存储已付押金的项目 以及 押金相关字段用于比对
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/11
 */
@Data
public class ContractProjectPointsDTO {

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同状态
     */
    private String formalStatus;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 点位编号
     */
    private String pointCode;
}

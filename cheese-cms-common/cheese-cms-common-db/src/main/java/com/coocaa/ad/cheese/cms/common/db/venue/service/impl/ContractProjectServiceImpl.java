package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractProjectMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectPointsDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 合同-项目 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractProjectServiceImpl extends ServiceImpl<ContractProjectMapper, ContractProjectEntity> implements IContractProjectService {
    /**
     * 同步合同下的签约数到项目下
     *
     * @param contractIds 合同ID列表
     * @return 成功与否
     */
    @Override
    public boolean syncContractSignNumToProject(List<Integer> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return false;
        }
        return getBaseMapper().syncContractSignNumToProject(contractIds) >= 0;
    }

    @Override
    public List<ContractProjectDepositPaidDTO> selectProjectDepositPaidInfo(Integer contractId) {
        return getBaseMapper().selectProjectDepositPaidInfo(contractId);
    }

    @Override
    public List<ContractProjectEntity> listProjectCodes(ContractQueryDTO condition) {
        return getBaseMapper().listProjectCodes(condition);
    }

    /**
     * 查询项目下的合同
     *
     * @param projectCode 项目编码
     * @return 合同列表
     */
    @Override
    public List<ContractProjectPointsDTO> queryProjectContractPoints(String projectCode) {
        return getBaseMapper().queryProjectContractPoints(projectCode);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.BuildingMetaMapper">

    <select id="metaPic" resultType="com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity">
        SELECT id, `building_meta_no`, `img_type`, `img_url` FROM `building_meta_img_relation`
        WHERE 1=1
        <if test="imgType != null and imgType.size() > 0">
            AND img_type IN
            <foreach collection="imgType" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="mapNo != null and mapNo != ''">
            and building_meta_no = (select building_meta_no from building_meta
            WHERE building_rating_no = #{mapNo})
        </if>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper">

    <select id="selectTaskPage" resultType="com.coocaa.meht.module.approve.dto.TodoTaskDTO">
        SELECT sar.natural_key as businessKey , sar.instance_code as instanceCode,sar.approval_result as approveResult,
        sar.approve_time as approveTime , sar.create_time as submitTime ,sar.create_by as submitUser,
        sar.approve_user as approveUser , sar.approve_type as approvalType ,
        sar.version as version,sar.node_id as nodeId ,
        <if test="query.approvalType == '0164-1' or query.approvalType == '0164-3'">
            COALESCE(br.building_name, br1.building_name) as projectName,
            COALESCE(br.map_city, br1.map_city) as city
        </if>

        <if test="query.approvalType == '0164-2' ">
            pa.id as priceApplyId,
            br.building_name as projectName,
            br.map_city as city
        </if>
        FROM screen_approve_record sar
        <if test="query.approvalType == '0164-1' or query.approvalType == '0164-3'">
            LEFT JOIN building_rating br on br.building_no = sar.natural_key
            LEFT JOIN complete_rating br1 on br1.complete_rating_no = sar.natural_key
        </if>
        <if test="query.approvalType == '0164-2' ">
            LEFT JOIN price_apply pa on pa.apply_code = sar.natural_key
            LEFT JOIN building_rating br on br.building_no = pa.building_no
        </if>
        <if test="query.keyword != null and query.keyword != '' ">
            LEFT JOIN sys_user su ON su.emp_code = sar.create_by
            LEFT JOIN agent_personnel ap ON ap.emp_code = sar.create_by
        </if>
        <where>
            sar.approval_flag = 1 and sar.delete_flag = 0
            <if test="query.keyword != null and query.keyword != '' and (query.approvalType == '0164-1' or query.approvalType == '0164-3' ) ">
                and ((COALESCE(br.building_name, br1.building_name) like concat('%',#{query.keyword},'%'))
                or su.real_name like concat('%',#{query.keyword},'%')
                or ap.emp_name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.keyword != null and query.keyword != '' and query.approvalType == '0164-2' ">
                and (br.building_name like concat('%',#{query.keyword},'%')
                or su.real_name like concat('%',#{query.keyword},'%')
                or ap.emp_name like concat('%',#{query.keyword},'%'))
            </if>
            <if test="query.approveUser != null and query.approveUser != '' ">
                and sar.approve_user = #{query.approveUser}
            </if>
            <if test="query.approvalType != null and query.approvalType != '' ">
                <choose>
                    <when test="query.approvalType == '0164-1'">
                        and sar.approve_type IN ('0164-1', '0164-3')
                    </when>
                    <otherwise>
                        and sar.approve_type = #{query.approvalType}
                    </otherwise>
                </choose>
            </if>
            <if test="status != null and status != '' and status == 'todo'">
                and sar.approve_time is null
            </if>
            <if test="status != null and status != '' and status == 'done'">
                and sar.approve_time is not null
            </if>
            and sar.node_status != '0139-1'
            and sar.node_status != '0139-3'

        </where>
        <if test="status != null and status != '' and status == 'todo'">
            order by sar.id desc
        </if>
        <if test="status != null and status != '' and status == 'done'">
            order by sar.approve_time desc
        </if>
    </select>

    <update id="batchUpdateNodeStatus">
        <foreach collection="nodes" item="item" separator=";">
            UPDATE screen_approve_record
            SET
            remark = #{item.comment},
            approval_flag = #{item.approvalFlag},
            approval_result = #{item.approvalResult},
            approve_time = #{item.endTime},
            submit_time = #{item.startTime},
            node_status = #{item.nodeStatus}
            WHERE
            node_id = #{item.id}
        </foreach>
    </update>

    <select id="countTodoTasksByApprovalType" resultType="com.coocaa.meht.module.approve.dto.TaskDealCountVO">
        SELECT
        CASE
        WHEN approve_type = '0164-3' THEN '0164-1'
        ELSE approve_type
        END as type,
        COUNT(1) as count
        FROM
        screen_approve_record
        WHERE
        delete_flag = 0
        <if test="approveUser != null and approveUser != ''">
            and approve_user = #{approveUser}
        </if>
        AND approve_time IS NULL
        AND approval_flag = 1
        and node_status != '0139-1'
        and node_status != '0139-3'

        GROUP BY
        CASE
        WHEN approve_type = '0164-3' THEN '0164-1'
        ELSE approve_type
        END
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.rating.dao.TempThirdBuildingDbMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.module.rating.entity.TempThirdBuildingDb">
    <!--@mbg.generated-->
    <!--@Table temp_third_building_db-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="building_name" jdbcType="VARCHAR" property="buildingName" />
    <result column="building_no" jdbcType="VARCHAR" property="buildingNo" />
    <result column="third_building_age" jdbcType="VARCHAR" property="thirdBuildingAge" />
    <result column="third_building_brand" jdbcType="VARCHAR" property="thirdBuildingBrand" />
    <result column="third_building_exterior" jdbcType="VARCHAR" property="thirdBuildingExterior" />
    <result column="third_building_garage" jdbcType="VARCHAR" property="thirdBuildingGarage" />
    <result column="third_building_grade" jdbcType="VARCHAR" property="thirdBuildingGrade" />
    <result column="third_building_lobby" jdbcType="VARCHAR" property="thirdBuildingLobby" />
    <result column="third_building_location" jdbcType="VARCHAR" property="thirdBuildingLocation" />
    <result column="third_building_number" jdbcType="VARCHAR" property="thirdBuildingNumber" />
    <result column="third_building_price" jdbcType="VARCHAR" property="thirdBuildingPrice" />
    <result column="third_building_rate" jdbcType="VARCHAR" property="thirdBuildingRate" />
    <result column="third_building_type" jdbcType="VARCHAR" property="thirdBuildingType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_name, building_no, third_building_age, third_building_brand, third_building_exterior, 
    third_building_garage, third_building_grade, third_building_lobby, third_building_location, 
    third_building_number, third_building_price, third_building_rate, third_building_type
  </sql>
</mapper>
package com.coocaa.meht.converter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 行业相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class IndustryConverter extends BaseConverter<String> {
    private final CodeNameHelper codeNameHelper;

    @Override
    protected ConvertType getConvertType() {
        return ConvertType.INDUSTRY;
    }

    @Override
    protected Map<String, String> getNameMapping(Collection<String> keys) {
        return codeNameHelper.getIndustryMapping(keys);
    }
}

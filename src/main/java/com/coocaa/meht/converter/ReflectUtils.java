package com.coocaa.meht.converter;

import cn.hutool.core.map.MapUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 反射工具
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Slf4j
public final class ReflectUtils {
    /**
     * 对类的字段信息缓存
     */
    private static final Map<Class<?>, List<Field>> CLASS_FIELD_MAP = Maps.newConcurrentMap();
    private static final Map<Class<?>, Map<String, Method>> CLASS_READABLE_METHOD_MAP = Maps.newConcurrentMap();
    private static final Map<Class<?>, Map<String, Method>> CLASS_WRITEABLE_METHOD_MAP = Maps.newConcurrentMap();


    /**
     * 检查指定对象所有属性都是空值
     *
     * @param obj               待检查对象
     * @param includeSuperclass 包含父对象
     * @param excludeFields     排除的字段
     * @return true:所有字段都为空
     */
    public static <T> boolean isAllNull(T obj, boolean includeSuperclass, String... excludeFields) {
        Map<String, Method> methodMap;
        if (includeSuperclass) {
            methodMap = new HashMap<>(getReadableMethods(obj));
        } else {
            Method[] methods = obj.getClass().getDeclaredMethods();
            methodMap = Maps.newHashMapWithExpectedSize(methods.length);
            for (Method method : methods) {
                if (method.getParameterCount() <= 0 && StringUtils.startsWithIgnoreCase(method.getName(), "get")) {
                    methodMap.put(StringUtils.substring(method.getName(), 3).toLowerCase(), method);
                }
            }
        }

        // 去掉要排除的字段
        if (ArrayUtils.isNotEmpty(excludeFields)) {
            Arrays.stream(excludeFields).filter(StringUtils::isNotBlank)
                    .map(String::trim).map(String::toLowerCase)
                    .forEach(methodMap::remove);
        }

        // 没有字段可检查，表示所有都不为空
        if (MapUtil.isEmpty(methodMap)) {
            return false;
        }

        for (Method method : methodMap.values()) {
            if (!isNull(obj, method)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查方法返回值是否为空
     *
     * @param obj    待检查对象
     * @param method 取值的Get方法
     * @return true: 为 null 或 字符串为空
     */
    private static <T> boolean isNull(T obj, Method method) {
        Object val = null;
        try {
            val = method.invoke(obj);
        } catch (Exception ex) {
            log.warn("通过反射获取数据失败", ex);
        }
        return Objects.isNull(val) || (method.getReturnType().equals(String.class) && StringUtils.isBlank(String.valueOf(val)));
    }

    /**
     * 获取类的属性信息
     */
    public static <T> List<Field> getAllFields(T obj) {
        Class<?> clz = obj.getClass();
        List<Field> fields = CLASS_FIELD_MAP.get(clz);
        if (CollectionUtils.isEmpty(fields)) {
            fields = Lists.newLinkedList();
            while (Objects.nonNull(clz) && !Objects.equals(clz, Object.class)) {
                fields.addAll(Arrays.stream(clz.getDeclaredFields())
                        .filter(field -> !Modifier.isFinal(field.getModifiers()))
                        .collect(Collectors.toList()));
                clz = clz.getSuperclass();
            }
            CLASS_FIELD_MAP.put(obj.getClass(), fields);
        }
        return ImmutableList.copyOf(fields);
    }

    /**
     * 获取对象的可读方法
     */
    public static <T> Map<String, Method> getReadableMethods(T obj) {
        if (Objects.isNull(obj)) {
            return Collections.emptyMap();
        }

        // 优先从缓存中获取可读方法
        Map<String, Method> methodMap = CLASS_READABLE_METHOD_MAP.get(obj.getClass());
        if (MapUtil.isNotEmpty(methodMap)) {
            return methodMap;
        }

        // 提取可读方法
        Class<?> clz = obj.getClass();
        methodMap = Maps.newHashMap();
        while (Objects.nonNull(clz) && !Objects.equals(clz, Object.class)) {
            Method[] methods = clz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getParameterCount() <= 0 && StringUtils.startsWithIgnoreCase(method.getName(), "get")) {
                    methodMap.put(StringUtils.substring(method.getName(), 3).toLowerCase(), method);
                }
            }
            clz = clz.getSuperclass();
        }

        if (MapUtil.isNotEmpty(methodMap)) {
            CLASS_READABLE_METHOD_MAP.put(obj.getClass(), methodMap);
            return ImmutableMap.copyOf(methodMap);
        }
        return Collections.emptyMap();
    }


    /**
     * 获取对象的可读方法
     */
    public static <T> Map<String, Method> getWriteableMethods(T obj) {
        if (Objects.isNull(obj)) {
            return Collections.emptyMap();
        }

        // 优先从缓存中获取可写方法
        Map<String, Method> methodMap = CLASS_WRITEABLE_METHOD_MAP.get(obj.getClass());
        if (MapUtil.isNotEmpty(methodMap)) {
            return methodMap;
        }

        // 提取可写方法
        Class<?> clz = obj.getClass();
        methodMap = Maps.newHashMap();
        while (Objects.nonNull(clz) && !Objects.equals(clz, Object.class)) {
            Method[] methods = clz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getParameterCount() > 0 && StringUtils.startsWithIgnoreCase(method.getName(), "set")) {
                    methodMap.put(StringUtils.substring(method.getName(), 3).toLowerCase(), method);
                }
            }
            clz = clz.getSuperclass();
        }

        if (MapUtil.isNotEmpty(methodMap)) {
            CLASS_WRITEABLE_METHOD_MAP.put(obj.getClass(), methodMap);
            return ImmutableMap.copyOf(methodMap);
        }
        return Collections.emptyMap();
    }
}


package com.coocaa.meht.converter;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

/**
 * 数据转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-05
 */
@FunctionalInterface
public interface Converter {
    /**
     * 转换单个数据
     */
    default <T> void convert(T value) {
        Optional.ofNullable(value).ifPresent(val -> convert(Collections.singleton(val)));
    }

    /**
     * 转换集合数据
     */
    <T> void convert(Collection<T> values);
}

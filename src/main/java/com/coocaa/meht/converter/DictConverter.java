package com.coocaa.meht.converter;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 字典相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-05
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DictConverter extends BaseConverter<String> {
    private final CodeNameHelper codeNameHelper;

    @Override
    protected ConvertType getConvertType() {
        return ConvertType.DICT;
    }

    @Override
    protected Map<String, String> getNameMapping(Collection<String> keys) {
        return codeNameHelper.getDictMapping(keys);
    }
}

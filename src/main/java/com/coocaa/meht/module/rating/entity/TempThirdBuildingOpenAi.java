package com.coocaa.meht.module.rating.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @version 1.0
* @since 2025-05-29
*/
/**
 * openAI楼宇信息表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "temp_third_building_open_ai")
public class TempThirdBuildingOpenAi {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 楼宇名称
     */
    @TableField(value = "building_name")
    private String buildingName;

    /**
     * 楼宇编号
     */
    @TableField(value = "building_no")
    private String buildingNo;

    /**
     * 建筑年龄（年）
     */
    @TableField(value = "third_building_age")
    private String thirdBuildingAge;

    /**
     * 建筑品牌
     */
    @TableField(value = "third_building_brand")
    private String thirdBuildingBrand;

    /**
     * 建筑外立面材料
     */
    @TableField(value = "third_building_exterior")
    private String thirdBuildingExterior;

    /**
     * 车库信息
     */
    @TableField(value = "third_building_garage")
    private String thirdBuildingGarage;

    /**
     * 建筑等级
     */
    @TableField(value = "third_building_grade")
    private String thirdBuildingGrade;

    /**
     * 大堂信息
     */
    @TableField(value = "third_building_lobby")
    private String thirdBuildingLobby;

    /**
     * 建筑位置
     */
    @TableField(value = "third_building_location")
    private String thirdBuildingLocation;

    /**
     * 建筑数量
     */
    @TableField(value = "third_building_number")
    private String thirdBuildingNumber;

    /**
     * 价格（元/平米）
     */
    @TableField(value = "third_building_price")
    private String thirdBuildingPrice;

    /**
     * 建筑评分
     */
    @TableField(value = "third_building_rate")
    private String thirdBuildingRate;

    /**
     * 建筑类型
     */
    @TableField(value = "third_building_type")
    private String thirdBuildingType;
}
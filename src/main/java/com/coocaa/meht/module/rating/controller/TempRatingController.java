package com.coocaa.meht.module.rating.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.rating.dto.RatingDTO;
import com.coocaa.meht.module.rating.service.TempRatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/temp-rating")
@Tag(name = "临时评分验证", description = "临时评分验证相关接口")
public class TempRatingController {

    @Autowired
    private TempRatingService tempRatingService;

    @Anonymous
    @Operation(summary = "评级计算")
    @PostMapping("/rating")
    public ResultTemplate<Void> rating(@RequestBody RatingDTO param) {
        tempRatingService.rating(param);
        return ResultTemplate.success();
    }

}

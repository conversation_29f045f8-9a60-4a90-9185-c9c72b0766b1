package com.coocaa.meht.module.building.convert;

import com.coocaa.meht.module.building.entity.CompleteRatingDetailEntity;
import com.coocaa.meht.module.building.vo.BuildingDetailsVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.7.8
 * @since 2025-06-16
 */
@Mapper
public interface BuildingDetailsConvert {
    BuildingDetailsConvert INSTANCE = Mappers.getMapper(BuildingDetailsConvert.class);

    BuildingDetailsVO toBuildingDetailsVO(BuildingDetailsEntity entity);


    CompleteRatingDetailEntity toCompleteRatingDetailEntity(BuildingDetailsEntity entity);

    BuildingDetailsVO toBuildingDetailsVOByApplyDto(RatingApplyDto ratingApplyDto);

    @Mapping(target = "buildingNo",source = "completeRatingNo")
    BuildingDetailsVO toBuildingDetailsVOByEntity(CompleteRatingDetailEntity entity);

    BuildingDetailsEntity  toRatingDetailEntity(CompleteRatingDetailEntity entity);
}

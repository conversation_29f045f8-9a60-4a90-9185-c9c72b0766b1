package com.coocaa.meht.module.building.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 算分结果
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
public class ScoreVO {
    @Schema(description = "AI楼宇类型")
    private String thirdBuildingType;

    @Schema(description = "评分等级")
    private String projectLevel;

    @Schema(description = "AI评分")
    private BigDecimal buildingAiScore;

    @Schema(description = "楼宇类型")
    private String buildingType;

    @Schema(description = "AI项目等级")
    private String projectAiLevel;

    @Schema(description = "综合得分")
    private BigDecimal totalScore;

    @Schema(description = "一楼独占")
    private BigDecimal firstFloorExclusive;

    @Schema(description = "一楼共享")
    private BigDecimal firstFloorShare;

    @Schema(description = "负一楼")
    private BigDecimal negativeFirstFloor;

    @Schema(description = "负二楼")
    private BigDecimal negativeTwoFloor;

    @Schema(description = "二楼及以上")
    private BigDecimal twoFloorAbove;

    @Schema(description = "负三楼及以下")
    private BigDecimal thirdFloorBelow;

    @Schema(description = "城市系数值")
    private BigDecimal coefficientValue;

    @Schema(description = "提交系数")
    private BigDecimal submitCoefficient;

    @Schema(description = "复核系数值")
    private BigDecimal finalCoefficient;

    @Schema(description = "是否大屏")
    private boolean largeScreen;

    @Schema(description = "评分项")
    private List<ScoreItemVO> list;

}

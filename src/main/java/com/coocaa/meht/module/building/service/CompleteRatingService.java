package com.coocaa.meht.module.building.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.building.param.TransferAndDropParam;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;

import java.util.Map;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.7.8
 * @since 2025-06-13
 */
public interface CompleteRatingService extends IService<CompleteRatingEntity> {


    /**
     * 保存完善评级草稿
     *
     * @param ratingApplyDto 提交参数
     * @return Result
     */
    Result<String> saveDraft(RatingApplyDto ratingApplyDto);

    /**
     * 提交完善评级
     *
     * @param ratingApplyDto 提交参数
     * @return Result 提交结果
     */
    Result<Boolean> submitComplete(RatingApplyDto ratingApplyDto);


    /**
     * 获取完善评级信息
     *
     * @param completeRatingNo 完善评级No
     * @return RatingVO
     */
    RatingVO getCompleteInfo(String completeRatingNo, String version);

    /**
     * 删除草稿
     *
     * @param buildingNo 楼宇编号
     */
    void deleteDraft(String buildingNo);

    /**
     * 获取完善评级评分细项
     *
     * @return CompleteRatingVO
     */
    Map<String, Object> getScoringItem(String completeRatingNo, String  version);

    /**
     * 完善评级转移及掉公海处理
     *
     */
    void transferAndDrop(TransferAndDropParam param);

    /**
     * 获取完善评级信息
     *
     * @return RatingVO
     */
    RatingVO getCompleteInfoByNo(CompleteRatingEntity completeRating);

    /**
     * 处理Ai评级
     *
     * @param completeRatingNo 业务编号
     * @param operatorWno 操作人工号
     */
    void handleAiRating(String completeRatingNo, String operatorWno);

    /**
     * 提交审核
     *
     * @param completeRatingNo 业务编号
     * @param operatorWno 操作人工号
     */
    void submitApproval(String completeRatingNo, String operatorWno);

    /**
     * 撤销未提交
     *
     * @param bizCode 业务编号
     */
    void revokeUnsubmitted(String bizCode);

}

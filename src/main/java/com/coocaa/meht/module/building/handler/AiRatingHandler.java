package com.coocaa.meht.module.building.handler;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StopWatch;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-07
 */
@Slf4j
@Component
public class AiRatingHandler {

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Lazy
    @Autowired
    private CompleteRatingService completeRatingService;

    @Autowired
    @Qualifier("asyncPool")
    private Executor executor;

    @Autowired
    private RedisUtils redisUtils;


    private static final long TIMEOUT = 60;

    /**
     * 事务提交后异步处理AI评级
     *
     * @param type        评级类型
     * @param bizCode     业务编码
     * @param operatorWno 是否大屏
     */
    public void aiRatingAfterCommit(Type type, String bizCode, String operatorWno) {
        String key = String.format("rating:complete:%s", bizCode);
        redisUtils.set(key, "1", TIMEOUT);
        // 注册事务同步回调，保证处理时事务已提交
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                    StopWatch stopWatch = new StopWatch("AI评级异步操作耗时统计，" + bizCode);
                    stopWatch.start();
                    try {
                        rating(type, bizCode, operatorWno);
                    } catch (Exception e) {
                        log.warn("AI评级异常，继续发起审批，type：{}，bizCode：{}，operatorWno：{}", type, bizCode, operatorWno, e);
                        // 继续发起审批
                        submitApproval(type, bizCode, operatorWno);
                    } finally {
                        redisUtils.delete(key);
                    }
                    stopWatch.stop();
                    log.info(stopWatch.prettyPrint());
                }, executor);
            }
        });
    }

    /**
     * 处理AI评级
     *
     * @param type        评级类型
     * @param bizCode     业务编码
     * @param operatorWno 处理人工号
     */
    public void rating(Type type, String bizCode, String operatorWno) {
        switch (type) {
            case BUILDING_RATING:
                buildingRatingService.handleAiRating(true, bizCode, operatorWno);
                break;
            case EDIT_RATING:
                buildingRatingService.handleAiRating(false, bizCode, operatorWno);
                break;
            case COMPLETE_RATING:
                completeRatingService.handleAiRating(bizCode, operatorWno);
                break;
        }
    }

    private void submitApproval(Type type, String bizCode, String operatorWno) {
        switch (type) {
            case BUILDING_RATING:
                buildingRatingService.submitApproval(bizCode, operatorWno);
                break;
            case COMPLETE_RATING:
                completeRatingService.submitApproval(bizCode, operatorWno);
                break;
        }
    }

    @AllArgsConstructor
    public enum Type {

        /**
         * 楼宇评级
         */
        BUILDING_RATING,

        /**
         * 完善评级
         */
        COMPLETE_RATING,

        /**
         * 编辑评级
         */
        EDIT_RATING
    }

}

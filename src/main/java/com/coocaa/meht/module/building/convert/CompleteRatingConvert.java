package com.coocaa.meht.module.building.convert;

import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Mapper
public interface CompleteRatingConvert {
    CompleteRatingConvert INSTANCE = Mappers.getMapper(CompleteRatingConvert.class);


    @Mapping(target = "buildingExteriorPic", ignore = true)
    @Mapping(target = "buildingLobbyPic", ignore = true)
    @Mapping(target = "buildingHallPic", ignore = true)
    @Mapping(target = "buildingLobbyEnvPic", ignore = true)
    @Mapping(target = "buildingElevatorPic", ignore = true)
    @Mapping(target = "buildingGatePic", ignore = true)
    @Mapping(target = "buildingInstallationPic", ignore = true)
    @Mapping(target = "buildingExteriorPics", ignore = true)
    @Mapping(target = "buildingLobbyPics", ignore = true)
    @Mapping(target = "buildingHallPics", ignore = true)
    @Mapping(target = "buildingLobbyEnvPics", ignore = true)
    @Mapping(target = "buildingElevatorPics", ignore = true)
    @Mapping(target = "buildingGatePics", ignore = true)
    @Mapping(target = "buildingInstallationPics", ignore = true)
    @Mapping(target = "otherAttachments", ignore = true)
    RatingVO toRatingVO(RatingApplyDto ratingApplyDto);

    @Mapping(target = "buildingNo", source = "buildingRatingNo")
    @Mapping(target = "createUserCode", source = "createBy")
    @Mapping(target = "ratingVersion", source = "version")
    RatingVO toRatingVOByEntity(CompleteRatingEntity entity);

}

package com.coocaa.meht.module.building.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Data
public class BuildingScreenVO {
    private Integer id;

    /**
     * 评分编号
     */
    private String buildingRatingNo;

    /**
     * 规格
     */
    private String spec;

    /**
     * 总层数
     */
    private Integer totalBuildingCount;

    /**
     * 入驻企业数量
     */
    private Integer companyCount;

    /**
     * 电梯数量
     */
    private Integer elevatorCount;

    /**
     * 间距
     */
    private BigDecimal buildingSpacing;

    /**
     * 挑高
     */
    private BigDecimal buildingCeilingHeight;

    /**
     * 提交系数
     */
    private BigDecimal submitCoefficient;

    /**
     * 复核系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 特殊说明
     */
    private String specialDesc;

    /**
     * 安装规格名称
     */
    private String specName;

}

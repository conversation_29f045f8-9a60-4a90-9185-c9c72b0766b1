package com.coocaa.meht.module.building.convert;

import com.coocaa.meht.module.building.entity.CreHousePriceRentEntity;
import com.coocaa.meht.rpc.dto.CreHousePriceRentResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-07-10
 */

@Mapper
public interface CreHousePriceRentConvert {
    CreHousePriceRentConvert INSTANCE = Mappers.getMapper(CreHousePriceRentConvert.class);


    @Mapping(target = "longitude", source = "lon")
    @Mapping(target = "latitude", source = "lat")
    CreHousePriceRentEntity toEntity(CreHousePriceRentResponse response);
}

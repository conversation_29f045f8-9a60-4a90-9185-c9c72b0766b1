package com.coocaa.meht.module.building.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 房价行情网租金对象
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cre_house_price_rent")
public class CreHousePriceRentEntity extends BaseEntity {

    /**
     * 城市
     */
    private String cityName;

    /**
     * 行政区
     */
    private String distName;

    /**
     * 小区名
     */
    private String haName;

    /**
     * 地址
     */
    private String location;

    /**
     * 经度 (百度坐标)
     */

    private String longitude;

    /**
     * 纬度 (百度坐标)
     */
    private String latitude;

    /**
     * 小区分类
     */
    private String clName;

    /**
     * 建筑类型
     */
    private String bldgType;

    /**
     * 建筑年代
     */
    private String buildYear;

    /**
     * 近期平均市场租金 元/日/㎡
     */
    private BigDecimal price;

    /**
     * 租金说明
     */
    private String priceNote;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 占地面价
     */
    private String groundArea;

    /**
     * 建筑面积
     */
    private String constArea;

} 
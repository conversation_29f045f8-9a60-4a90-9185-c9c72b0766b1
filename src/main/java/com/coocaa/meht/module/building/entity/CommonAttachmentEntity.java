package com.coocaa.meht.module.building.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("common_attachment")
public class CommonAttachmentEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 附件归属类型
     */
    private Integer type;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务id
     */
    private Integer bizId;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 附件全路径
     */
    private String url;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 附件大小
     */
    private Long size;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 扩展数据
     */
    private String extendData;

    /**
     * 否删除: 0否,1是
     */
    private Integer deleted;

}

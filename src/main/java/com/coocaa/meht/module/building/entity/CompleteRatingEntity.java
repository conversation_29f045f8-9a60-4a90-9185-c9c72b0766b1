package com.coocaa.meht.module.building.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 完善主表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("complete_rating")
public class CompleteRatingEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 完善评级编号
     */
    private String buildingRatingNo;

    /**
     * 楼宇类型 0写字楼 1商住楼 2综合体 3产业园区
     */
    private Integer buildingType;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 状态和楼宇评级一致
     */
    private Integer status;

    /**
     * 完善标识 1:完善小屏, 2:完善大屏, 3:完善大小屏
     */
    private Integer largeScreenFlag;

    /**
     * 完善编码
     */
    private String completeRatingNo;

    /**
     * 复核等级等级
     */
    private String projectReviewLevel;


    /**
     * 评分等级等级
     */
    private String projectLevel;

    /**
     * 综合得分
     */
    private BigDecimal buildingScore;

    /**
     * AI评级
     */
    private String projectAiLevel;

    /**
     * AI得分
     */
    private BigDecimal buildingAiScore;

    /**
     * 一楼独享
     */
    private BigDecimal firstFloorExclusive;

    /**
     * 一楼共享
     */
    private BigDecimal firstFloorShare;

    /**
     * 负一楼
     */
    private BigDecimal negativeFirstFloor;

    /**
     * 负二楼
     */
    private BigDecimal negativeSecondFloor;

    /**
     * 二楼及以上
     */
    private BigDecimal secondFloorAbove;

    /**
     * 负三楼及以下
     */
    private BigDecimal thirdFloorBelow;

    /**
     * 目标点位数
     */
    private Integer targetPointCount;

    /**
     * 省名称
     */
    private String mapProvince;
    /**
     * 市名称
     */
    private String mapCity;
    /**
     * 区名称
     */
    private String mapRegion;
    /**
     * 详细地址
     */
    private String mapAddress;

    /**
     * 草稿内容
     */
    private String draftText;

    /**
     * 提交人
     */
    private String submitUser;

    /**
     * 数据版本标识
     */
    private Integer dataFlag;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 数据版本号
     */
    private String version;

    /**
     * 外墙材料附件地址
     */
    private String buildingExteriorPic;

    /**
     * 楼盘大堂附件地址
     */
    private String buildingLobbyPic;

    /**
     * 侯梯厅附件地址
     */
    private String buildingHallPic;

    /**
     * 大堂环境图附件地址
     */
    private String buildingLobbyEnvPic;

    /**
     * 梯厅环境图附件地址
     */
    private String buildingElevatorPic;

    /**
     * 闸口图附件地址
     */
    private String buildingGatePic;

    /**
     * 安装示意图附件地址
     */
    private String buildingInstallationPic;

    /**
     * 是否删除: 0否,1是
     */
    @TableLogic
    @TableField("delete_flag")
    private Integer deleteFlag;
}

package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.module.building.dao.CompleteBuildingScreenMapper;
import com.coocaa.meht.module.building.entity.CompleteBuildingScreenEntity;
import com.coocaa.meht.module.building.service.CompleteBuildingScreenService;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompleteBuildingScreenServiceImpl extends ServiceImpl<CompleteBuildingScreenMapper, CompleteBuildingScreenEntity> implements CompleteBuildingScreenService {

    private final LargeScreenProperties largeScreenProperties;

    @Override
    public void saveOrUpdate(String completeRatingNo, RatingApplyDto applyDto, String submitCoefficient) {
        CompleteBuildingScreenEntity completeBuildingScreen = this.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, completeRatingNo)
                .one();
        CompleteBuildingScreenEntity entity = new CompleteBuildingScreenEntity();
        entity.setBuildingSpacing(applyDto.getBuildingSpacing());
        entity.setCompleteRatingNo(completeRatingNo);
        entity.setBuildingCeilingHeight(applyDto.getBuildingCeilingHeight());
        entity.setTotalBuildingCount(applyDto.getTotalBuildingCount());
        entity.setCompanyCount(applyDto.getCompanyCount());
        entity.setElevatorCount(applyDto.getElevatorCount());
        if (StringUtils.isNotBlank(submitCoefficient)){
            entity.setSubmitCoefficient(new BigDecimal(submitCoefficient));
        }
        entity.setSpecialDesc(applyDto.getSpecialDesc());
        entity.setSpec(applyDto.getSpec());
        if (Objects.nonNull(completeBuildingScreen)) {
            entity.setId(completeBuildingScreen.getId());
            this.updateById(entity);
        } else {
            this.save(entity);

        }

    }

    public Boolean isScreenLarge(CompleteBuildingScreenEntity entity) {
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getSpec())) {
            return false;
        }
        List<String> codeList = JSON.parseArray(entity.getSpec(), String.class);
        List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();
        return CollUtil.isNotEmpty(CollectionUtil.intersection(largeDeviceKey, codeList));
    }
}

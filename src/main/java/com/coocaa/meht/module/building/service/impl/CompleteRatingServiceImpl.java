package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.facade.ApprovalCenterFacade;
import com.coocaa.meht.module.approve.service.ApprovalQueryService;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategyFactory;
import com.coocaa.meht.module.building.convert.BuildingDetailsConvert;
import com.coocaa.meht.module.building.convert.BuildingGeneConvert;
import com.coocaa.meht.module.building.convert.BuildingScreenConvert;
import com.coocaa.meht.module.building.convert.CompleteRatingConvert;
import com.coocaa.meht.module.building.dao.CompleteRatingMapper;
import com.coocaa.meht.module.building.entity.CompleteBuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingDetailEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.enums.LargeScreenFlagEnum;
import com.coocaa.meht.module.building.handler.AiRatingHandler;
import com.coocaa.meht.module.building.param.TransferAndDropParam;
import com.coocaa.meht.module.building.service.CommonAttachmentService;
import com.coocaa.meht.module.building.service.CompleteBuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingDetailService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.building.vo.BuildingDetailsVO;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.building.vo.SysFileVO;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.tctask.CalculateResultDTO;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.module.web.enums.BusinessTypeEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.OperateLogService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.LargeScreenCalculator;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompleteRatingServiceImpl extends ServiceImpl<CompleteRatingMapper, CompleteRatingEntity>
        implements CompleteRatingService {

    private final BuildingRatingService buildingRatingService;

    private final RsaExample rsaExample;

    private final SysFileService sysFileService;

    private final BuildingDetailsService buildingDetailsService;

    private final BuildingGeneService buildingGeneService;

    private final CodeGenerator codeGenerator;

    private final LargeScreenProperties largeScreenProperties;

    private final OperateLogService operateLogService;

    private final CompleteRatingDetailService completeRatingDetailService;

    private final LargeScreenCalculator largeScreenCalculator;

    private final CompleteBuildingScreenService completeBuildingScreenService;

    private final BuildingSnapshotService buildingSnapshotService;

    private final ApprovalQueryService approvalQueryService;

    private final BuildingParameterService buildingParameterService;

    private final CodeNameHelper codeNameHelper;

    private final SysUserService userService;

    private final AiRatingHandler aiRatingHandler;

    private final CommonAttachmentService commonAttachmentService;

    @Lazy
    @Autowired
    private ApprovalStrategyFactory strategyFactory;

    @Lazy
    @Autowired
    private ApprovalCenterFacade approvalCenterFacade;

    @Value("${parameter.max.dataFlag}")
    private Integer dataFlag;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> saveDraft(RatingApplyDto ratingApplyDto) {
        log.info("保存草稿参数：{}", JSON.toJSONString(ratingApplyDto));

        boolean completeFlag = StringUtils.isNotBlank(ratingApplyDto.getCompleteRatingNo());

        BuildingRatingEntity buildingRating = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, ratingApplyDto.getBuildingNo())
                .one();

        CompleteRatingEntity completeRating = this.lambdaQuery()
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue())
                .eq(CompleteRatingEntity::getBuildingRatingNo, ratingApplyDto.getBuildingNo())
                .eq(StringUtils.isNotBlank(ratingApplyDto.getCompleteRatingNo()), CompleteRatingEntity::getCompleteRatingNo,
                        ratingApplyDto.getCompleteRatingNo())
                .one();

        try {
            //生成新数据
            CompleteRatingEntity completeRatingEntity = new CompleteRatingEntity();
            completeRatingEntity.setBuildingRatingNo(ratingApplyDto.getBuildingNo());
            completeRatingEntity.setBuildingName(ratingApplyDto.getBuildingName());
            completeRatingEntity.setBuildingType(ratingApplyDto.getBuildingType());
            completeRatingEntity.setBuildingScore(ratingApplyDto.getBuildingScore());
            completeRatingEntity.setProjectLevel(ratingApplyDto.getProjectLevel());
            completeRatingEntity.setMapProvince(ratingApplyDto.getMapProvince());
            completeRatingEntity.setMapCity(ratingApplyDto.getMapCity());
            completeRatingEntity.setMapRegion(ratingApplyDto.getMapRegion());
            completeRatingEntity.setMapAddress(rsaExample.encryptByPublic(ratingApplyDto.getMapAddress()));
            completeRatingEntity.setStatus(BuildingRatingEntity.Status.DRAFT.getValue());
            completeRatingEntity.setSubmitUser(SecurityUser.getUser().getUserCode());

            //图片处理
            processPictures(ratingApplyDto, completeRatingEntity);

            //是否大屏
            boolean largeScreen = isLargeScreen(ratingApplyDto);

            if (largeScreen && buildingRating.getSmallScreenRatingFlag().equals(BooleFlagEnum.YES.getCode())) {
                completeRatingEntity.setLargeScreenFlag(LargeScreenFlagEnum.SCREEN_FLAG_LARGE.getCode());
            } else if (largeScreen && buildingRating.getLargeScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())
                    && buildingRating.getSmallScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())) {
                completeRatingEntity.setLargeScreenFlag(LargeScreenFlagEnum.SCREEN_FLAG_BOTH.getCode());
            }

            //处理编码
            String completeRatingNo;
            if (StrUtil.isBlank(ratingApplyDto.getCompleteRatingNo())) {
                completeRatingNo = getCompleteRatingNo(ratingApplyDto.getBuildingNo());
                completeRatingEntity.setCompleteRatingNo(completeRatingNo);
            } else {
                completeRatingNo = ratingApplyDto.getCompleteRatingNo();
            }
            completeRatingEntity.setCompleteRatingNo(completeRatingNo);


            //保存基因数据
            BuildingGeneEntity buildingGene = buildingGeneService.saveOrUpdate(buildingRating.getBuildingNo(), ratingApplyDto);
            BuildingGeneVO buildingGeneVO = BuildingGeneConvert.INSTANCE.toBuildingGeneVO(buildingGene);
            translate(buildingGeneVO);

            //处理草稿文本
            ratingApplyDto.setCompleteRatingNo(completeRatingNo);
            completeRatingEntity.setDraftText(draftText(ratingApplyDto, buildingGeneVO));
            if (completeFlag) {
                completeRatingEntity.setId(completeRating.getId());
                this.updateById(completeRatingEntity);
            } else {
                this.save(completeRatingEntity);
            }

            // 处理其他附件
            processOtherAttachments(ratingApplyDto, completeRatingEntity);

            return Result.ok(completeRatingNo);
        } catch (Exception e) {
            log.error("保存完善楼宇信息失败", e);
            throw new RuntimeException("保存完善楼宇信息失败", e);
        }

    }

    /**
     * 处理其他附件
     *
     */
    private void processOtherAttachments(RatingApplyDto ratingApplyDto, CompleteRatingEntity completeRatingEntity) {
        commonAttachmentService.save(BusinessTypeEnum.COMPLETE_RATING.getValue(),
                BuildingMetaImgTypeEnum.OTHER_ATTACHMENTS.getType().toString(),
                completeRatingEntity.getId(),
                completeRatingEntity.getCompleteRatingNo(),
                completeRatingEntity.getVersion(),
                ratingApplyDto.getOtherAttachments());
    }


    /**
     * 处理草稿文本
     */
    private String draftText(RatingApplyDto ratingApplyDto, BuildingGeneVO buildingGeneVO) {
        RatingVO ratingVO = CompleteRatingConvert.INSTANCE.toRatingVO(ratingApplyDto);

        ratingVO.setStatus(BuildingRatingEntity.Status.DRAFT.getValue());

        // 外墙材料附件地址
        ratingVO.setBuildingExteriorPics(fillPic(picConvert(ratingApplyDto.getBuildingExteriorPics())));
        // 楼盘大堂附件地址
        ratingVO.setBuildingLobbyPics(fillPic(picConvert(ratingApplyDto.getBuildingLobbyPics())));
        // 侯梯厅附件地址
        ratingVO.setBuildingHallPics(fillPic(picConvert(ratingApplyDto.getBuildingHallPics())));
        // 大堂环境图附件地址
        ratingVO.setBuildingLobbyEnvPics(fillPic(picConvert(ratingApplyDto.getBuildingLobbyEnvPics())));
        // 梯厅环境图附件地址
        ratingVO.setBuildingElevatorPics(fillPic(picConvert(ratingApplyDto.getBuildingElevatorPics())));
        // 闸口图附件地址
        ratingVO.setBuildingGatePics(fillPic(picConvert(ratingApplyDto.getBuildingGatePics())));
        // 安装示意图附件地址
        ratingVO.setBuildingInstallationPics(fillPic(picConvert(ratingApplyDto.getBuildingInstallationPics())));

        //名称转转换
        ratingVO.setBuildingTypeName(getBuildingTypeName(ratingVO.getBuildingType()));

        //处理详情
        BuildingDetailsVO buildingDetailsVOByApplyDto = BuildingDetailsConvert.INSTANCE
                .toBuildingDetailsVOByApplyDto(ratingApplyDto);
        ratingVO.setBuildingDetailsVO(buildingDetailsVOByApplyDto);

        //处理大屏
        BuildingScreenVO buildingScreenVOByApplyDto = BuildingScreenConvert.INSTANCE.toBuildingScreenVOByApplyDto(ratingApplyDto);
        processSpec(buildingScreenVOByApplyDto);
        ratingVO.setBuildingScreenVO(buildingScreenVOByApplyDto);

        ratingVO.setCreateUserName(SecurityUser.getUser().getUserName());
        ratingVO.setCreateUserCode(SecurityUser.getUser().getUserCode());
        ratingVO.setSubmitUser(SecurityUser.getUser().getUserCode());
        ratingVO.setSubmitUserName(SecurityUser.getUser().getUserName());

        //大屏标识
        CompleteBuildingScreenEntity completeBuildingScreen = new CompleteBuildingScreenEntity();
        completeBuildingScreen.setSpec(ratingApplyDto.getSpec());
        Boolean screenLarge = completeBuildingScreenService.isScreenLarge(completeBuildingScreen);
        ratingVO.setLargeScreen(screenLarge);

        //基因数据
        ratingVO.setBuildingGeneVO(buildingGeneVO);

        //将ratingVO转换成json字符串
        return JSON.toJSONString(ratingVO);

    }

    private String picConvert(List<String> pic) {
        if (CollectionUtil.isNotEmpty(pic)) {
            List<SysFileEntity> sysFileEntities = sysFileService.getByUrls(pic);
            //将sysFileEntities的id通过，拼成字符串
            return sysFileEntities.stream().map(SysFileEntity::getId).map(String::valueOf).collect(Collectors.joining(","));
        }
        return null;

    }


    private String getCompleteRatingNo(String buildingNo) {
        return codeGenerator.getCompleteCode(buildingNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> submitComplete(RatingApplyDto applyDto) {
        log.info("提交完善评级数据:{}", JSON.toJSONString(applyDto));
        boolean isSave = StringUtils.isBlank(applyDto.getCompleteRatingNo());

        // 查询楼宇评级数据
        BuildingRatingEntity existRatingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, applyDto.getBuildingNo())
                .last("limit 1")
                .one();

        // 是否需要AI评级标识
        boolean needAiRating = existRatingEntity.getSmallScreenRatingFlag().equals(BooleFlagEnum.NO.getCode());

        // 是否大屏
        boolean largeScreen = isLargeScreen(applyDto);

        // 校验
        checkComplete(applyDto, existRatingEntity, largeScreen);

        // 填充完善评级数据
        CompleteRatingEntity completeRatingEntity = new CompleteRatingEntity();
        CompleteRatingDetailEntity completeRatingDetailEntity = fillCompleteRatingAndDetailData(largeScreen, existRatingEntity,
                applyDto, completeRatingEntity);

        // 保存或更新楼宇评级
        saveOrUpdateComplete(completeRatingEntity, isSave);

        // 评级详情
        saveOrUpdateCompleteDetail(completeRatingDetailEntity);

        // 大屏提交系数
        String submitCoefficient = completeCalculateSubmitCoefficient(applyDto, largeScreen);

        // 保存或更新BuildingScreen对象
        completeBuildingScreenService.saveOrUpdate(completeRatingEntity.getCompleteRatingNo(), applyDto, submitCoefficient);

        // 保存或更新楼宇基因数据
        buildingGeneService.saveOrUpdate(existRatingEntity.getBuildingNo(), applyDto);

        //修改rating
        buildingRatingService.updateById(existRatingEntity);

        if (needAiRating) {
            // 异步处理AI评级，再提交审批
            aiRatingHandler.aiRatingAfterCommit(AiRatingHandler.Type.COMPLETE_RATING,
                    completeRatingEntity.getCompleteRatingNo(), UserThreadLocal.getUser().getWno());
        } else {
            // 提交审批
            submitApproval(completeRatingEntity, UserThreadLocal.getUser().getWno());
        }

        // 处理其他附件
        processOtherAttachments(applyDto, completeRatingEntity);

        return Result.ok();
    }

    @Override
    public void submitApproval(String completeRatingNo, String operatorWno) {
        CompleteRatingEntity completeRatingEntity = lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(completeRatingEntity)) {
            log.warn("完善评级不存在: {}", completeRatingNo);
            return;
        }

        submitApproval(completeRatingEntity, operatorWno);
    }

    @Override
    public void revokeUnsubmitted(String bizCode) {
        CompleteRatingEntity completeRatingEntity = lambdaQuery()
                .eq(CompleteRatingEntity::getBuildingRatingNo, bizCode)
                .one();
        if (Objects.isNull(completeRatingEntity)) {
            return;
        }

        if (approvalCenterFacade.isTaskExist(bizCode, completeRatingEntity.getVersion(),
                ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL)) {
            // 已经发起了审批流，不能撤回
            return;
        }

        ApprovalStrategy strategy = strategyFactory.getStrategy(ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode());
        strategy.onRevoke(bizCode, null);
    }

    private void submitApproval(CompleteRatingEntity completeRatingEntity, String operatorWno) {
        ApprovalDTO approvalDTO = ApprovalDTO.builder()
                .approvalType(ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode())
                .businessKey(completeRatingEntity.getCompleteRatingNo())
                .version(completeRatingEntity.getVersion())
                .submitter(operatorWno)
                .build();
        approvalCenterFacade.submitApproval(approvalDTO);
    }

    private void checkComplete(RatingApplyDto applyDto, BuildingRatingEntity existRatingEntity, boolean largeScreen) {
        if (StrUtil.isBlank(applyDto.getBuildingNo())) {
            throw new ServerException("楼宇编号不能为空");
        }

        if (Objects.isNull(existRatingEntity)) {
            throw new ServerException("楼宇评级数据不存在");
        }

        //完善大屏
        if (!largeScreen
                && existRatingEntity.getSmallScreenRatingFlag().equals(1)
                && applyDto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())) {
            throw new ServerException("该写字楼已完善小屏，请选择大屏规格完善");
        }

        if (applyDto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && !largeScreen
                && existRatingEntity.getSmallScreenRatingFlag().equals(1)) {
            throw new ServerException("写字楼已完善过小屏");
        }

        if (applyDto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && largeScreen
                && existRatingEntity.getLargeScreenRatingFlag().equals(1)) {
            throw new ServerException("写字楼已完善过大屏");
        }

        if (!applyDto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && existRatingEntity.getSmallScreenRatingFlag().equals(1)) {
            throw new ServerException("非写字楼已完善过小屏");
        }


        CompleteRatingEntity completeRatingEntity = this.lambdaQuery()
                .select(CompleteRatingEntity::getId)
                .eq(CompleteRatingEntity::getCompleteRatingNo, applyDto.getCompleteRatingNo())
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .one();

        if (Objects.nonNull(completeRatingEntity)) {
            throw new ServerException("该完善已存在");
        }

        if (!existRatingEntity.getBuildingType().equals(applyDto.getBuildingType())) {
            throw new ServerException("楼宇类型不一致");
        }
    }

    @Override
    public RatingVO getCompleteInfo(String completeRatingNo, String version) {

        RatingVO ratingVO = new RatingVO();
        CompleteRatingEntity completeRating = this.lambdaQuery()
                .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingNo)
                .one();

        BuildingSnapshotEntity buildingSnapshot = buildingSnapshotService.lambdaQuery()
                .eq(BuildingSnapshotEntity::getBuildingRatingNo, completeRatingNo)
                .eq(BuildingSnapshotEntity::getRatingVersion, version)
                .last("limit 1")
                .one();

        if (Objects.isNull(completeRating)) {
            throw new ServerException("完善楼宇评级数据不存在");
        }

        //草稿详情
        if (completeRating.getStatus().equals(BuildingRatingEntity.Status.DRAFT.getValue())
                && StringUtils.isNotBlank(completeRating.getDraftText())) {
            String draftText = completeRating.getDraftText();
            log.info("草稿详情：{}", draftText);
            ratingVO = JSON.parseObject(draftText, RatingVO.class);
            ratingVO.setStatus(BuildingRatingEntity.Status.DRAFT.getValue());

            //提交人处理
            CompleteRatingEntity completeRatingEntity = this.lambdaQuery()
                    .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingNo)
                    .one();

            submitUser(completeRatingEntity, ratingVO);
            //处理基因
            getBuildingGene(completeRating.getBuildingRatingNo(), ratingVO);

            //审批
            setApproveRecord(completeRating.getCompleteRatingNo(), ratingVO);

            return ratingVO;
        }

        //完善详情
        if (completeRating.getVersion().equals(version)) {
            ratingVO = getCompleteInfoByNo(completeRating);
            //处理基因
            getBuildingGene(completeRating.getBuildingRatingNo(), ratingVO);
            log.info("完善详情：{}", completeRating);
            return ratingVO;
        }

        //快照详情
        if (Objects.nonNull(buildingSnapshot)) {
            ratingVO = getSnapshot(buildingSnapshot);
            //处理基因
            getBuildingGene(completeRating.getBuildingRatingNo(), ratingVO);
            log.info("快照详情：{}", buildingSnapshot);
            return ratingVO;
        }

        return ratingVO;

    }


    private void submitUser(CompleteRatingEntity completeRatingEntity, RatingVO ratingVO) {

        if (StringUtils.isNotBlank(completeRatingEntity.getSubmitUser())) {
            Set<String> submitCode = Set.of(completeRatingEntity.getSubmitUser());
            Map<String, LoginUser> submitCodeUsers = userService.getUsers(submitCode);
            ratingVO.setSubmitUser(completeRatingEntity.getSubmitUser());
            LoginUser loginUser = submitCodeUsers.get(completeRatingEntity.getSubmitUser());
            ratingVO.setSubmitUserName(Objects.nonNull(loginUser) ? loginUser.getUserName() : "");
        }

    }

    private void getBuildingGene(String buildingNo, RatingVO ratingVO) {
        BuildingGeneEntity buildingGeneEntity = buildingGeneService.lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo)
                .one();

        if (Objects.nonNull(buildingGeneEntity)) {
            if (StringUtils.isNotBlank(buildingGeneEntity.getCompetitiveMediaInfo())) {
                buildingGeneEntity.setCompetitiveMediaInfos(List.of(buildingGeneEntity.getCompetitiveMediaInfo().split(",")));
            }


            BuildingGeneVO buildingGeneVO = BuildingGeneConvert.INSTANCE.toBuildingGeneVO(buildingGeneEntity);
            buildingGeneVO.setTargetPointCount(ratingVO.getTargetPointCount());
            translate(buildingGeneVO);
            ratingVO.setBuildingGeneVO(buildingGeneVO);
        }

    }


    private void translate(BuildingGeneVO buildingGene) {
        if (Objects.nonNull(buildingGene)) {
            if (CollectionUtils.isNotEmpty(buildingGene.getCompetitiveMediaInfos())) {
                // 按逗号截取
                Map<String, String> mediaTypeDictMapping = codeNameHelper.getDictMapping(buildingGene.getCompetitiveMediaInfos());
                // 使用String.join方法进行字符串拼接
                String res = String.join("，", mediaTypeDictMapping.values());
                buildingGene.setCompetitiveMediaInfoName(res);
            }

            if (StringUtils.isNotBlank(buildingGene.getForbiddenIndustry())) {
                if (buildingGene.getForbiddenIndustry().trim().equals("无")) {
                    buildingGene.setForbiddenIndustryName("无");
                } else {
                    List<String> list = Arrays.asList(buildingGene.getForbiddenIndustry().split(","));
                    Map<String, String> industryMapping = codeNameHelper.getIndustryMapping(list);
                    // 使用String.join方法进行字符串拼接
                    String res = String.join("，", industryMapping.values());
                    buildingGene.setForbiddenIndustryName(res);
                }

            }
        }
    }


    private void processPictures(RatingApplyDto dto, CompleteRatingEntity newEntity) {
        // 先清空图片
        newEntity.setBuildingExteriorPic("")
                .setBuildingLobbyPic("")
                .setBuildingHallPic("")
                .setBuildingLobbyEnvPic("")
                .setBuildingElevatorPic("")
                .setBuildingGatePic("")
                .setBuildingInstallationPic("");

        // 定义要处理的图片字段映射
        Map<List<String>, Consumer<String>> pictureFieldsMap = new HashMap<>();
        pictureFieldsMap.put(dto.getBuildingExteriorPics(), newEntity::setBuildingExteriorPic);
        pictureFieldsMap.put(dto.getBuildingLobbyPics(), newEntity::setBuildingLobbyPic);
        pictureFieldsMap.put(dto.getBuildingHallPics(), newEntity::setBuildingHallPic);
        pictureFieldsMap.put(dto.getBuildingLobbyEnvPics(), newEntity::setBuildingLobbyEnvPic);
        pictureFieldsMap.put(dto.getBuildingElevatorPics(), newEntity::setBuildingElevatorPic);
        pictureFieldsMap.put(dto.getBuildingGatePics(), newEntity::setBuildingGatePic);
        pictureFieldsMap.put(dto.getBuildingInstallationPics(), newEntity::setBuildingInstallationPic);

        // 批量处理所有图片字段
        pictureFieldsMap.forEach((pictureUrls, setter) -> {
            if (CollectionUtils.isNotEmpty(pictureUrls)) {
                // 从数据库一次查询附件ID
                List<SysFileEntity> sysFileEntities = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                        .in(SysFileEntity::getUrl, pictureUrls));

                // 提取ID并设置到实体中
                if (CollectionUtils.isNotEmpty(sysFileEntities)) {
                    String ids = sysFileEntities.stream()
                            .map(SysFileEntity::getId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    setter.accept(ids);
                }
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraft(String buildingNo) {
        this.lambdaUpdate()
                .set(CompleteRatingEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(CompleteRatingEntity::getCompleteRatingNo, buildingNo)
                .update();
    }

    @Override
    public Map<String, Object> getScoringItem(String completeRatingNo, String version) {
        if (!completeRatingNo.contains("WS")) {
            // 1.7.8以前的完善评级，完善表里没有数据，需要查主表的评分项
            return buildingRatingService.getScoringDetails(completeRatingNo, version);
        }

        log.info("获取完善评级评分项，completeRatingNo：{}，version：{}", completeRatingNo, version);
        CompleteRatingEntity ratingEntity = this.getOne(new LambdaQueryWrapper<CompleteRatingEntity>()
                .select(CompleteRatingEntity::getId,
                        CompleteRatingEntity::getBuildingType,
                        CompleteRatingEntity::getBuildingScore,
                        CompleteRatingEntity::getBuildingAiScore,
                        CompleteRatingEntity::getProjectLevel,
                        CompleteRatingEntity::getProjectAiLevel,
                        CompleteRatingEntity::getDataFlag,
                        CompleteRatingEntity::getVersion)
                .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingNo));
        if (Objects.isNull(ratingEntity)) {
            throw new ServerException("楼宇不存在");
        }

        CompleteRatingDetailEntity details;
        if (StrUtil.isNotBlank(version) && !version.equals(ratingEntity.getVersion())) {
            // 非当前版本数据，需要查询对应版本的数据
            BuildingSnapshotEntity snapshotEntity = buildingSnapshotService.lambdaQuery()
                    .eq(BuildingSnapshotEntity::getBuildingRatingNo, completeRatingNo)
                    .eq(BuildingSnapshotEntity::getRatingVersion, version)
                    .one();
            if (Objects.isNull(snapshotEntity)) {
                throw new ServerException("楼宇快照不存在");
            }
            ratingEntity = JSON.parseObject(snapshotEntity.getRatingSnapshot(), CompleteRatingEntity.class);
            details = JSON.parseObject(snapshotEntity.getDetailsSnapshot(), CompleteRatingDetailEntity.class);
        } else {
            details = completeRatingDetailService.lambdaQuery()
                    .eq(CompleteRatingDetailEntity::getCompleteRatingNo, completeRatingNo)
                    .last("limit 1")
                    .one();
        }

        log.info("获取完善评级评分项，details：{}", details);

        Integer entityDataFlag = ratingEntity.getDataFlag();
        List<BuildingParameterEntity> rules = buildingParameterService.lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, entityDataFlag)
                .eq(BuildingParameterEntity::getBuildingType, ratingEntity.getBuildingType())
                .orderByAsc(BuildingParameterEntity::getSort)
                .list();
        if (CollectionUtils.isEmpty(rules)) {
            return Collections.emptyMap();
        }

        Map<Long, BuildingParameterEntity> parameterMap = new HashMap<>();
        List<BuildingParameterEntity> parentList = rules.stream()
                .peek(ele -> parameterMap.put(ele.getId(), ele))
                .filter(ele -> Objects.equals(0L, ele.getParentId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentList)) {
            return Collections.emptyMap();
        }
        log.info("获取完善评级评分项,parameterMap:{}", parameterMap);
        log.info("获取完善评级评分项，rules：{}", rules);

        return setRules(details, ratingEntity, parentList, parameterMap);
    }

    /**
     *
     * 处理评分项
     */
    private Map<String, Object> setRules(CompleteRatingDetailEntity details,
                                         CompleteRatingEntity ratingEntity,
                                         List<BuildingParameterEntity> parentList,
                                         Map<Long, BuildingParameterEntity> parameterMap) {

        Map<String, Object> mapVo = new HashMap<>();
        List<Map<String, Object>> listVo = new ArrayList<>();
        mapVo.put("thirdBuildingType", details == null ? null : details.getThirdBuildingType());
        mapVo.put("projectLevel", ratingEntity.getProjectLevel());
        mapVo.put("buildingAiScore", ratingEntity.getBuildingAiScore());
        mapVo.put("BuildingType", BuildingRatingEntity.BuildingType.getNameByValue(ratingEntity.getBuildingType()));
        mapVo.put("projectAiLevel", ratingEntity.getProjectAiLevel());
        mapVo.put("totalScore", ratingEntity.getBuildingScore());
        mapVo.put("list", listVo);

        parentList.forEach(ele -> {
            Map<String, Object> map = new HashMap<>();
            if (ele.getParameterName().equals("楼龄") && Objects.nonNull(details) && Objects.nonNull(details.getDeliveryDate())) {
                map.put("parameterName", "交付时间");
            } else {
                map.put("parameterName", ele.getParameterName());
            }

            // 人工评级
            Long code = details == null ? null : details.valueId(ele.getParameterCode());
            BuildingParameterEntity valParameter = parameterMap.get(code);
            if (valParameter != null) {
                map.put("parameterValue", valParameter.getParameterName());
                map.put("thirdValue", details == null ? null : details.thirdValue(ele.getParameterCode()));
                map.put("score", valParameter.getParameterScore());
                map.put("weight", valParameter.getWeightValue());
                map.put("avgScore", valParameter.getParameterScore().multiply(valParameter.getWeightValue())
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));

            }

            // AI评级
            map.put("thirdValue", details == null ? null : details.thirdValue(ele.getParameterCode()));
            Long ruleId = Objects.isNull(details) ? null : details.valueId("third" + ele.getParameterCode() + "id");
            if (Objects.nonNull(ruleId)) {
                BuildingParameterEntity.ScoreAndWeight<BigDecimal> scoreAndWeight = buildingParameterService.getScoreAndWeight(
                        ratingEntity.getDataFlag(), ratingEntity.getBuildingType(), ruleId, ele.getParameterCode());

                BigDecimal aiScore = BigDecimal.ZERO;
                BigDecimal aiAvgScore = BigDecimal.ZERO;
                if (ObjectUtil.isNotNull(scoreAndWeight)) {
                    aiScore = scoreAndWeight.Score();
                    aiAvgScore = scoreAndWeight.Score().multiply(scoreAndWeight.weight())
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                }
                map.put("aiScore", aiScore);
                map.put("aiAvgScore", aiAvgScore);
            }

            listVo.add(map);
        });

        return mapVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferAndDrop(TransferAndDropParam param) {
        if (CollUtil.isEmpty(param.getBuildingNos())) {
            return;
        }

        List<CompleteRatingEntity> completeRatingEntities = this.lambdaQuery()
                .select(CompleteRatingEntity::getId, BaseEntity::getUpdateTime)
                .in(CompleteRatingEntity::getBuildingRatingNo, param.getBuildingNos())
                .list();

        for (CompleteRatingEntity completeRatingEntity : completeRatingEntities) {
            completeRatingEntity.setSubmitUser(param.getUserCode());
            completeRatingEntity.setUpdateTime(completeRatingEntity.getUpdateTime());
        }

        this.updateBatchById(completeRatingEntities);
    }

    @Override
    public RatingVO getCompleteInfoByNo(CompleteRatingEntity completeRating) {
        RatingVO ratingVO = CompleteRatingConvert.INSTANCE.toRatingVOByEntity(completeRating);

        //用户处理
        Set<String> userCode = Set.of(completeRating.getCreateBy());
        Map<String, LoginUser> users = userService.getUsers(userCode);
        LoginUser loginUser = users.get(completeRating.getCreateBy());
        ratingVO.setCreateUserName(Objects.nonNull(loginUser) ? loginUser.getUserName() : "");

        //提交人
        submitUser(completeRating, ratingVO);

        ratingVO.setMapAddress(rsaExample.decryptByPrivate(ratingVO.getMapAddress()));

        //图片处理
        processPicturesForRatingVO(completeRating, ratingVO);

        //详情处理
        CompleteRatingDetailEntity completeRatingDetail = completeRatingDetailService.lambdaQuery()
                .eq(CompleteRatingDetailEntity::getCompleteRatingNo, completeRating.getCompleteRatingNo())
                .one();

        BuildingDetailsVO buildingDetailsVOByEntity = BuildingDetailsConvert.INSTANCE
                .toBuildingDetailsVOByEntity(completeRatingDetail);

        ratingVO.setBuildingDetailsVO(buildingDetailsVOByEntity);

        //大屏处理
        CompleteBuildingScreenEntity completeBuildingScreen = completeBuildingScreenService.lambdaQuery()
                .eq(CompleteBuildingScreenEntity::getCompleteRatingNo, completeRating.getCompleteRatingNo())
                .one();

        if (Objects.nonNull(completeBuildingScreen)) {
            BuildingScreenVO buildingScreenVO = BuildingScreenConvert.INSTANCE.toBuildingScreenVOByEntity(completeBuildingScreen);
            processSpec(buildingScreenVO);
            ratingVO.setBuildingScreenVO(buildingScreenVO);
        }

        //审批
        setApproveRecord(completeRating.getCompleteRatingNo(), ratingVO);

        //基因数据
        BuildingGeneEntity buildingGene = buildingGeneService.lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, completeRating.getBuildingRatingNo())
                .one();

        BuildingGeneVO buildingGeneVO = BuildingGeneConvert.INSTANCE.toBuildingGeneVO(buildingGene);
        ratingVO.setBuildingGeneVO(buildingGeneVO);

        //标识
        Boolean screenLarge = completeBuildingScreenService.isScreenLarge(completeBuildingScreen);
        ratingVO.setLargeScreen(screenLarge);

        return ratingVO;
    }

    @Override
    public void handleAiRating(String completeRatingNo, String operatorWno) {
        log.info("开始处理AI评级: {}", completeRatingNo);

        if (StrUtil.isBlank(completeRatingNo)) {
            log.warn("处理AI评级，参数completeRatingNo为空");
            return;
        }

        CompleteRatingEntity completeRatingEntity = lambdaQuery()
                .select(CompleteRatingEntity::getId,
                        CompleteRatingEntity::getCompleteRatingNo,
                        CompleteRatingEntity::getBuildingRatingNo,
                        CompleteRatingEntity::getVersion,
                        CompleteRatingEntity::getBuildingName,
                        CompleteRatingEntity::getBuildingType,
                        CompleteRatingEntity::getUpdateTime,
                        CompleteRatingEntity::getStatus,
                        CompleteRatingEntity::getMapCity)
                .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(completeRatingEntity)) {
            log.warn("完善评级不存在: {}", completeRatingNo);
            return;
        }

        if (BuildingRatingEntity.Status.WAIT_AUDIT.getValue() != completeRatingEntity.getStatus()) {
            log.warn("完善评级状态不在审核中: {}", completeRatingNo);
            return;
        }

        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getRatingVersion,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getBuildingType,
                        BuildingRatingEntity::getUpdateTime,
                        BuildingRatingEntity::getStatus,
                        BuildingRatingEntity::getMapCity,
                        BuildingRatingEntity::getMapAdCode,
                        BuildingRatingEntity::getMapRegion,
                        BuildingRatingEntity::getMapAddress,
                        BuildingRatingEntity::getMapLatitude,
                        BuildingRatingEntity::getMapLongitude)
                .eq(BuildingRatingEntity::getBuildingNo, completeRatingEntity.getBuildingRatingNo())
                .last("limit 1")
                .one();
        if (Objects.isNull(ratingEntity)) {
            log.warn("楼宇评级不存在: {}", completeRatingEntity.getBuildingRatingNo());
            return;
        }

        CompleteRatingDetailEntity completeRatingDetailEntity = completeRatingDetailService.lambdaQuery()
                .eq(CompleteRatingDetailEntity::getCompleteRatingNo, completeRatingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(completeRatingDetailEntity)) {
            log.warn("完善评级详情不存在: {}", completeRatingNo);
            return;
        }

        // 结果处理
        handleResult(completeRatingDetailEntity, completeRatingEntity, ratingEntity, completeRatingNo);

        // 提交审核
        submitApproval(completeRatingEntity, UserThreadLocal.getUser().getWno());

        log.info("处理AI评级完成: {}", completeRatingNo);
    }

    /**
     * 处理结果
     */
    private void handleResult(CompleteRatingDetailEntity completeRatingDetailEntity,
                              CompleteRatingEntity completeRatingEntity,
                              BuildingRatingEntity ratingEntity,
                              String completeRatingNo) {

        BuildingDetailsEntity detailEntity = BuildingDetailsConvert.INSTANCE.toRatingDetailEntity(completeRatingDetailEntity);
        detailEntity.emptyAiData();
        // AI评级，填充评级数据到detailsEntity
        RatingApplyDto ratingDto = new RatingApplyDto()
                .setMapCity(completeRatingEntity.getMapCity())
                .setBuildingName(completeRatingEntity.getBuildingName())
                .setBuildingType(completeRatingEntity.getBuildingType())
                .setMapAdCode(ratingEntity.getMapAdCode())
                .setBuildingNo(ratingEntity.getBuildingNo())
                .setMapRegion(ratingEntity.getMapRegion());
        try {
            ratingDto.setMapLatitude(rsaExample.decryptByPrivate(ratingEntity.getMapLatitude()))
                    .setMapLongitude(rsaExample.decryptByPrivate(ratingEntity.getMapLongitude()))
                    .setMapAddress(rsaExample.decryptByPrivate(ratingEntity.getMapAddress()));
        } catch (Exception e) {
            log.warn("AI楼宇评级，解密数据异常，{}", completeRatingNo, e);
        }

        CalculateResultDTO result = buildingRatingService.aiRating(ratingDto, detailEntity);

        if (Objects.nonNull(result)) {
            // 设置AI评分，评级
            completeRatingEntity.setBuildingAiScore(result.getBuildingAiScore());
            completeRatingEntity.setProjectAiLevel(result.getProjectAiLevel());
        } else {
            // 置空AI评分，评级
            completeRatingEntity.setBuildingAiScore(BigDecimal.ZERO);
            completeRatingEntity.setProjectAiLevel("");
        }
        completeRatingEntity.setUpdateTime(completeRatingEntity.getUpdateTime());
        updateById(completeRatingEntity);

        // 回填AI评级数据，楼宇类型
        completeRatingDetailEntity = BuildingDetailsConvert.INSTANCE.toCompleteRatingDetailEntity(detailEntity);
        completeRatingDetailEntity.setCompleteRatingNo(completeRatingNo);
        completeRatingDetailService.updateById(completeRatingDetailEntity);
        if (Objects.isNull(result) || Objects.isNull(completeRatingDetailEntity.getCreDailyPrice())) {
            // 置空中国房价行情网日租金
            LambdaUpdateWrapper<CompleteRatingDetailEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CompleteRatingDetailEntity::getId, completeRatingDetailEntity.getId());
            updateWrapper.set(CompleteRatingDetailEntity::getCreDailyPrice, null);
            completeRatingDetailService.update(updateWrapper);
        }
    }


    private void setApproveRecord(String completeRatingNo, RatingVO ratingVO) {
        Result<List<ScreenApproveRecordDTO>> screenApproveRecordResult = approvalQueryService.queryLocalNodes(completeRatingNo);
        if (Objects.nonNull(screenApproveRecordResult) && Objects.nonNull(screenApproveRecordResult.getData())) {
            List<ScreenApproveRecordDTO> data = screenApproveRecordResult.getData();
            ratingVO.setApprovalDetailVO(data);
        }
    }

    private void processPicturesForRatingVO(CompleteRatingEntity ratingEntity, RatingVO ratingVO) {
        ratingVO.setBuildingExteriorPics(fillPic(ratingEntity.getBuildingExteriorPic()));
        ratingVO.setBuildingLobbyPics(fillPic(ratingEntity.getBuildingLobbyPic()));
        ratingVO.setBuildingHallPics(fillPic(ratingEntity.getBuildingHallPic()));
        ratingVO.setBuildingLobbyEnvPics(fillPic(ratingEntity.getBuildingLobbyEnvPic()));
        ratingVO.setBuildingElevatorPics(fillPic(ratingEntity.getBuildingElevatorPic()));
        ratingVO.setBuildingGatePics(fillPic(ratingEntity.getBuildingGatePic()));
        ratingVO.setBuildingInstallationPics(fillPic(ratingEntity.getBuildingInstallationPic()));

    }

    private void processSpec(BuildingScreenVO buildingScreenVO) {
        if (StrUtil.isNotBlank(buildingScreenVO.getSpec())) {
            List<String> dicCodes = JSON.parseArray(buildingScreenVO.getSpec(), String.class);
            Map<String, String> dictMapping = codeNameHelper.getDictMapping(dicCodes);
            List<CodeNameVO> codeNameVOS = dictMapping.entrySet().stream()
                    .map(entry -> new CodeNameVO(null, entry.getKey(), entry.getValue(), null, null))
                    .toList();
            buildingScreenVO.setSpec(JSON.toJSONString(codeNameVOS));
            buildingScreenVO.setSpecName(String.join(",", dictMapping.values()));
        }
    }

    @SuppressWarnings("checkstyle:WhitespaceAfter")
    private RatingVO getSnapshot(BuildingSnapshotEntity buildingSnapshot) {
        String ratingSnapshot = buildingSnapshot.getRatingSnapshot();
        RatingVO ratingVO = null;

        String buildingRatingNo = "";
        if (StringUtils.isNotBlank(ratingSnapshot)) {
            CompleteRatingEntity completeRating = JSON.parseObject(ratingSnapshot, CompleteRatingEntity.class);
            ratingVO = CompleteRatingConvert.INSTANCE.toRatingVOByEntity(completeRating);

            //用户处理
            Set<String> userCode = Set.of(completeRating.getCreateBy());
            Map<String, LoginUser> users = userService.getUsers(userCode);
            LoginUser loginUser = users.get(completeRating.getCreateBy());
            ratingVO.setCreateUserName(Objects.nonNull(loginUser) ? loginUser.getUserName() : "");

            //提交人
            Set<String> submitCode = Set.of(completeRating.getSubmitUser());
            Map<String, LoginUser> submitCodeUsers = userService.getUsers(submitCode);
            LoginUser submitUser = submitCodeUsers.get(completeRating.getSubmitUser());
            ratingVO.setSubmitUserName(Objects.nonNull(submitUser) ? submitUser.getUserName() : "");

            // 外墙材料附件地址
            ratingVO.setBuildingExteriorPics(fillPic(completeRating.getBuildingExteriorPic()));
            // 楼盘大堂附件地址
            ratingVO.setBuildingLobbyPics(fillPic(completeRating.getBuildingLobbyPic()));
            // 侯梯厅附件地址
            ratingVO.setBuildingHallPics(fillPic(completeRating.getBuildingHallPic()));
            // 大堂环境图附件地址
            ratingVO.setBuildingLobbyEnvPics(fillPic(completeRating.getBuildingLobbyEnvPic()));
            // 梯厅环境图附件地址
            ratingVO.setBuildingElevatorPics(fillPic(completeRating.getBuildingElevatorPic()));
            // 闸口图附件地址
            ratingVO.setBuildingGatePics(fillPic(completeRating.getBuildingGatePic()));
            // 安装示意图附件地址
            ratingVO.setBuildingInstallationPics(fillPic(completeRating.getBuildingInstallationPic()));


            //审批
            setApproveRecord(completeRating.getCompleteRatingNo(), ratingVO);

            buildingRatingNo = completeRating.getBuildingRatingNo();

        }

        if (StringUtils.isNotBlank(buildingSnapshot.getDetailsSnapshot())) {
            //详情处理
            CompleteRatingDetailEntity completeRatingDetail = JSON.parseObject(buildingSnapshot.getDetailsSnapshot(),
                    CompleteRatingDetailEntity.class);
            BuildingDetailsVO buildingDetailsVO = BuildingDetailsConvert.INSTANCE.
                    toBuildingDetailsVOByEntity(completeRatingDetail);
            assert ratingVO != null;
            ratingVO.setBuildingDetailsVO(buildingDetailsVO);
        }

        //大屏处理
        if (Objects.nonNull(buildingSnapshot.getScreenSnapshot())) {
            CompleteBuildingScreenEntity completeBuildingScreen = JSON.parseObject(buildingSnapshot.getScreenSnapshot(),
                    CompleteBuildingScreenEntity.class);
            BuildingScreenVO buildingScreenVO = BuildingScreenConvert.INSTANCE.toBuildingScreenVOByEntity(completeBuildingScreen);
            processSpec(buildingScreenVO);
            assert ratingVO != null;
            ratingVO.setBuildingScreenVO(buildingScreenVO);
            ratingVO.setLargeScreen(completeBuildingScreenService.isScreenLarge(completeBuildingScreen));
        }

        //基因数据
        BuildingGeneEntity geneEntity = buildingGeneService.lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, buildingRatingNo)
                .one();
        ratingVO.setBuildingGeneVO(BuildingGeneConvert.INSTANCE.toBuildingGeneVO(geneEntity));

        ratingVO.setMapAddress(rsaExample.decryptByPrivate(ratingVO.getMapAddress()));
        return ratingVO;
    }

    /**
     * 大屏试算
     *
     */
    private String completeCalculateSubmitCoefficient(RatingApplyDto param, boolean isLargeScreen) {
        if (!isLargeScreen) {
            return null;
        }
        //楼龄处理
        int years = buildingDetailsService.getYears(param.getDeliveryDate());
        param.setBuildingAgeInput(String.valueOf(years));
        BigScreenCalculateDTO bigScreenCalculateDTO = new BigScreenCalculateDTO();
        bigScreenCalculateDTO.setBuildingCeilingHeight(param.getBuildingCeilingHeight());
        bigScreenCalculateDTO.setBuildingNumberInput(param.getBuildingNumberInput());
        bigScreenCalculateDTO.setBuildingSpacing(param.getBuildingSpacing());
        bigScreenCalculateDTO.setBuildingAgeInput(param.getBuildingAgeInput());
        bigScreenCalculateDTO.setBuildingLocationText(param.getLocationName());
        return largeScreenCalculator.calculate(bigScreenCalculateDTO);
    }

    private void saveOrUpdateComplete(CompleteRatingEntity completeRatingEntity, boolean isSave) {
        completeRatingEntity.setSubmitUser(SecurityUser.getUserCode());
        completeRatingEntity.setMapAddress(rsaExample.encryptByPublic(completeRatingEntity.getMapAddress()));
        if (isSave) {
            this.save(completeRatingEntity);
        } else {
            CompleteRatingEntity completeRating = this.lambdaQuery()
                    .eq(CompleteRatingEntity::getBuildingRatingNo, completeRatingEntity.getBuildingRatingNo())
                    .eq(CompleteRatingEntity::getCompleteRatingNo, completeRatingEntity.getCompleteRatingNo())
                    .one();

            completeRatingEntity.setId(completeRating.getId());
            completeRatingEntity.setUpdateTime(LocalDateTime.now());
            this.updateById(completeRatingEntity);
        }
    }

    private void saveOrUpdateCompleteDetail(CompleteRatingDetailEntity completeRatingDetailEntity) {
        CompleteRatingDetailEntity completeRatingDetail = completeRatingDetailService.lambdaQuery()
                .eq(CompleteRatingDetailEntity::getCompleteRatingNo, completeRatingDetailEntity.getCompleteRatingNo())
                .one();
        if (Objects.nonNull(completeRatingDetail)) {
            completeRatingDetailEntity.setId(completeRatingDetail.getId());
            completeRatingDetailEntity.setUpdateTime(LocalDateTime.now());
            completeRatingDetailService.updateById(completeRatingDetailEntity);
        } else {
            completeRatingDetailEntity.setId(null);
            completeRatingDetailService.save(completeRatingDetailEntity);
        }
    }

    /**
     * 完整评级填充
     *
     */
    private CompleteRatingDetailEntity fillCompleteRatingAndDetailData(boolean largeScreen,
                                                                       BuildingRatingEntity existRatingEntity,
                                                                       RatingApplyDto applyDto,
                                                                       CompleteRatingEntity completeRatingEntity) {

        String completeRatingNo;
        if (StringUtils.isNotBlank(applyDto.getCompleteRatingNo())) {
            completeRatingNo = applyDto.getCompleteRatingNo();
        } else {
            completeRatingNo = getCompleteRatingNo(existRatingEntity.getBuildingNo());
        }

        //生成新数据
        completeRatingEntity.setBuildingRatingNo(applyDto.getBuildingNo());
        completeRatingEntity.setTargetPointCount(applyDto.getTargetPointCount());
        completeRatingEntity.setBuildingName(applyDto.getBuildingName());
        completeRatingEntity.setBuildingType(applyDto.getBuildingType());
        completeRatingEntity.setMapProvince(applyDto.getMapProvince());
        completeRatingEntity.setMapCity(applyDto.getMapCity());
        completeRatingEntity.setMapRegion(applyDto.getMapRegion());
        completeRatingEntity.setMapAddress(applyDto.getMapAddress());
        completeRatingEntity.setStatus(BuildingRatingEntity.Status.WAIT_AUDIT.getValue());
        completeRatingEntity.setVersion(codeGenerator.generateRatingVersion());

        completeRatingEntity.setSubmitTime(LocalDateTime.now());

        //图片处理
        processPictures(applyDto, completeRatingEntity);

        //处理编码
        if (StrUtil.isBlank(applyDto.getCompleteRatingNo())) {
            completeRatingEntity.setCompleteRatingNo(getCompleteRatingNo(applyDto.getBuildingNo()));
        } else {
            completeRatingEntity.setCompleteRatingNo(applyDto.getCompleteRatingNo());
        }

        BuildingDetailsEntity detailsEntity = buildingDetailsService.createDetailsEntity(applyDto);

        if (existRatingEntity.getSmallScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())) {

            smallScreen(applyDto, detailsEntity, completeRatingNo, completeRatingEntity);

        } else {

            detailsEntity = largeScreen(existRatingEntity, completeRatingNo, completeRatingEntity);
        }

        //是否大屏
        if (largeScreen && existRatingEntity.getSmallScreenRatingFlag().equals(BooleFlagEnum.YES.getCode())) {
            completeRatingEntity.setLargeScreenFlag(LargeScreenFlagEnum.SCREEN_FLAG_LARGE.getCode());
            existRatingEntity.setLargeScreenRatingFlag(BooleFlagEnum.YES.getCode());
        } else if (largeScreen && existRatingEntity.getLargeScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())
                && existRatingEntity.getSmallScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())) {
            completeRatingEntity.setLargeScreenFlag(LargeScreenFlagEnum.SCREEN_FLAG_BOTH.getCode());
            existRatingEntity.setLargeScreenRatingFlag(BooleFlagEnum.YES.getCode());
            existRatingEntity.setSmallScreenRatingFlag(BooleFlagEnum.YES.getCode());
        } else if (!largeScreen && existRatingEntity.getSmallScreenRatingFlag().equals(BooleFlagEnum.NO.getCode())) {
            existRatingEntity.setSmallScreenRatingFlag(BooleFlagEnum.YES.getCode());
        }
        //主数据时间
        existRatingEntity.setUpdateTime(existRatingEntity.getUpdateTime());

        CompleteRatingDetailEntity completeRatingDetailEntity = BuildingDetailsConvert.INSTANCE
                .toCompleteRatingDetailEntity(detailsEntity);
        completeRatingDetailEntity.setCompleteRatingNo(completeRatingEntity.getCompleteRatingNo());


        return completeRatingDetailEntity;
    }

    /**
     * 小屏评级处理
     */
    private void smallScreen(RatingApplyDto applyDto,
                             BuildingDetailsEntity detailsEntity,
                             String completeRatingNo,
                             CompleteRatingEntity completeRatingEntity) {
        // 人工评级
        CalculateResultDTO manualRatingResult = buildingRatingService.manualRating(applyDto, detailsEntity);
        // 人工评级达标判断
        String projectLevel = manualRatingResult.getProjectLevel();

        if (!Arrays.asList("AAA", "AA", "A").contains(projectLevel)) {
            operateLogService.saveOperateLog(BusinessTypeEnum.COMPLETE_RATING.getValue(),
                    completeRatingNo,
                    "当前完善评级未达到A级",
                    "提交", "");

            completeRatingEntity.setProjectReviewLevel("A");
        }

        completeRatingEntity.setDataFlag(dataFlag);
        completeRatingEntity.setProjectLevel(projectLevel);
        completeRatingEntity.setBuildingScore(manualRatingResult.getBuildingScore());
        completeRatingEntity.setFirstFloorExclusive(manualRatingResult.getFirstFloorExclusiveScore());
        completeRatingEntity.setFirstFloorShare(manualRatingResult.getFirstFloorShareScore());
        completeRatingEntity.setNegativeFirstFloor(manualRatingResult.getNegativeFirstFloorScore());
        completeRatingEntity.setNegativeSecondFloor(manualRatingResult.getNegativeTwoFloorScore());
        completeRatingEntity.setSecondFloorAbove(manualRatingResult.getTwoFloorAboveScore());
        completeRatingEntity.setThirdFloorBelow(manualRatingResult.getThirdFloorBelowScore());
    }

    /**
     * 大屏评级处理
     */
    private BuildingDetailsEntity largeScreen(BuildingRatingEntity existRatingEntity,
                                              String completeRatingNo,
                                              CompleteRatingEntity completeRatingEntity) {
        // 人工评级达标判断
        String projectLevel = existRatingEntity.getProjectLevel();

        if (!Arrays.asList("AAA", "AA", "A").contains(projectLevel)) {
            operateLogService.saveOperateLog(BusinessTypeEnum.COMPLETE_RATING.getValue(),
                    completeRatingNo, "当前完善评级未达到A级", "提交", "");
        }

        completeRatingEntity.setDataFlag(existRatingEntity.getDataFlag());
        // AI评级及人工评分
        completeRatingEntity.setProjectLevel(projectLevel);
        completeRatingEntity.setBuildingScore(existRatingEntity.getBuildingScore());
        completeRatingEntity.setProjectAiLevel(existRatingEntity.getProjectAiLevel());
        completeRatingEntity.setBuildingAiScore(existRatingEntity.getBuildingAiScore());
        completeRatingEntity.setProjectReviewLevel(existRatingEntity.getProjectReviewLevel());
        completeRatingEntity.setFirstFloorExclusive(existRatingEntity.getFirstFloorExclusive());
        completeRatingEntity.setFirstFloorShare(existRatingEntity.getFirstFloorShare());
        completeRatingEntity.setNegativeFirstFloor(existRatingEntity.getNegativeFirstFloor());
        completeRatingEntity.setNegativeSecondFloor(existRatingEntity.getNegativeTwoFloor());
        completeRatingEntity.setSecondFloorAbove(existRatingEntity.getTwoFloorAbove());
        completeRatingEntity.setThirdFloorBelow(existRatingEntity.getThirdFloorBelow());

        //评级详情
        return buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, existRatingEntity.getBuildingNo())
                .one();
    }

    private boolean isLargeScreen(RatingApplyDto dto) {
        if (dto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && StringUtils.isNotBlank(dto.getSpec())) {
            List<String> codeList = JSON.parseArray(dto.getSpec(), String.class);
            List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();

            Collection<String> intersection = CollectionUtil.intersection(largeDeviceKey, codeList);
            return CollectionUtil.isNotEmpty(intersection);
        }
        return false;
    }


    private String getBuildingTypeName(Integer buildingType) {
        if (Objects.isNull(buildingType)) {
            return "";
        }
        return switch (buildingType) {
            case 0 -> "写字楼";
            case 1 -> "商住楼";
            case 2 -> "综合体";
            case 3 -> "产业园区";
            default -> "";
        };
    }


    private List<SysFileVO> fillPic(String fileIds) {
        List<SysFileVO> fileVOS = new ArrayList<>();
        if (StringUtils.isNotBlank(fileIds)) {
            String[] split = StringUtils.split(fileIds, ",");
            List<SysFileEntity> list = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                    .in(SysFileEntity::getId, Arrays.asList(split)));
            if (CollectionUtils.isNotEmpty(list)) {

                list.forEach(ele -> {
                    SysFileVO sysFileVO = new SysFileVO();
                    sysFileVO.setId(ele.getId());
                    sysFileVO.setName(ele.getName());
                    sysFileVO.setUrl(ele.getUrl());
                    sysFileVO.setSize(ele.getSize());
                    sysFileVO.setAttachmentType(ele.getAttachmentType());
                    fileVOS.add(sysFileVO);
                });


            }

        }

        return fileVOS;
    }
}

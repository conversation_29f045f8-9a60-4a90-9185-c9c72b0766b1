package com.coocaa.meht.module.building.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.module.building.param.TransferAndDropParam;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Slf4j
@RestController
@RequestMapping("/complete-rating")
@Tag(name = "完善评级", description = "完善相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompleteRatingController {

    private final CompleteRatingService completeRatingService;


    /**
     * 保存草稿
     *
     */
    @Operation(summary = "完善评级保存草稿")
    @PostMapping("/draft")
    public Result<String> saveDraft(@RequestBody RatingApplyDto ratingApplyDto) {
        return completeRatingService.saveDraft(ratingApplyDto);
    }


    /**
     * 完善评级提交
     *
     */
    @Operation(summary = "完善评级提交")
    @PostMapping("/submit")
    @RepeatSubmit
    public Result<Boolean> submitComplete(@RequestBody RatingApplyDto ratingApplyDto) {
        return completeRatingService.submitComplete(ratingApplyDto);
    }


    /**
     * 删除草稿
     *
     */
    @Operation(summary = "删除草稿")
    @DeleteMapping("/draft/{completeRatingNo}")
    public Result<Boolean> deleteDraft(@PathVariable String completeRatingNo) {
        completeRatingService.deleteDraft(completeRatingNo);
        return Result.ok();
    }

    /**
     * 完善评级评分细项
     *
     */
    @Operation(summary = "完善评级评分细项")
    @GetMapping("/scoring-item")
    public Result<Map<String, Object>> getScoringItem(@Param("completeRatingNo")String completeRatingNo, @Param("version")String version) {

        return Result.ok(completeRatingService.getScoringItem(completeRatingNo, version));
    }

    /**
     * 完善评级评分详情
     *
     */
    @Operation(summary = "完善评级转移及掉公海处理")
    @PostMapping("/transfer-and-drop")
    public Result<Boolean> transferAndDrop(@RequestBody TransferAndDropParam param) {
        completeRatingService.transferAndDrop(param);
        return Result.ok();
    }

}

package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.building.dao.CommonAttachmentMapper;
import com.coocaa.meht.module.building.entity.CommonAttachmentEntity;
import com.coocaa.meht.module.building.service.CommonAttachmentService;
import com.coocaa.meht.module.building.vo.SysFileVO;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.module.web.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 附件实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Slf4j
@Service
public class CommonAttachmentServiceImpl extends ServiceImpl<CommonAttachmentMapper, CommonAttachmentEntity> implements CommonAttachmentService {

    @Autowired
    private SysFileService sysFileService;

    @Override
    public void saveRatingAttachments(BuildingRatingEntity ratingEntity, List<String> urls) {
        save(BusinessTypeEnum.BUILDING_RATING.getValue(), BuildingMetaImgTypeEnum.OTHER_ATTACHMENTS.getType().toString(),
                ratingEntity.getId().intValue(), ratingEntity.getBuildingNo(), ratingEntity.getRatingVersion(), urls);
    }

    @Override
    public void save(Integer type, String businessType, Integer bizId, String bizCode, String extendData, List<String> urls) {
        // 删除已有附件
        remove(type, businessType, bizCode, extendData);

        if (CollUtil.isEmpty(urls)) {
            return;
        }

        //sys_file查询附件信息
        List<SysFileEntity> sysFiles = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                .in(SysFileEntity::getUrl, urls));

        if (CollUtil.isEmpty(sysFiles)) {
            log.warn("附件不存在，type：{}，businessType：{}，bizCode：{}，extendData：{}，urls：{}",
                    type, businessType, bizCode, extendData, urls);
            return;
        }

        List<CommonAttachmentEntity> attachmentEntities = sysFiles.stream().map(sysFile -> {
            CommonAttachmentEntity entity = new CommonAttachmentEntity();
            return entity
                    .setType(type)
                    .setBusinessType(businessType)
                    .setBizId(bizId)
                    .setBizCode(bizCode)
                    .setExtendData(extendData)
                    .setUrl(sysFile.getUrl())
                    .setName(sysFile.getName())
                    .setSize(sysFile.getSize())
                    .setAttachmentType(sysFile.getAttachmentType());
        }).collect(Collectors.toList());

        // 保存附件
        saveBatch(attachmentEntities);
    }

    @Override
    public List<SysFileVO> get(Integer type, String businessType, String bizCode, String extendData) {
        List<CommonAttachmentEntity> attachmentEntities = lambdaQuery()
                .eq(CommonAttachmentEntity::getType, type)
                .eq(CommonAttachmentEntity::getBusinessType, businessType)
                .eq(CommonAttachmentEntity::getBizCode, bizCode)
                .eq(StrUtil.isNotBlank(extendData), CommonAttachmentEntity::getExtendData, extendData)
                .list();

        if (CollUtil.isEmpty(attachmentEntities)) {
            return Collections.emptyList();
        }

        return attachmentEntities.stream().map(attachmentEntity -> {
            SysFileVO sysFileVo = new SysFileVO();
            sysFileVo.setId(attachmentEntity.getId().longValue());
            sysFileVo.setName(attachmentEntity.getName());
            sysFileVo.setUrl(attachmentEntity.getUrl());
            sysFileVo.setSize(attachmentEntity.getSize());
            sysFileVo.setAttachmentType(attachmentEntity.getAttachmentType());
            return sysFileVo;
        }).collect(Collectors.toList());
    }

    private void remove(Integer type, String businessType, String bizCode, String extendData) {
        lambdaUpdate()
                .eq(CommonAttachmentEntity::getType, type)
                .eq(CommonAttachmentEntity::getBusinessType, businessType)
                .eq(CommonAttachmentEntity::getBizCode, bizCode)
                .eq(StrUtil.isNotBlank(extendData), CommonAttachmentEntity::getExtendData, extendData)
                .remove();
    }

}

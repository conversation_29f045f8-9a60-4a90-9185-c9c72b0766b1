package com.coocaa.meht.module.building.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分数项
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
public class ScoreItemVO {
    @Schema(description = "参数名称")
    private String parameterName;

    @Schema(description = "AI参数值")
    private Object thirdValue;

    @Schema(description = "参数值")
    private String parameterValue;

    @Schema(description = "分数")
    private BigDecimal score;

    @Schema(description = "权重")
    private BigDecimal weight;

    @Schema(description = "平均分")
    private BigDecimal avgScore;

    @Schema(description = "AI分数")
    private BigDecimal aiScore;

    @Schema(description = "AI平均分")
    private BigDecimal aiAvgScore;

}

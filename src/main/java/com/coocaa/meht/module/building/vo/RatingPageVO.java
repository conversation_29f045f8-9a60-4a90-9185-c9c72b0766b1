package com.coocaa.meht.module.building.vo;

import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Data
public class RatingPageVO {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "申请编码")
    private String buildingNo;

    @Schema(description = "类型：1-评级申请，2-完善评级")
    private Integer type;

    @Schema(description = "省名称")
    private String mapProvince;

    @Schema(description = "市名称")
    private String mapCity;

    @Schema(description = "区名称")
    private String mapRegion;

    @Schema(description = "详细地址")
    private String mapAddress;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "楼宇类型")
    @Convert(type = ConvertType.BUILDING_TYPE)
    private Integer buildingType;

    @Schema(description = "楼宇类型名称")
    private String buildingTypeName;

    @Schema(description = "楼宇评级")
    private String projectLevel;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "评级版本")
    private String ratingVersion;

    @Schema(description = "提交认证时间")
    private LocalDateTime submitTime;

    @Schema(description = "申请人工号")
    private String createBy;

    @Schema(description = "申请人名称")
    private String createByName;

    @Schema(description = "提交人工号")
    private String submitUser;

    @Schema(description = "当前登陆人是否有数据编辑权限")
    private Boolean isSelfApply;

}

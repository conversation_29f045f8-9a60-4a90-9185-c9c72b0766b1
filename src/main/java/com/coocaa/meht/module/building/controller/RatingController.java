package com.coocaa.meht.module.building.controller;

import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.module.building.dto.RatingPageDTO;
import com.coocaa.meht.module.building.handler.AiRatingHandler;
import com.coocaa.meht.module.building.service.RatingService;
import com.coocaa.meht.module.building.vo.RatingPageVO;
import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.req.BuildingLevelReq;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.vo.common.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 楼宇评级控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@RestController
@RequestMapping("/rating")
@Tag(name = "楼宇评级", description = "楼宇评级相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RatingController {

    private final RatingService ratingService;

    private final AiRatingHandler aiRatingHandler;

    @Operation(summary = "楼宇评级列表")
    @PostMapping("/page")
    public Result<PageResult<RatingPageVO>> page(@RequestBody RatingPageDTO param) {
        return Result.ok(ratingService.page(param));
    }

    @Operation(summary = "楼宇评级详情")
    @GetMapping("/{businessKey}")
    public Result<RatingVO> info(@PathVariable String businessKey,
                                 @RequestParam Integer type,
                                 @RequestParam String ratingVersion) {
        return Result.ok(ratingService.info(businessKey, type, ratingVersion));
    }

    @RepeatSubmit
    @Operation(summary = "楼宇评级编辑")
    @PutMapping
    public Result<BuildingRatingEntity> edit(@RequestBody @Validated RatingApplyDto param) {
        return Result.ok(ratingService.edit(param));
    }

    @Operation(summary = "楼宇评级编辑检查")
    @GetMapping("/check/{buildingNo}")
    public Result<Boolean> check(@PathVariable String buildingNo) {
        return Result.ok(ratingService.checkComplete(buildingNo));
    }

    @Operation(summary = "楼宇评级草稿")
    @PostMapping("/draft")
    public Result<BuildingRatingEntity> draft(@RequestBody RatingApplyDto param) {
        return Result.ok(ratingService.draft(param));
    }

    @Operation(summary = "删除草稿")
    @DeleteMapping("/draft/{buildingNo}")
    public Result<BuildingRatingEntity> deleteDraft(@PathVariable String buildingNo) {
        ratingService.deleteDraft(buildingNo);
        return Result.ok();
    }

    @Operation(summary = "楼宇评级修改")
    @PostMapping("/update-level")
    public Result<Void> updateLevel(@RequestBody @Validated BuildingLevelReq req) {
        ratingService.updateLevel(req);
        return Result.ok();
    }

    /**
     * 撤回未发起审批流数据
     * 发起楼宇评级，完善评级，但AI评级没有发起审批流，正常的撤回逻辑无法处理，使用该方法撤回异常数据
     */
    @Operation(summary = "撤回未发起审批流数据", hidden = true)
    @PutMapping("/revoke-unsubmitted")
    public Result<Void> revokeUnsubmitted(@RequestParam AiRatingHandler.Type type,
                                          @RequestParam String bizCode) {
        ratingService.revokeUnsubmitted(type, bizCode);
        return Result.ok();
    }

}

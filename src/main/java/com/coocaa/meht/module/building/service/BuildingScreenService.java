package com.coocaa.meht.module.building.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;

import java.util.List;
import java.util.Map;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
public interface BuildingScreenService extends IService<BuildingScreenEntity> {

    /**
     * 保存或更新
     *
     * @param buildingNo
     * @param submitCoefficient
     * @param param
     */
    void saveOrUpdate(String buildingNo, String submitCoefficient, RatingApplyDto param);

    /**
     * 查询大屏数据
     *
     * @param buildingNo
     * @return
     */
    BuildingScreenVO getBuildingScreenByNo(String buildingNo);
    /**
     * 查询大屏数据
     *
     * @param buildingNo
     * @return
     */
    Map<String,BuildingScreenVO> getBuildingScreenByNoList(List<String> buildingNo);


    /**
     * 是否有大屏设备
     * @param vo
     * @return
     */
    Boolean isLargeScreen(BuildingScreenVO vo);

    /**
     * 是否是大屏设备
     * @param spec
     * @return
     */
    Boolean isLargeScreen(String spec);

    /**
     * 创建大屏对象
     * @param buildingNo
     * @param
     */
    BuildingScreenEntity creteScreenEntity(String buildingNo, String submitCoefficient, RatingApplyDto param);

    /**
     * 是否是大屏设备
     * @param buildingRatingEntity
     * @return
     */
    Boolean isLargeScreen(BuildingRatingEntity buildingRatingEntity);

}

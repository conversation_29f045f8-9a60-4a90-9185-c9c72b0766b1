package com.coocaa.meht.module.building.convert;

import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Mapper
public interface BuildingGeneConvert {
    BuildingGeneConvert INSTANCE = Mappers.getMapper(BuildingGeneConvert.class);

    BuildingGeneVO toBuildingGeneVO(BuildingGeneEntity entity);

    @Mapping(target = "forbiddenIndustry", ignore = true)
    BuildingGeneEntity toBuildingGeneByDto(RatingApplyDto applyDto);
}

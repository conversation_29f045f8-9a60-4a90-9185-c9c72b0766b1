package com.coocaa.meht.module.building.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("complete_rating_detail")
public class CompleteRatingDetailEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 完善编码
     */
    private String completeRatingNo;

    /**
     * 楼盘等级
     */
    private Long buildingGrade;

    /**
     * 等级（选项文本）
     */
    private String gradeName;

    /**
     * 地理位置
     */
    private Long buildingLocation;

    /**
     * 地理位置（选项文本）
     */
    private String locationName;

    /**
     * 楼层数
     */
    private Long buildingNumber;

    /**
     * 楼层数(输入)
     */
    private String buildingNumberInput;


    /**
     * 月租金
     */
    private Long buildingPrice;

    /**
     * 月租金(输入)
     */
    private String buildingPriceInput;

    /**
     * 日租金
     */
    private BigDecimal dailyPriceInput;

    /**
     * 楼龄
     */
    private Long buildingAge;

    /**
     * 楼龄(输入)
     */
    private String buildingAgeInput;

    /**
     * 交付日期
     */
    private Date deliveryDate;

    /**
     * 外观造型
     */
    private Long buildingExterior;

    /**
     * 外观造型（选项文本）
     */
    private String exteriorName;

    /**
     * 楼盘大堂
     */
    private Long buildingLobby;

    /**
     * 楼盘大堂（选项文本）
     */
    private String lobbyName;

    /**
     * 地下车库
     */
    private Long buildingGarage;

    /**
     * 地下车库（选项文本）
     */
    private String garageName;

    /**
     * 侯梯厅
     */
    private Long buildingHall;

    /**
     * 侯梯厅（选项文本）
     */
    private String hallName;

    /**
     * 综合体品牌
     */
    private Long buildingBrand;

    /**
     * 综合体品牌（选项文本）
     */
    private String brandName;

    /**
     * TOP100品牌ID
     */
    private Long topBrandId;

    /**
     * TOP100品牌名称
     */
    private String topBrandName;

    /**
     * 点评评分
     */
    private Long buildingRating;

    /**
     * 入驻率
     */
    private Long buildingSettled;

    /**
     * 第三方等级
     */
    private String thirdBuildingGrade;

    /**
     * 第三方地理位置
     */
    private String thirdBuildingLocation;

    /**
     * 第三方楼层数
     */
    private String thirdBuildingNumber;

    /**
     * 第三方月租金
     */
    private String thirdBuildingPrice;

    /**
     * 第三方日租金
     */
    private String thirdDailyPrice;

    /**
     * 中国房价行情网日租金
     */
    private BigDecimal creDailyPrice;

    /**
     * 第三方楼龄
     */
    private String thirdBuildingAge;

    /**
     * 第三方交付日期
     */
    private String thirdDeliveryDate;

    /**
     * 第三方楼型
     */
    private String thirdBuildingExterior;

    /**
     * 第三方楼高
     */
    private String thirdBuildingLobby;

    /**
     * 第三方地下车库
     */
    private String thirdBuildingGarage;

    /**
     * 第三方楼厅
     */
    private String thirdBuildingHall;

    /**
     * 第三方品牌
     */
    private String thirdBuildingBrand;

    /**
     * 第三方评分
     */
    private String thirdBuildingRating;

    /**
     * 第三方入驻率
     */
    private String thirdBuildingSettled;

    /**
     * 第三方等级分数ID
     */
    private Long thirdBuildingGradeId;

    /**
     * 第三方地理位置分数ID
     */
    private Long thirdBuildingLocationId;

    /**
     * 第三方楼层数分数ID
     */
    private Long thirdBuildingNumberId;

    /**
     * 第三方月租金分数ID
     */
    private Long thirdBuildingPriceId;

    /**
     * 第三方楼龄分数ID
     */
    private Long thirdBuildingAgeId;

    /**
     * 第三方外型分数ID
     */
    private Long thirdBuildingExteriorId;

    /**
     * 第三方大堂分数ID
     */
    private Long thirdBuildingLobbyId;

    /**
     * 第三方地下车库分数ID
     */
    private Long thirdBuildingGarageId;

    /**
     * 第三方侯梯厅分数ID
     */
    private Long thirdBuildingHallId;

    /**
     * 第三方品牌分数ID
     */
    private Long thirdBuildingBrandId;

    /**
     * 第三方评分分数ID
     */
    private Long thirdBuildingRatingId;

    /**
     * 第三方入驻率分数ID
     */
    private Long thirdBuildingSettledId;

    /**
     * 第三方楼宇类型
     */
    private String thirdBuildingType;

    /**
     * 评分文本值
     */
    private String ratingName;

    /**
     * 入驻率文本值
     */
    private String settledName;


    public Long valueId(String parameterCode) {
        switch (parameterCode) {
            case "buildingGrade":
                return this.buildingGrade;
            case "buildingLocation":
                return this.buildingLocation;
            case "buildingNumber":
                return this.buildingNumber;
            case "buildingSettled":
                return this.buildingSettled;
            case "buildingPrice":
                return this.buildingPrice;
            case "buildingAge":
                return this.buildingAge;
            case "buildingExterior":
                return this.buildingExterior;
            case "buildingHall":
                return this.buildingHall;
            case "buildingLobby":
                return this.buildingLobby;
            case "buildingGarage":
                return this.buildingGarage;
            case "buildingBrand":
                return this.buildingBrand;
            case "buildingRating":
                return this.buildingRating;
            case "thirdbuildingGradeid":
                return this.thirdBuildingGradeId;
            case "thirdbuildingGarageid":
                return this.thirdBuildingGarageId;
            case "thirdbuildingLocationid":
                return this.thirdBuildingLocationId;
            case "thirdbuildingNumberid":
                return this.thirdBuildingNumberId;
            case "thirdbuildingSettledid":
                return this.thirdBuildingSettledId;
            case "thirdbuildingPriceid":
                return this.thirdBuildingPriceId;
            case "thirdbuildingAgeid":
                return this.thirdBuildingAgeId;
            case "thirdbuildingExteriorid":
                return this.thirdBuildingExteriorId;
            case "thirdbuildingHallid":
                return this.thirdBuildingHallId;
            case "thirdbuildingLobbyid":
                return this.thirdBuildingLobbyId;
            case "thirdbuildingBrandid":
                return this.thirdBuildingBrandId;
            case "thirdbuildingRatingid":
                return this.thirdBuildingRatingId;
            default:
                return null;
        }
    }


    public String thirdValue(String parameterCode) {
        switch (parameterCode) {
            case "buildingGrade":
                return this.thirdBuildingGrade;
            case "buildingLocation":
                return this.thirdBuildingLocation;
            case "buildingNumber":
                return this.thirdBuildingNumber;
            case "buildingSettled":
                return this.thirdBuildingSettled;
            case "buildingPrice":
                return Objects.nonNull(this.creDailyPrice) ? this.creDailyPrice.toString() : this.thirdDailyPrice;
            case "buildingAge":
                return Objects.nonNull(this.deliveryDate) ? this.thirdDeliveryDate : this.thirdBuildingAge;
            case "buildingExterior":
                return this.thirdBuildingExterior;
            case "buildingHall":
                return this.thirdBuildingHall;
            case "buildingLobby":
                return this.thirdBuildingLobby;
            case "buildingGarage":
                return this.thirdBuildingGarage;
            case "buildingBrand":
                return this.thirdBuildingBrand;
            case "buildingRating":
                return this.thirdBuildingRating;
            default:
                return null;
        }
    }
}

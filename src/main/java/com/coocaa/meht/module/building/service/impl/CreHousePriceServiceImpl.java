package com.coocaa.meht.module.building.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.meht.module.building.convert.CreHousePriceRentConvert;
import com.coocaa.meht.module.building.dao.CreHousePriceRentMapper;
import com.coocaa.meht.module.building.entity.CreHousePriceRentEntity;
import com.coocaa.meht.module.building.service.CreHousePriceRentService;
import com.coocaa.meht.rpc.CreHousePriceRpc;
import com.coocaa.meht.rpc.dto.CreHousePriceRentRequest;
import com.coocaa.meht.rpc.dto.CreHousePriceRentResponse;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 房价行情网接口服务
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreHousePriceServiceImpl extends ServiceImpl<CreHousePriceRentMapper, CreHousePriceRentEntity> implements CreHousePriceRentService {

    private final CreHousePriceRpc creHousePriceRpc;


    @Value("${cre-house-price.api-key}")
    private String apiKey;

    private static final List<String> SUFFIX_BLACK_LIST = List.of("市");

    /**
     * 获取房屋租金信息，处理正常和异常响应
     *
     * @param request 房价查询请求
     * @return 房价响应结果
     */
    @Override
    public CreHousePriceRentEntity getHouseRentInfo(CreHousePriceRentRequest request) {
        if (request == null) {
            return null;
        }
        String summary = SecureUtil.md5(JSON.toJSONString(request));
        CreHousePriceRentEntity housePriceRent = getHousePriceRent(summary);
        if (housePriceRent != null) {
            return housePriceRent;
        }

        log.info("获取房屋租金信息请求 {}", JSON.toJSONString(request));
        try {
            // 调用RPC接口
            String city = request.getCity().trim();
            // 处理行政区跟行情网那边的差异
            if (StrUtil.isNotBlank(city)) {
                for (String suffix : SUFFIX_BLACK_LIST) {
                    if (city.endsWith(suffix)) {
                        city = StrUtil.replaceLast(city, suffix, "");
                    }
                }
            }
            CreHousePriceRentResponse result = creHousePriceRpc.getHouseRent(
                    city,
                    request.getDistrict().trim(),
                    request.getHaName(),
                    request.getLocation(),
                    apiKey
            );
            log.info("房价行情 RPC result: {}", result);
            if (result.getError() == null) {
                CreHousePriceRentEntity entity = CreHousePriceRentConvert.INSTANCE.toEntity(result);
                entity.setLatitude(AesUtils.encryptHex(entity.getLatitude()));
                entity.setLongitude(AesUtils.encryptHex(entity.getLongitude()));
                entity.setSummary(summary);
                this.save(entity);
                return entity;
            } else {
                log.warn("房价行情接口返回失败: {}", JSON.toJSONString(result.getError()));
                return null;
            }
        } catch (FeignException.BadRequest e) {
            // 专门处理400错误
            String errorBody = e.contentUTF8();
            log.error("房价行情接口调用异常(400): {}", errorBody);

            try {
                // 尝试解析错误响应
                CreHousePriceRentResponse errorResponse = JSON.parseObject(errorBody, CreHousePriceRentResponse.class);
                if (errorResponse != null && errorResponse.getError() != null) {
                    log.warn("房价行情接口错误: code={}, message={}, detail={}",
                            errorResponse.getError().getCode(),
                            errorResponse.getError().getMessage(),
                            errorResponse.getError().getDetail());
                }
            } catch (Exception ex) {
                log.error("解析房价行情接口错误响应失败", ex);
            }

            return null;
        } catch (FeignException e) {
            // 处理其他Feign异常(非400错误)
            log.error("房价行情接口调用异常: status={}, 内容={}",
                    e.status(), e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("调用房价行情接口异常", e);
            return null;
        }
    }

    /**
     * 从数据库获取房价行情
     *
     * @param summary 摘要
     * @return
     */
    public CreHousePriceRentEntity getHousePriceRent(String summary) {
        return this.lambdaQuery()
                .eq(CreHousePriceRentEntity::getSummary, summary)
                .last(" limit 1")
                .one();
    }
} 
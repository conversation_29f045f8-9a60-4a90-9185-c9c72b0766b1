package com.coocaa.meht.module.building.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.7.8
 * @since 2025-06-16
 */
@Data
public class BuildingDetailsVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 楼宇编码
     */
    private String buildingNo;
    /**
     * 写字楼等级
     */
    private Long buildingGrade;
    /**
     * 地理等级
     */
    private Long buildingLocation;
    /**
     * 楼层数
     */
    private Long buildingNumber;

    /**
     * 楼层数输入
     */
    private String buildingNumberInput;
    /**
     * 月租金
     */
    private Long buildingPrice;

    /**
     * 月租金输入
     */
    private String buildingPriceInput;
    /**
     * 楼龄
     */
    private Long buildingAge;
    /**
     * 楼龄输入
     */
    private String buildingAgeInput;
    /**
     * 外墙材料
     */
    private Long buildingExterior;
    /**
     * 楼盘大堂
     */
    private Long buildingLobby;
    /**
     * 地下车库
     */
    private Long buildingGarage;
    /**
     * 侯梯厅
     */
    private Long buildingHall;
    /**
     * 综合体品牌
     */
    private Long buildingBrand;

    /**
     * Top100品牌ID
     * 来源于数据表：building_brand
     */
    private Long topBrandId;

    /**
     * TOP100品牌名称
     */
    private String topBrandName;

    /**
     * 点评评分
     */
    private Long buildingRating;
    /**
     * 入驻率
     */
    private Long buildingSettled;
    /**
     * 第三方等级
     */
    private String thirdBuildingGrade;

    /**
     * 第三方等级ID
     */
    private Long thirdBuildingGradeId;

    /**
     * 第三方地理位置
     */
    private String thirdBuildingLocation;

    /**
     * 第三方地理位置ID
     */
    private Long thirdBuildingLocationId;

    /**
     * 第三方楼层数
     */
    private String thirdBuildingNumber;
    /**
     * 第三方楼层数
     */
    private Long thirdBuildingNumberId;


    /**
     * 第三方月租金
     */
    private String thirdBuildingPrice;
    /**
     * 第三方月租金
     */
    private Long thirdBuildingPriceId;

    /**
     * 第三方楼宇类型
     */
    private String thirdBuildingType;

    /**
     * 第三方楼龄
     */
    private String thirdBuildingAge;
    /**
     * 第三方楼龄
     */
    private Long thirdBuildingAgeId;

    /**
     * 第三方外观造型
     */
    private String thirdBuildingExterior;

    /**
     * 第三方外观造型
     */
    private Long thirdBuildingExteriorId;

    /**
     * 第三方楼盘大堂
     */
    private String thirdBuildingLobby;

    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingLobbyId;

    /**
     * 第三方地下车库
     */
    private String thirdBuildingGarage;
    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingGarageId;

    /**
     * 第三方侯梯厅
     */
    private String thirdBuildingHall;
    /**
     * 第三方楼盘大堂
     */
    private Long thirdBuildingHallId;

    /**
     * 第三方综合体品牌
     */
    private String thirdBuildingBrand;
    /**
     * 第三方综合体品牌
     */
    private Long thirdBuildingBrandId;

    /**
     * 第三方点评评分
     */
    private String thirdBuildingRating;
    /**
     * 第三方点评评分
     */
    private Long thirdBuildingRatingId;

    /**
     * 第三方入驻率
     */
    private String thirdBuildingSettled;
    /**
     * 第三方入驻率
     */
    private Long thirdBuildingSettledId;

    /**
     * 日租金输入值
     */
    private BigDecimal dailyPriceInput;

    /**
     * 第三方日租金
     */
    private String thirdDailyPrice;

    /**
     * 交付日期输入值
     */
    private LocalDate deliveryDate;

    /**
     * 第三方交付日期
     */
    private String thirdDeliveryDate;

    /**
     * 楼宇等级文本值
     */
    private String gradeName;

    /**
     * 地理位置文本值
     */
    private String locationName;

    /**
     * 外立面文本值
     */
    private String exteriorName;

    /**
     * 大楼大堂文本值
     */
    private String lobbyName;

    /**
     * 车库文本值
     */
    private String garageName;

    /**
     * 等候厅文本值
     */
    private String hallName;

    /**
     * 品牌文本值
     */
    private String brandName;

    /**
     * 评分文本值
     */
    private String ratingName;

    /**
     * 入驻率文本值
     */
    private String settledName;

    /**
     * 中国房价行情网日租金
     */
    private BigDecimal creDailyPrice;

}

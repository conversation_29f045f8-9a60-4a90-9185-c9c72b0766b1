package com.coocaa.meht.module.building.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.building.entity.CommonAttachmentEntity;
import com.coocaa.meht.module.building.vo.SysFileVO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;

import java.util.List;

/**
 * 附件服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
public interface CommonAttachmentService extends IService<CommonAttachmentEntity> {

    /**
     * 保存附件
     *
     * @param type 附件类型
     * @param businessType 业务类型
     * @param bizId 业务ID
     * @param bizCode 业务编码
     * @param extendData 扩展数据
     * @param urls 附件地址
     */
    void save(Integer type, String businessType, Integer bizId, String bizCode, String extendData, List<String> urls);

    /**
     * 保存楼宇评级附件
     *
     * @param ratingEntity 评级信息
     * @param urls 附件地址
     */
    void saveRatingAttachments(BuildingRatingEntity ratingEntity, List<String> urls);

    /**
     * 获取附件
     *
     * @param type 附件类型
     * @param businessType 业务类型
     * @param bizCode 业务编码
     * @param extendData 扩展数据
     * @return 附件列表
     */
    List<SysFileVO> get(Integer type, String businessType, String bizCode, String extendData);

}

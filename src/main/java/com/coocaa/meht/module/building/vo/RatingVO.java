package com.coocaa.meht.module.building.vo;

import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Data
public class RatingVO {

    @Schema(description = "主键id")
    private Long id;

    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingNo;

    /**
     * 完善编码
     */
    @Schema(description = "完善编码")
    private String completeRatingNo;

    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中 3已认证
     */
    @Schema(description = "楼宇认证状态：0未认证，1认证中 2冻结中 3已认证")
    private Integer buildingStatus;

    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过 4 已放弃
     */
    @Schema(description = "楼宇状态：0审核中，1已审核 2已驳回 3审核不通过 4 已放弃")
    private Integer status;

    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区")
    private Integer buildingType;

    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区")
    private String buildingTypeName;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 地图楼宇编码
     */
    @Schema(description = "地图楼宇编码")
    private String mapNo;

    /**
     * 省名称
     */
    @Schema(description = "省名称")
    private String mapProvince;

    /**
     * 市名称
     */
    @Schema(description = "市名称")
    private String mapCity;

    /**
     * 区名称
     */
    @Schema(description = "区名称")
    private String mapRegion;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String mapAddress;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String mapLatitude;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String mapLongitude;

    /**
     * 区域行政编码
     */
    @Schema(description = "区域行政编码")
    private String mapAdCode;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String buildingDesc;
    /**
     * 提交人工号
     */
    @Schema(description = "提交人工号")
    private String submitUser;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 综合得分
     */
    @Schema(description = "综合得分")
    private BigDecimal buildingScore;

    /**
     * 等级评价
     */
    @Schema(description = "等级评价")
    private String projectLevel;

    /**
     * 一楼独享
     */
    @Schema(description = "一楼独享")
    private BigDecimal firstFloorExclusive;

    /**
     * 一楼共享
     */
    @Schema(description = "一楼共享")
    private BigDecimal firstFloorShare;

    /**
     * 负一楼
     */
    @Schema(description = "负一楼")
    private BigDecimal negativeFirstFloor;

    /**
     * 负二楼
     */
    @Schema(description = "负二楼")
    private BigDecimal negativeTwoFloor;

    /**
     * 二楼及以上
     */
    @Schema(description = "二楼及以上")
    private BigDecimal twoFloorAbove;

    /**
     * 负三楼及以下
     */
    @Schema(description = "负三楼及以下")
    private BigDecimal thirdFloorBelow;

    /**
     * 审批人工号
     */
    @Schema(description = "审批人工号")
    private String approveUser;
    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 审批描述
     */
    @Schema(description = "审批描述")
    private String approveDesc;
    /**
     * 驳回人工号
     */
    @Schema(description = "驳回人工号")
    private String rejectUser;
    /**
     * 驳回时间
     */
    @Schema(description = "驳回时间")
    private LocalDateTime rejectTime;

    /**
     * 驳回描述
     */
    @Schema(description = "驳回描述")
    private String rejectDesc;

    /**
     * 大屏评级标识
     */
    @Schema(description = "大屏评级标识")
    private Boolean largeScreen;

    /**
     * 外墙材料附件地址
     */
    @Schema(description = "外墙材料附件地址")
    private String buildingExteriorPic;

    /**
     * 外墙材料附件地址集合
     */
    @Schema(description = "外墙材料附件地址集合")
    private List<SysFileVO> buildingExteriorPics = new ArrayList<>();

    /**
     * 楼盘大堂附件地址
     */
    @Schema(description = "楼盘大堂附件地址")
    private String buildingLobbyPic;

    /**
     * 楼盘大堂附件地址
     */
    @Schema(description = "楼盘大堂附件地址")
    private List<SysFileVO> buildingLobbyPics = new ArrayList<>();

    /**
     * 侯梯厅附件地址
     */
    @Schema(description = "侯梯厅附件地址")
    private String buildingHallPic;

    /**
     * 侯梯厅附件地址
     */
    @Schema(description = "侯梯厅附件地址")
    private List<SysFileVO> buildingHallPics = new ArrayList<>();

    /**
     * 大堂环境图附件地址
     */
    @Schema(description = "大堂环境图附件地址")
    private String buildingLobbyEnvPic;

    /**
     * 大堂环境图附件地址
     */
    @Schema(description = "大堂环境图附件地址")
    private List<SysFileVO> buildingLobbyEnvPics = new ArrayList<>();

    /**
     * 梯厅环境图附件地址
     */
    @Schema(description = "梯厅环境图附件地址")
    private String buildingElevatorPic;

    /**
     * 梯厅环境图附件地址
     */
    @Schema(description = "梯厅环境图附件地址")
    private List<SysFileVO> buildingElevatorPics = new ArrayList<>();

    /**
     * 闸口图附件地址
     */
    @Schema(description = "闸口图附件地址")
    private String buildingGatePic;

    /**
     * 闸口图附件地址
     */
    @Schema(description = "闸口图附件地址")
    private List<SysFileVO> buildingGatePics = new ArrayList<>();

    /**
     * 安装示意图附件地址
     */
    @Schema(description = "安装示意图附件地址")
    private String buildingInstallationPic;

    /**
     * 安装示意图附件地址
     */
    @Schema(description = "安装示意图附件地址")
    private List<SysFileVO> buildingInstallationPics = new ArrayList<>();

    /**
     * 地图地址
     */
    @Schema(description = "地图地址")
    private String mapUrl;


    /**
     * AI评级
     */
    @Schema(description = "AI评级")
    private String projectAiLevel;
    /**
     * AI得分
     */
    @Schema(description = "AI得分")
    private BigDecimal buildingAiScore;

    /**
     * 目标点位数
     */
    @Schema(description = "目标点位数")
    private Integer targetPointCount;

    /**
     * 楼宇等级top等级
     */
    @Schema(description = "楼宇等级top等级")
    private String topLevel;

    /**
     * 公海数据标识：0-否，1-是
     */
    @Schema(description = "公海数据标识：0-否，1-是")
    private Integer highSeaFlag;

    /**
     * 掉入公海时间
     */
    @Schema(description = "掉入公海时间")
    private LocalDateTime enterSeaTime;

    /**
     * 掉入公海计算起始时间
     */
    @Schema(description = "掉入公海计算起始时间")
    private LocalDateTime enterSeaCalculateTime;

    /**
     * 掉入公海原因
     */
    @Schema(description = "掉入公海原因")
    private String enterSeaReason;

    /**
     * 小屏评级完成标识：0-否，1-是
     */
    private Integer smallScreenRatingFlag;

    /**
     * 大屏评级完成标识：0-否，1-是
     */
    private Integer largeScreenRatingFlag;

    /**
     * 评级版本
     */
    @Schema(description = "评级版本")
    private String ratingVersion;

    /**
     * 数据版本标识
     */
    @Schema(description = "数据版本标识")
    private Integer dataFlag;

    /**
     * 审批详情
     */
    @Schema(description = "审批详情")
    private List<ScreenApproveRecordDTO> approvalDetailVO;

    /**
     * buildingDetails详情
     */
    @Schema(description = "buildingDetails详情")
    private BuildingDetailsVO buildingDetailsVO;

    /**
     * 大屏数据
     */
    @Schema(description = "大屏数据")
    private BuildingScreenVO buildingScreenVO;

    @Schema(description = "负责人名称")
    private String submitUserName;

    @Schema(description = "创建人名称")
    private String createUserName;

    @Schema(description = "创建人工号")
    private String  createUserCode;

    @Schema(description = "系数值")
    private BigDecimal coefficientValue;

    @Schema(description = "数据类型：1-楼宇评级，2-完善评级")
    private Integer type;

    @Schema(description = "基因数据")
    private BuildingGeneVO buildingGeneVO;

    @Schema(description = "去掉省市区的详细地址")
    private String simplifyAddress;

    @Schema(description = "当前登陆人是否是数据审批人")
    private Boolean isSelfApprove;

    @Schema(description = "楼宇状态名称")
    private String statusName;

    @Schema(description = "项目复核等级")
    private String projectReviewLevel;

    @Schema(description = "当前登陆人是否有数据编辑权限")
    private Boolean isSelfApply;

    @Schema(description = "其他附件")
    private List<SysFileVO> otherAttachments;

}

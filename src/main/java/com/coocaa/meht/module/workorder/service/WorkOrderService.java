package com.coocaa.meht.module.workorder.service;

import com.coocaa.meht.module.workorder.dto.OrderPointUpdateDTO;
import com.coocaa.meht.module.workorder.dto.PointListQueryDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderCancelRequestDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderNoticeDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderPageQueryDTO;
import com.coocaa.meht.module.workorder.vo.BuildingDetailVO;
import com.coocaa.meht.module.workorder.vo.PointDetail2WorkOrder;
import com.coocaa.meht.module.workorder.vo.PointResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderBuildingVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderCancelResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderDetailVO;
import com.coocaa.meht.rpc.dto.ContractWithPointsVO;
import com.coocaa.meht.rpc.dto.WorkOrderPageResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 工单功能服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
public interface WorkOrderService {
    /**
     * 创建工单
     *
     * @param workOrderDTO 工单信息
     * @return 工单结果列表
     */
    List<PointResultVO> create(WorkOrderDTO workOrderDTO);

    /**
     * 获取工单列表
     *
     * @param workOrderPageQueryDTO 查询条件
     * @return 工单列表
     */
    WorkOrderPageResult<WorkOrderBuildingVO> list(WorkOrderPageQueryDTO workOrderPageQueryDTO);

    /**
     * 获取工单楼宇详情
     *
     * @param buildingMetaNo 工单楼宇编码
     * @param workOrderType  工单类型
     * @return 工单楼宇详情
     */
    BuildingDetailVO buildingDetail(String buildingMetaNo, String workOrderType);

    /**
     * 获取工单详情
     *
     * @param workOrderNo 工单编号
     * @return 工单详情
     */
    WorkOrderDetailVO workOrderDetail(String workOrderNo);

    /**
     * 通知工单已完成
     *
     * @param workOrderNoticeDTO 工单通知信息
     * @return 是否成功
     */
    boolean noticeWorkOrderFinished(WorkOrderNoticeDTO workOrderNoticeDTO);

    /**
     * 获取项目下待执行、执行中的合同及点位编码信息
     *
     * @param buildingNo 工单楼宇编码
     * @return 工单详情
     */
    List<ContractWithPointsVO> projectContractPoints(String buildingNo, String workOrderType);

    /**
     * 根据点位编码列表查询点位详情
     *
     * @param queryDTO 点位查询参数
     * @return 点位详情列表
     */
    List<PointDetail2WorkOrder> queryPointDetails(PointListQueryDTO queryDTO);

    /**
     * 取消工单
     *
     * @param request 取消工单请求参数
     * @return 取消工单结果列表
     */
    List<WorkOrderCancelResultVO> cancelWorkOrders(@RequestBody WorkOrderCancelRequestDTO request);


    /**
     * 工单点位信息编辑
     */
    void workOrderPointEdit(OrderPointUpdateDTO param);
}

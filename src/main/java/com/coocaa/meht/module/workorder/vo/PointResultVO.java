package com.coocaa.meht.module.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 点位创建结果VO类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class PointResultVO {
    /**
     * 点位编码
     */
    @Schema(description = "点位编码", type = "String", example = "P123456")
    private String pointCode;

    /**
     * 新建结果，true-成功，false-失败
     */
    @Schema(description = "新建结果，true-成功，false-失败", type = "Boolean", example = "true")
    private Boolean createSuccess;

    /**
     * 说明（失败原因或其他提示信息）
     */
    @Schema(description = "说明（失败原因或其他提示信息）", type = "String", example = "工单已存在")
    private String message;
}

package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 点位方案详情
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class PointDetailDTO {

    /**
     * 点位编码
     */
    @NotEmpty(message = "点位编码不能为空")
    @Schema(description = "点位编码", type = "String", example = "P123456")
    private String pointCode;

    /**
     * 点位尺寸
     */
    @NotEmpty(message = "点位尺寸不能为空")
    @Schema(description = "点位尺寸", type = "String", example = "折角屏")
    private String pointSize;

    /**
     * 楼栋名称
     */
    @NotEmpty(message = "楼栋名称不能为空")
    @Schema(description = "楼栋名称", type = "String", example = "1号楼")
    private String buildingBlockName;

    /**
     * 单元名称
     */
    @NotEmpty(message = "单元名称不能为空")
    @Schema(description = "单元名称", type = "String", example = "A单元")
    private String unitName;

    /**
     * 楼层名称
     */
    @NotEmpty(message = "楼层名称不能为空")
    @Schema(description = "楼层名称", type = "String", example = "3F")
    private String floorName;

    /**
     * 楼层字典
     */
//    @NotEmpty(message = "楼层字典不能为空")
//    @Schema(description = "楼层字典", type = "String", example = "F3")
//    private String floorDic;

    /**
     * 等候厅名称
     */
    @NotEmpty(message = "等候厅名称不能为空")
    @Schema(description = "等候厅名称", type = "String", example = "东厅")
    private String waitingHallName;

    /**
     * 等候厅ID
     */
//    @NotEmpty(message = "等候厅ID不能为空")
//    @Schema(description = "等候厅ID", type = "String", example = "WH123")
//    private String waitingHallId;

    /**
     * 等候厅类型字典
     */
//    @NotEmpty(message = "等候厅类型字典不能为空")
//    @Schema(description = "等候厅类型字典", type = "String", example = "TYPE1")
//    private String waitingHallTypeDic;

    /**
     * 等候厅类型名称
     */
//    @NotEmpty(message = "等候厅类型名称不能为空")
    @Schema(description = "等候厅类型名称", type = "String", example = "普通厅")
    private String waitingHallTypeName;

    /**
     * 点位图
     */
    @NotEmpty(message = "点位图不能为空")
    @Schema(description = "点位图", type = "array", example = "[\"http://example.com/1.jpg\",\"http://example.com/2.jpg\"]")
    private List<String> pointPictureUrls;

    @Schema(description = "合同状态", type = "String", example = "无合同")
    @NotEmpty(message = "合同状态不能为空")
    private String contractStatus;
}

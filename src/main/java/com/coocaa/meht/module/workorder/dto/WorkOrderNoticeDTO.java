package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工单通知DTO
 * <p>用于工单通知相关数据传输</p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
public class WorkOrderNoticeDTO {
    /**
     * 楼宇编码
     */
    @NotEmpty(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码", type = "String", required = true)
    private String buildingMetaNo;

    /**
     * 楼宇名称
     */
    @NotEmpty(message = "楼宇名称不能为空")
    @Schema(description = "楼宇名称", type = "String", required = true)
    private String buildingName;

    /**
     * 楼宇负责人工号
     */
    @NotEmpty(message = "楼宇负责人工号不能为空")
    @Schema(description = "楼宇负责人工号", type = "String", required = true)
    private String followerWno;

    /**
     * 工单类型 1安装工单 2拆除工单
     */
    @NotNull(message = "工单类型不能为空")
    @Schema(description = "工单类型 1安装工单 2拆除工单", type = "int", required = true)
    private Integer workOrderType;

    /**
     * 点位编码
     */
    @NotEmpty(message = "点位编码不能为空")
    @Schema(description = "点位编码", type = "String", required = true)
    private String pointCode;

    /**
     * 工单编码
     */
    @NotEmpty(message = "工单编码不能为空")
    @Schema(description = "工单编码", type = "String", required = true)
    private String workOrderNo;

    /**
     * 工单状态
     */
    @NotEmpty(message = "工单状态不能为空")
    @Schema(description = "工单状态", type = "String", required = true)
    private String workOrderStatusName;

    /**
     * 工单状态
     */
    @Schema(description = "其他信息", type = "String")
    private String message;
} 
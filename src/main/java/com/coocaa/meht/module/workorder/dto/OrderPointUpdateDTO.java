package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-18
 */
@Data
public class OrderPointUpdateDTO {
    @Schema(description = "评级编号")
    @NotBlank(message = "评级编号不能为空")
    private String buildingRatingNo;

    @Schema(description = "楼栋名称")
    @NotBlank(message = "楼栋名称不能为空")
    private String buildingName;

    @Schema(description = "楼栋名称修改之前的值")
    @NotBlank(message = "楼栋名称修改之前的值不能为空")
    private String buildingOriginVale;

    @Schema(description = "单元名称")
    @NotBlank(message = "单元名称不能为空")
    private String unitName;

    @Schema(description = "单元名称修改之前的值")
    @NotBlank(message = "单元名称修改之前的值不能为空")
    private String unitOriginVale;

    @Schema(description = "楼层传字典")
    @NotBlank(message = "楼层不能为空")
    private String floor;


    @Schema(description = "楼层传字典修改之前的值")
    @NotBlank(message = "楼层修改之前的值不能为空")
    private String floorOriginVale;

    @Schema(description = "等候厅名称")
    @NotBlank(message = "等候厅名称不能为空")
    private String waitingHallName;

    @Schema(description = "等候厅类型")
    @NotBlank(message = "等候厅类型不能为空")
    private String waitingHallType;


    @Schema(description = "等候厅id")
    @NotNull(message = "等候厅id不能为空")
    private Integer waitingHallId;

    @Schema(description = "点位编号")
    @NotBlank(message = "点位编号不能为空")
    private String pointCode;


    @Schema(description = "备注")
    private String pointRemark;

    @Schema(description = "设备尺寸")
    @NotBlank(message = "设备尺寸不能为空")
    private String deviceSize;

    @Schema(description = "点位id")
    @NotNull(message = "点位id不能为空")
    private Integer pointId;

    @Schema(description = "点位图片")
    @NotNull(message = "点位图片不能为空")
    private List<String> pointPics = new ArrayList<>();
}

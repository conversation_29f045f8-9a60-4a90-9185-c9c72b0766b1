package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-07-17
 */
@Data
public class BuildingNameQuery {

    @Schema(description = "bc编码集合")
    @NotEmpty(message = "bc编码集合不能为空")
    private List<String> buildingMetaNoList;

}

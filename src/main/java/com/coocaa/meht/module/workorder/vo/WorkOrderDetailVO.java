package com.coocaa.meht.module.workorder.vo;

import com.coocaa.meht.module.workorder.dto.WorkOrderAttachmentDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 工单详情信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class WorkOrderDetailVO {
    @Schema(description = "创建时间", example = "2024-05-01 10:00:00")
    private String createTime;

    @Schema(description = "施工日期", example = "2024-05-02")
    private String constructionDate;

    @Schema(description = "施工时段", example = "10:00-12:00")
    private String constructionPeriod;

    @Schema(description = "施工说明，key为说明项，value为内容。例如：'是否有障碍物需要拆除: 是'",
            example = "{\"是否有障碍物需要拆除\":\"是\",\"障碍物类型\":\"类型1\",\"线槽材料\":\"材料1\",\"挂板工艺\":\"工艺1\",\"其他说明\":\"无\"}")
    private Map<String, String> constructionDescription;

    @Schema(description = "联系BD姓名", example = "张三")
    private String bdContactPerson;

    @Schema(description = "联系BD工号", example = "W123456")
    private String bdContactPersonWno;

    @Schema(description = "BD联系方式", example = "13800000000")
    private String bdContactPhone;

    @Schema(description = "物业对接人", example = "李四")
    private String propertyContactPerson;

    @Schema(description = "物业联系方式", example = "13900000000")
    private String propertyContactPhone;

    @Schema(description = "关闭原因")
    private String closeReason;

    @Schema(description = "附件列表", type = "array")
    private List<WorkOrderAttachmentDTO> attachment;

}

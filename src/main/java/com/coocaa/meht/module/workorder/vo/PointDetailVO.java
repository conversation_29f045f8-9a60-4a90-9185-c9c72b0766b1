package com.coocaa.meht.module.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 工单点位详情VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
@Schema(description = "工单点位详情VO")
public class PointDetailVO {

    @Schema(description = "点位编码", example = "CD001511")
    private String pointCode;

    @Schema(description = "点位位置，规则：楼栋名称_单元名称_楼层_等候厅_点位编码",
            example = "1栋_1单元_1层_1号电梯厅_点位编码1")
    private String pointLocation;

    @Schema(description = "点位尺寸", example = "100*200")
    private String pointSize;

    @Schema(description = "工单状态", example = "已完成")
    private String workOrderStatusName;

    @Schema(description = "工单编码（请求工单详情时用）", example = "WO20240101001")
    private String workOrderNo;

    @Schema(description = "是否可撤销，true可以，false不可以", example = "true")
    private Boolean canCancelled;

    @Schema(description = "创建时间", example = "2024-01-01 00:00:00")
    private Date createTime;

    @Schema(description = "合同状态", example = "合同履约中")
    private String contractStatus;
}
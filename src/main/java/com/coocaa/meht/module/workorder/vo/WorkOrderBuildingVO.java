package com.coocaa.meht.module.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 列表维度工单VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class WorkOrderBuildingVO {
    @Schema(description = "楼宇编码", example = "BCX001")
    private String buildingMetaNo;

    @Schema(description = "楼宇名称", example = "万达广场A座")
    private String buildingName;

    @Schema(description = "楼宇类型", example = "写字楼")
    private String buildingType;

    @Schema(description = "楼宇负责人工号", example = "CC2405")
    private String followerWno;

    @Schema(description = "楼宇负责人姓名", example = "张强")
    private String followerName;

    @Schema(description = "工单的最近创建时间", example = "2025-01-01 14:20:30")
    private String latestTime;

    @Schema(description = "楼宇下工单数量", example = "10")
    private Integer workOrderCount;

    @Schema(description = "楼宇地址", example = "上海")
    private String address;
}

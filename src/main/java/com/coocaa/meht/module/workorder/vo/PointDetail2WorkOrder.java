package com.coocaa.meht.module.workorder.vo;

import cn.hutool.core.date.DatePattern;
import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-08-08
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class PointDetail2WorkOrder {

    /**
     * 是否可安装
     */
    private Boolean canInstall;

    /**
     * 提示
     */
    private String message;

    /**
     * 合同状态 需要上传附件
     */
    private String contractStatus;

    /**
     * 是否需要上传附件
     */
    private Boolean requireAttachment;


    @Schema(description = "评级编号")
    private String buildingRatingNo;

    @Schema(description = "楼栋名称")
    private String buildingName;

    @Schema(description = "单元名称")
    private String unitName;
    @Convert(type = ConvertType.DICT)

    @Schema(description = "楼层")
    private String floor;

    @Schema(description = "楼层名称")
    private String floorName;

    @Schema(description = "等候厅名称")
    private String waitingHallName;

    @Convert(type = ConvertType.DICT)
    @Schema(description = "等候厅类型")
    private String waitingHallType;

    @Schema(description = "等候厅类型")
    private String waitingHallTypeName;

    @Schema(description = "等候厅id")
    private Integer waitingHallId;

    @Schema(description = "点位编号")
    private String pointCode;

    @Schema(description = "业务编号")
    private String businessCode;

    @Schema(description = "备注")
    private String pointRemark;

    @Convert(type = ConvertType.DICT, targetFieldName = "deviceSizeName")
    @Schema(description = "设备尺寸")
    private String deviceSize;

    @Schema(description = "设备尺寸名称")
    private String deviceSizeName;

    @Schema(description = "点位id")
    private Integer pointId;

    @Schema(description = "点位图片")
    private List<String> pointPics = new ArrayList<>();

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime pointCreateTime;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime expireTime;


    /**
     * 安装位置
     */
    public String getInstallLocation() {
        return String.format("%s_%s_%s", buildingName, unitName, floorName);
    }

    /**
     * 安装区域
     */
    public String getInstallArea() {
        return String.format("%s_%s", waitingHallTypeName, waitingHallName);
    }

    /**
     * 生成分组的Key
     */
    public String getGroupKey() {
        return String.format("%s|%s|%s|%s|%s|%s", deviceSize, buildingName, unitName, floor, waitingHallType, waitingHallName);
    }

    @Data
    public static class Device {
        /**
         * 类型
         */
        private String type = "创维液晶电视";

        /**
         * 设备尺寸
         */
        private String model;

        /**
         * 安装区域
         * 等候厅
         */
        private String area;

        /**
         * 安装位置
         * 楼栋单元楼层
         */
        private String location;

        /**
         * 安装数量
         */
        private Integer count;
    }
}

package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 工单分页查询DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class WorkOrderPageQueryDTO {

    /**
     * 当前页
     */
    @Schema(description = "当前页", type = "int", example = "1", required = true)
    private int currentPage = 1;

    /**
     * 每页显示条数
     */
    @Schema(description = "每页显示条数", type = "int", example = "20", required = true)
    private int pageSize = 10;

    /**
     * 工单分页查询条件
     */
    @Schema(description = "查询条件", type = "object", required = false)
    private Query query;


    /**
     * 工单分页查询条件内容类
     */
    @Data
    public static class Query {
        /**
         * 工单类型 1安装工单 2拆除工单
         */
        @Schema(description = "工单类型 1安装工单 2拆除工单", type = "int", example = "1", required = true)
        @NotBlank(message = "工单类型不能为空")
        private Integer workOrderType;

        /**
         * 项目负责人列表
         */
        @Schema(description = "项目负责人列表", type = "array", example = "[\"CC3456\",\"CC4321\"]")
        private List<String> followerWnoList;

        /**
         * 城市ID集合
         */
        @Schema(description = "城市ID集合", type = "array", example = "[510100,510200]")
        private List<Integer> cityIdList;

        /**
         * 楼栋名称
         */
        @Schema(description = "楼栋名称", type = "string", example = "CC1234")
        private String buildingName;
    }
}

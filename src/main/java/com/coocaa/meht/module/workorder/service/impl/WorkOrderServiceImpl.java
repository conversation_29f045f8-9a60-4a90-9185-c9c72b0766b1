package com.coocaa.meht.module.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.module.web.dao.PointMapper;
import com.coocaa.meht.module.web.dto.point.PointDTO;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.UpdateTreeParam;
import com.coocaa.meht.module.web.dto.point.WaitingHallDTO;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.enums.PointNodeTypeEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.WaitingHallService;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.module.workorder.dto.BuildingWorkOrderDetailQueryDTO;
import com.coocaa.meht.module.workorder.dto.OrderPointUpdateDTO;
import com.coocaa.meht.module.workorder.dto.PointCreateCheckQueryDTO;
import com.coocaa.meht.module.workorder.dto.PointListQueryDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderCancelRequestDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderNoticeDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderPageQueryDTO;
import com.coocaa.meht.module.workorder.service.WorkOrderService;
import com.coocaa.meht.module.workorder.vo.BuildingDetailVO;
import com.coocaa.meht.module.workorder.vo.PointCreateCheckVO;
import com.coocaa.meht.module.workorder.vo.PointDetail2WorkOrder;
import com.coocaa.meht.module.workorder.vo.PointResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderBuildingVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderCancelResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderDetailVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.rpc.FeignWorkOrderRpc;
import com.coocaa.meht.rpc.dto.ContractWithPointsVO;
import com.coocaa.meht.rpc.dto.PointsContractStatusVO;
import com.coocaa.meht.rpc.dto.UserFeiShuMessageParam;
import com.coocaa.meht.rpc.dto.WorkOrderPageResult;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工单功能服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WorkOrderServiceImpl implements WorkOrderService {

    /**
     * 飞书应用编码（字典0124）
     */
    private static final String APP_CODE = "0124-1";

    /**
     * 撤销 tip
     */
    private static final List<String> REVOKE_FLAG = List.of("待确认", "待分配", "待受理");
    // 需要上传附件的状态
    private static final List<String> REQUIRE_ATTACHMENT_STATUS = List.of("合同履约中");
    private final FeignWorkOrderRpc workOrderRpc;
    private final FeignCmsRpc cmsRpc;
    private final FeignSspRpc sspRpc;
    private final ConverterFactory converterFactory;
    private final PermissionHandler permissionHandler;
    private final BuildingRatingService buildingRatingService;
    private final PointMapper pointMapper;
    private final UserCacheHelper userCacheHelper;
    private final FeignAuthorityRpc authorityRpc;
    private final WaitingHallService waitingHallService;
    private final PointService pointService;
    private final RsaExample rsaExample;
    private final IBuildingMetaService buildingMetaService;
    private final BusinessOpportunityService businessOpportunityService;

    @Override
    public List<PointResultVO> create(WorkOrderDTO workOrderDTO) {
        workOrderDTO.setPointCount(workOrderDTO.getPointDetailList().size());
        fillBuildingInfo(workOrderDTO);
        log.info("创建工单参数：{}", JSON.toJSONString(workOrderDTO));
        Result<List<PointResultVO>> result = workOrderRpc.addInstallRemoveWorkOrder(workOrderDTO);
        log.info("创建工单结果：{}", JSON.toJSONString(result));
        return result.getData();
    }

    private void fillBuildingInfo(WorkOrderDTO workOrderDTO) {
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(bc2brr(workOrderDTO.getBuildingMetaNo()));
        if (buildingRating == null) {
            throw new ServerException("未找到该楼宇信息");
        }
        workOrderDTO.setBuildingName(buildingRating.getBuildingName());
        workOrderDTO.setBuildingType(BuildingRatingEntity.BuildingType.getNameByValue(buildingRating.getBuildingType()));
        if (StrUtil.isNotBlank(buildingRating.getMapCity())) {
            CodeNameVO city = RpcUtils.unBox(authorityRpc.getByCityName(buildingRating.getMapCity()));
            workOrderDTO.setCityId(city.getId());
            workOrderDTO.setCityName(city.getName());
        }
        if (StrUtil.isNotBlank(buildingRating.getSubmitUser())) {
            workOrderDTO.setFollowerWno(buildingRating.getSubmitUser());
            workOrderDTO.setFollowerName(userCacheHelper.getUser(buildingRating.getSubmitUser()).getName());
        }
        if (StrUtil.isNotBlank(buildingRating.getMapAddress())) {
            workOrderDTO.setAddress(rsaExample.decryptByPrivate(buildingRating.getMapAddress()));
        }
    }

    @Override
    public WorkOrderPageResult<WorkOrderBuildingVO> list(WorkOrderPageQueryDTO workOrderPageQueryDTO) {
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission();
        workOrderPageQueryDTO.getQuery().setCityIdList(cmsPermission.getCityIds());
        workOrderPageQueryDTO.getQuery().setFollowerWnoList(cmsPermission.getUserCodes());
        log.info("查询工单列表参数：{}", JSON.toJSONString(workOrderPageQueryDTO));
        Result<WorkOrderPageResult<WorkOrderBuildingVO>> result = workOrderRpc.pageQueryBuildingWorkOrders(workOrderPageQueryDTO);
        log.info("查询工单列表结果：{}", JSON.toJSONString(result));
        return result.getData();
    }

    @Override
    public BuildingDetailVO buildingDetail(String buildingMetaNo, String workOrderType) {
        BuildingWorkOrderDetailQueryDTO dto = BuildingWorkOrderDetailQueryDTO.builder()
                .workOrderType(workOrderType).buildingMetaNo(buildingMetaNo).build();
        log.info("查询工单楼宇详情参数：{}", JSON.toJSONString(dto));
        BuildingDetailVO data = workOrderRpc.getBuildingWorkOrderDetail(dto).getData();
        fillRevoke(buildingMetaNo, data);
        log.info("查询工单楼宇详情结果：{}", JSON.toJSONString(data));
        return data;
    }

    /**
     * 填充撤销权限
     *
     * @param buildingMetaNo 楼宇编号
     * @param data           楼宇详情
     */
    private void fillRevoke(String buildingMetaNo, BuildingDetailVO data) {
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(bc2brr(buildingMetaNo));
        if (data != null && CollUtil.isNotEmpty(data.getPointDetailList())) {
            boolean match = data.getPointDetailList().stream().anyMatch(
                    point -> REVOKE_FLAG.contains(point.getWorkOrderStatusName()));
            if (match && buildingRating != null
                    && UserThreadLocal.getUser().getWno().equals(buildingRating.getSubmitUser())) {
                data.setCanRevoke(Boolean.TRUE);
            }
        }
    }

    @Override
    public WorkOrderDetailVO workOrderDetail(String workOrderNo) {
        log.info("查询工单详情参数：{}", workOrderNo);
        Result<WorkOrderDetailVO> workOrderDetail = workOrderRpc.getWorkOrderDetail(workOrderNo);
        log.info("查询工单详情结果：{}", JSON.toJSONString(workOrderDetail));

        WorkOrderDetailVO data = workOrderDetail.getData();
        if (data != null) {
            // 填充附件类型
            fillAttachmentTypes(data);
        }

        return data;
    }

    @Override
    public boolean noticeWorkOrderFinished(WorkOrderNoticeDTO workOrderNoticeDTO) {
        log.info("工单通知参数：{}", JSON.toJSONString(workOrderNoticeDTO));

        try {
            String followerWno = workOrderNoticeDTO.getFollowerWno();
            if (StringUtils.isBlank(followerWno)) {
                log.warn("工单通知失败，关注人WNO为空，工单信息: {}", JSON.toJSONString(workOrderNoticeDTO));
                return false;
            }

            List<UserVO> userVOS = RpcUtils.unBox(authorityRpc.listUserByWnos(Collections.singletonList(followerWno)));
            if (CollectionUtils.isEmpty(userVOS)) {
                log.warn("工单通知失败，未找到关注人，WNO: {}, 工单ID: {}", followerWno, workOrderNoticeDTO.getWorkOrderNo());
                return false;
            }

            UserFeiShuMessageParam param = new UserFeiShuMessageParam();
            param.setAppCode(APP_CODE);
            param.setTitle("工单状态变更通知");
            param.setContent(buildMessageContent(workOrderNoticeDTO));
            // 支持多用户通知
            param.setReceiveUserIds(userVOS.stream().map(UserVO::getId).collect(Collectors.toSet()));
            log.info("工单通知飞书参数: {}", JSON.toJSONString(param));
            authorityRpc.sendFeishuMessage(param);
            return true;
        } catch (Exception e) {
            log.error("工单完成通知异常, 工单ID: {}, 错误信息: {}", workOrderNoticeDTO.getWorkOrderNo(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建飞书消息内容
     *
     * @param workOrderNoticeDTO 工单信息
     * @return 工单通知信息   如：你发起的「项目名称：XX；点位编码：XX」的【安装/拆除】工单，当前已经「完成」，请及时跟进后续事务处理
     */
    private String buildMessageContent(WorkOrderNoticeDTO workOrderNoticeDTO) {
        String projectName = workOrderNoticeDTO.getBuildingName();
        String pointCode = workOrderNoticeDTO.getPointCode();
        Integer workOrderType = workOrderNoticeDTO.getWorkOrderType();

        // 工单类型 0安装工单 1拆除工单
        String workOrderTypeName = switch (workOrderType) {
            case 0 -> "安装";
            case 1 -> "拆除";
            default -> throw new BusinessException("未知工单类型");
        };

        String workOrderStatus = workOrderNoticeDTO.getWorkOrderStatusName();

        if ("已撤销".equals(workOrderStatus)) {
            return "你发起的「项目名称："
                    + projectName + "，点位编码："
                    + pointCode + "」的【"
                    + workOrderTypeName + "】工单，当前已经「"
                    + workOrderStatus + "」，关闭原因："
                    + workOrderNoticeDTO.getMessage() + "。请及时跟进后续事务处理";
        }

        return "你发起的「项目名称："
                + projectName + "，点位编码："
                + pointCode + "」的【"
                + workOrderTypeName + "】工单，当前已经「"
                + workOrderStatus + "」，请及时跟进后续事务处理";
    }


    @Override
    public List<ContractWithPointsVO> projectContractPoints(String buildingNo, String workOrderType) {
        List<ContractWithPointsVO> contractWithPoints = RpcUtils.unBox(cmsRpc.queryProjectContractPoints(bc2brr(buildingNo)));
        if (CollUtil.isEmpty(contractWithPoints)) {
            return List.of();
        }

        // 获取楼宇下所有点位Map
        Map<String, PointDetail> allPointMap = getBuildingPointMap(buildingNo);
        if (allPointMap.isEmpty()) {
            return List.of();
        }
        // 构建商机code name  的map
        Map<String, String> businessMap = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getCode,
                        contractWithPoints.stream().map(ContractWithPointsVO::getBusinessCode).toList())
                .list()
                .stream()
                .collect(Collectors.toMap(BusinessOpportunityEntity::getCode,
                        BusinessOpportunityEntity::getName,
                        (oldValue, newValue) -> oldValue));
        log.info("商机map--{}", JSON.toJSONString(businessMap));
        // 填充合同点位详情
        fillPointDetail(contractWithPoints, allPointMap, businessMap);

        // 获取所有点位详情进行统一处理
        List<PointDetail2WorkOrder> allPointDetails = contractWithPoints.stream()
                .filter(Objects::nonNull)
                .map(ContractWithPointsVO::getPointDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 处理点位：检查安装状态、排序、字典转换
        processPointsWithInstallCheck(allPointDetails, workOrderType);

        return contractWithPoints;
    }

    @Override
    public List<PointDetail2WorkOrder> queryPointDetails(PointListQueryDTO queryDTO) {
        if (queryDTO == null || CollUtil.isEmpty(queryDTO.getPointCodes())) {
            log.warn("查询点位详情参数为空");
            return List.of();
        }

        log.info("查询点位详情，楼宇编码: {}, 点位数量: {}", queryDTO.getBuildingNo(), queryDTO.getPointCodes().size());

        // 获取楼宇下所有点位Map
        Map<String, PointDetail> pointMap = getBuildingPointMap(queryDTO.getBuildingNo());
        if (pointMap.isEmpty()) {
            return List.of();
        }

        // 根据请求的点位编码过滤点位详情
        List<PointDetail2WorkOrder> filteredPoints = queryDTO.getPointCodes().stream()
                .filter(Objects::nonNull)
                .map(pointMap::get)
                .map(item -> item == null ? null : BeanUtil.copyProperties(item, PointDetail2WorkOrder.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(filteredPoints)) {
            log.warn("未找到匹配的点位详情，楼宇编码: {}", queryDTO.getBuildingNo());
            return List.of();
        }

        // 处理点位：检查安装状态、排序、字典转换，并映射为返回类型
        List<PointDetail2WorkOrder> result = processPointsWithInstallCheck(filteredPoints, queryDTO.getWorkOrderType());
        log.info("查询点位详情完成，返回点位数量: {}", result.size());
        return result;
    }

    @Override
    public List<WorkOrderCancelResultVO> cancelWorkOrders(WorkOrderCancelRequestDTO request) {
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(bc2brr(request.getBuildingNo()));
        if (buildingRating == null) {
            log.warn("未找到楼宇信息，楼宇编码: {}", request.getBuildingNo());
            throw new ServerException("未找到楼宇信息");
        }
        if (!UserThreadLocal.getUser().getWno().equals(buildingRating.getSubmitUser())) {
            throw new ServerException("不是当前楼宇负责人  无法撤回");
        }
        log.info("取消工单，参数: {}", request);
        Result<List<WorkOrderCancelResultVO>> result = workOrderRpc.cancelWorkOrders(request);
        log.info("取消工单，结果: {}", result);
        return result.getData();
    }

    @Override
    public void workOrderPointEdit(OrderPointUpdateDTO param) {
        //更新楼栋
        if (!Objects.equals(param.getBuildingName(), param.getBuildingOriginVale())) {
            UpdateTreeParam updateTreeBuildingParam = new UpdateTreeParam();
            updateTreeBuildingParam.setType(PointNodeTypeEnum.BUILDING.getCode());
            updateTreeBuildingParam.setBuildingName(param.getBuildingName());
            updateTreeBuildingParam.setOriginVale(param.getBuildingOriginVale());
            updateTreeBuildingParam.setBuildingRatingNo(param.getBuildingRatingNo());
            waitingHallService.updateTreeNode(updateTreeBuildingParam);
        }


        //更新单元
        if (!Objects.equals(param.getUnitName(), param.getUnitOriginVale())) {
            UpdateTreeParam updateTreeUnitParam = new UpdateTreeParam();
            updateTreeUnitParam.setBuildingName(param.getBuildingName());
            updateTreeUnitParam.setType(PointNodeTypeEnum.UNIT.getCode());
            updateTreeUnitParam.setUnitName(param.getUnitName());
            updateTreeUnitParam.setOriginVale(param.getUnitOriginVale());
            updateTreeUnitParam.setBuildingRatingNo(param.getBuildingRatingNo());
            waitingHallService.updateTreeNode(updateTreeUnitParam);
        }


        //更新楼层
        if (!Objects.equals(param.getFloor(), param.getFloorOriginVale())) {
            UpdateTreeParam updateTreeFloorParam = getUpdateTreeFloorParam(param);
            waitingHallService.updateTreeNode(updateTreeFloorParam);
        }


        // 更新等候厅
        WaitingHallDTO waitingHallDTO = getWaitingHallDTO(param);
        waitingHallService.handleWaitingHall(waitingHallDTO);

        //更新点位
        PointDTO pointDTO = getPointDTO(param);
        pointService.handlePoint(pointDTO);

    }

    private UpdateTreeParam getUpdateTreeFloorParam(OrderPointUpdateDTO param) {
        UpdateTreeParam updateTreeFloorParam = new UpdateTreeParam();
        updateTreeFloorParam.setBuildingName(param.getBuildingName());
        updateTreeFloorParam.setUnitName(param.getUnitName());
        updateTreeFloorParam.setType(PointNodeTypeEnum.FLOOR.getCode());
        updateTreeFloorParam.setFloorName(param.getFloor());
        updateTreeFloorParam.setOriginVale(param.getFloorOriginVale());
        updateTreeFloorParam.setBuildingRatingNo(param.getBuildingRatingNo());
        return updateTreeFloorParam;
    }

    private WaitingHallDTO getWaitingHallDTO(OrderPointUpdateDTO param) {
        WaitingHallDTO waitingHallDTO = new WaitingHallDTO();
        waitingHallDTO.setBuildingRatingNo(param.getBuildingRatingNo());
        waitingHallDTO.setWaitingHall(param.getWaitingHallName());
        waitingHallDTO.setWaitingHallType(param.getWaitingHallType());
        waitingHallDTO.setWaitingHallId(param.getWaitingHallId());
        waitingHallDTO.setBuildingName(param.getBuildingName());
        waitingHallDTO.setUnitName(param.getUnitName());
        waitingHallDTO.setFloor(param.getFloor());
        return waitingHallDTO;
    }

    private PointDTO getPointDTO(OrderPointUpdateDTO param) {
        PointDTO updatePointParam = new PointDTO();
        updatePointParam.setDescription(param.getPointRemark());
        updatePointParam.setPointId(param.getPointId());
        updatePointParam.setWaitingHallId(param.getWaitingHallId());
        updatePointParam.setImages(param.getPointPics());
        updatePointParam.setDeviceSize(param.getDeviceSize());
        updatePointParam.setCode(param.getPointCode());
        return updatePointParam;
    }


    /**
     * 检查点位安装状态
     *
     * @param pointDetails  点位详情列表
     * @param workOrderType 工单类型
     */
    private void checkPointInstallStatus(List<PointDetail2WorkOrder> pointDetails, String workOrderType) {
        if (CollUtil.isEmpty(pointDetails)) {
            return;
        }

        // 构建检查请求
        PointCreateCheckQueryDTO request = new PointCreateCheckQueryDTO();
        request.setWorkOrderType(workOrderType);
        List<String> pointCodeList = pointDetails.stream()
                .filter(Objects::nonNull)
                .map(PointDetail2WorkOrder::getPointCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        request.setPointCodeList(pointCodeList);

        // 获取点位创建检查数据
        List<PointCreateCheckVO> checkData;
        log.info("检查点位安装状态，参数: {}", request);
        checkData = workOrderRpc.batchQueryPointCanCreateWorkOrder(request).getData();
        log.info("检查点位安装状态，结果: {}", checkData);
        Map<String, String> deviceMap = pointMapper.listByPointCodes(pointCodeList).stream()
                .filter(Objects::nonNull)
                .filter(point -> StrUtil.isNotBlank(point.getPointCode()))
                .collect(Collectors.toMap(
                        PointDetail::getPointCode,
                        PointDetail::getDeviceSize,
                        (existing, replacement) -> replacement));
        log.info("点位设备尺寸map: {}", deviceMap);

        // 组装点位详情map
        LambdaQueryWrapper<PointEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PointEntity::getCode, pointCodeList);
        Map<String, PointEntity> pointMap = pointMapper.selectList(queryWrapper)
                .stream().collect(Collectors.toMap(
                        PointEntity::getCode,
                        Function.identity(),
                        (existing, replacement) -> replacement));

        // 组装点位合同状态map
        Map<String, String> contractStatusMap = RpcUtils.unBox(
                        cmsRpc.queryPointNewestContractStatus(new ArrayList<>(pointCodeList)))
                .stream().collect(Collectors.toMap(
                        PointsContractStatusVO::getPointCode,
                        PointsContractStatusVO::getContractStatus,
                        (existing, replacement) -> replacement));

        // 组装检查数据Map
        Map<String, PointCreateCheckVO> pointCheckMap = checkData.stream()
                .filter(Objects::nonNull)
                .filter(check -> StrUtil.isNotBlank(check.getPointCode()))
                .collect(Collectors.toMap(
                        PointCreateCheckVO::getPointCode,
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));

        // 更新点位安装状态和消息
        pointDetails.forEach(point -> {
            String pointCode = point.getPointCode();
            if (StrUtil.isNotBlank(pointCode)) {
                point.setDeviceSize(deviceMap.get(pointCode));
                PointEntity pointEntity = pointMap.get(pointCode);
                if (pointEntity != null) {
                    // 填充楼宇编码与商机
                    point.setBuildingRatingNo(pointEntity.getBuildingRatingNo());
                    point.setBusinessCode(pointEntity.getBusinessCode());
                }
                // 填充合同状态
                point.setContractStatus(contractStatusMap.get(pointCode));

                // 填充是否可安装
                PointCreateCheckVO checkVO = pointCheckMap.get(pointCode);
                if (checkVO != null) {
                    point.setCanInstall(Boolean.TRUE.equals(checkVO.getCanCreate()));
                    point.setMessage(checkVO.getMessage());
                } else {
                    point.setCanInstall(false);
                    point.setMessage("点位信息未找到");
                }
            }
        });

        // 如果检查数据为空，所有点位都设置为不可安装
        if (CollUtil.isEmpty(checkData)) {
            pointDetails.forEach(point -> {
                point.setCanInstall(false);
                point.setMessage("无法获取安装状态信息");
            });
        }
    }

    /**
     * 获取楼宇下所有点位的Map
     *
     * @param buildingNo 楼宇编码
     * @return 点位Map，key为点位编码，value为点位详情
     */
    private Map<String, PointDetail> getBuildingPointMap(String buildingNo) {
        List<PointDetail> allPoints = RpcUtils.unBox(sspRpc.pointList(bc2brr(buildingNo)));
        if (CollUtil.isEmpty(allPoints)) {
            log.warn("楼宇 {} 下未找到任何点位信息", buildingNo);
            return Map.of();
        }

        return allPoints.stream()
                .filter(Objects::nonNull)
                .filter(point -> point.getPointCode() != null)
                .collect(Collectors.toMap(
                        PointDetail::getPointCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 处理点位详情：检查安装状态、排序、字典转换
     *
     * @param points        点位详情列表
     * @param workOrderType 工单类型
     */
    private List<PointDetail2WorkOrder> processPointsWithInstallCheck(List<PointDetail2WorkOrder> points, String workOrderType) {
        if (CollUtil.isEmpty(points)) {
            return List.of();
        }
        // 检查安装状态
        checkPointInstallStatus(points, workOrderType);

        //排序
        points.sort((p1, p2) -> Boolean.compare(
                Boolean.TRUE.equals(p2.getCanInstall()),
                Boolean.TRUE.equals(p1.getCanInstall())
        ));

        // 拆除时填充是否需要附件
        if ("1".equals(workOrderType)) {
            points.forEach(point ->
                    point.setRequireAttachment(REQUIRE_ATTACHMENT_STATUS.contains(point.getContractStatus())));
        }

        // 字典转换
        converterFactory.convert(points);
        return points;
    }

    /**
     * 填充合同点位详情
     *
     * @param contractWithPoints 合同点位列表
     * @param allPointMap        所有点位Map，key为点位编码，value为点位详情
     */
    private void fillPointDetail(List<ContractWithPointsVO> contractWithPoints,
                                 Map<String, PointDetail> allPointMap,
                                 Map<String, String> businessMap) {
        if (CollUtil.isEmpty(contractWithPoints) || allPointMap.isEmpty()) {
            return;
        }

        contractWithPoints.stream()
                .filter(Objects::nonNull)
                .filter(contract -> CollUtil.isNotEmpty(contract.getPoints()))
                .forEach(contract -> {
                    contract.setBusinessName(businessMap.get(contract.getBusinessCode()));
                    List<PointDetail> pointDetails = contract.getPoints().stream()
                            .filter(Objects::nonNull)
                            .map(allPointMap::get)
                            .filter(Objects::nonNull)
                            .toList();
                    contract.setPointDetails(BeanUtil.copyToList(pointDetails, PointDetail2WorkOrder.class));
                });
    }

    /**
     * BC转BRR
     *
     * @param bc BC编码
     * @return BRR编码
     */
    private String bc2brr(String bc) {
        if (StrUtil.isBlank(bc)) {
            return null;
        }
        BuildingMetaEntity one = buildingMetaService.lambdaQuery()
                .eq(BuildingMetaEntity::getBuildingMetaNo, bc)
                .last("LIMIT 1").one();
        if (one == null) {
            return null;
        }
        return one.getBuildingRatingNo();
    }

    /**
     * 填充附件类型
     * 根据URL后缀填充附件类型
     *
     * @param workOrderDetailVO 工单详情
     */
    private void fillAttachmentTypes(WorkOrderDetailVO workOrderDetailVO) {
        if (workOrderDetailVO == null || CollUtil.isEmpty(workOrderDetailVO.getAttachment())) {
            return;
        }

        workOrderDetailVO.getAttachment().forEach(attachment -> {
            if (StrUtil.isNotBlank(attachment.getUrl()) && StrUtil.isBlank(attachment.getType())) {
                String fileType = getFileTypeFromUrl(attachment.getUrl());
                attachment.setType(fileType);
            }
        });
    }

    /**
     * 从URL获取文件类型
     *
     * @param url 文件URL
     * @return 文件类型
     */
    private String getFileTypeFromUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "unknown";
        }

        // 获取文件扩展名
        String extension = "";
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            extension = url.substring(lastDotIndex + 1).toLowerCase();
        }

        // 根据扩展名返回文件类型
        return switch (extension) {
            case "jpg", "jpeg", "png", "gif", "bmp", "webp" -> "image";
            case "pdf" -> "pdf";
            case "doc", "docx" -> "word";
            case "xls", "xlsx" -> "excel";
            case "ppt", "pptx" -> "powerpoint";
            case "txt" -> "text";
            case "zip", "rar", "7z" -> "archive";
            case "mp4", "avi", "mov", "wmv", "flv" -> "video";
            case "mp3", "wav", "flac", "aac" -> "audio";
            default -> "unknown";
        };
    }

}

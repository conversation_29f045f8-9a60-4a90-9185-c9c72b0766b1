package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 点位列表查询请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-18
 */
@Data
@Schema(description = "点位列表查询请求DTO")
public class PointListQueryDTO {

    /**
     * 楼宇编码
     */
    @NotBlank(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码", example = "B001", required = true)
    private String buildingNo;

    /**
     * 点位编码列表
     */
    @NotEmpty(message = "点位编码列表不能为空")
    @Schema(description = "点位编码列表", example = "[\"P001\", \"P002\", \"P003\"]", required = true)
    private List<String> pointCodes;


    /**
     * 工单类型（检查安装状态时使用）
     */
    @Schema(description = "工单类型：1-安装工单 2-拆除工单，默认为1", example = "1")
    private String workOrderType = "1";
}

package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-08-13
 */
@Data
public class WorkOrderAttachmentDTO {


    @Schema(description = "附件地址")
    private String url;

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "附件类型")
    private String type;
}

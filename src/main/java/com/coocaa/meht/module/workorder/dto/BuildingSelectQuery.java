package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-07-17
 */
@Data
public class BuildingSelectQuery {


    @Schema(description = "名称")
    private String name;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "提交人")
    private String submitUser;
}

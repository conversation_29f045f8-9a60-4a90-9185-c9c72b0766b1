package com.coocaa.meht.module.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 工单楼宇详情VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */

@Data
@Schema(description = "工单楼宇详情VO")
public class BuildingDetailVO {

    @Schema(description = "楼宇名称", example = "万达广场A座")
    private String buildingName;

    @Schema(description = "楼宇编码", example = "BCX001")
    private String buildingMetaNo;

    @Schema(description = "楼宇类型", example = "写字楼")
    private String buildingType;

    @Schema(description = "楼宇地址", example = "楼宇地址")
    private String address;

    @Schema(description = "能否撤回", example = "true")
    private Boolean canRevoke = Boolean.FALSE;

    @Schema(description = "点位详情列表")
    private List<PointDetailVO> pointDetailList;
}

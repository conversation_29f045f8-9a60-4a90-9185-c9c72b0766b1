package com.coocaa.meht.module.workorder.controller;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.vo.BuildingRating2WorkOrderVO;
import com.coocaa.meht.module.workorder.dto.BuildingNameQuery;
import com.coocaa.meht.module.workorder.dto.BuildingSelectQuery;
import com.coocaa.meht.module.workorder.dto.OrderPointUpdateDTO;
import com.coocaa.meht.module.workorder.dto.PointListQueryDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderCancelRequestDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderNoticeDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderPageQueryDTO;
import com.coocaa.meht.module.workorder.service.WorkOrderService;
import com.coocaa.meht.module.workorder.vo.BuildingDetailVO;
import com.coocaa.meht.module.workorder.vo.PointDetail2WorkOrder;
import com.coocaa.meht.module.workorder.vo.PointResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderBuildingVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderCancelResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderDetailVO;
import com.coocaa.meht.rpc.dto.ContractWithPointsVO;
import com.coocaa.meht.rpc.dto.WorkOrderPageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 工单相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@RestController
@RequestMapping("/work/order")
@Tag(name = "工单相关接口", description = "工单相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class WorkOrderController {

    private final WorkOrderService workOrderService;
    private final BuildingRatingService buildingRatingService;

    /**
     * 创建工单
     *
     * @param workOrderDTO 工单信息
     * @return 工单创建结果
     */
    @PostMapping
    @Operation(summary = "创建安装/拆除工单")
    @RepeatSubmit
    public Result<List<PointResultVO>> create(@Validated @RequestBody WorkOrderDTO workOrderDTO) {
        log.info("创建工单:入参：{}", JSON.toJSONString(workOrderDTO));
        List<PointResultVO> result = workOrderService.create(workOrderDTO);
        log.info("创建工单:返回：{}", JSON.toJSONString(result));
        return Result.ok(result);
    }

    /**
     * 分页获取工单列表
     *
     * @param workOrderPageQueryDTO 查询条件
     * @return 工单列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取工单列表")
    public Result<WorkOrderPageResult<WorkOrderBuildingVO>> list(@RequestBody WorkOrderPageQueryDTO workOrderPageQueryDTO) {
        log.info("获取工单列表:入参：{}", JSON.toJSONString(workOrderPageQueryDTO));
        WorkOrderPageResult<WorkOrderBuildingVO> result = workOrderService.list(workOrderPageQueryDTO);
        log.info("获取工单列表:返回：{}", JSON.toJSONString(result));
        return Result.ok(result);
    }

    /**
     * 获取楼宇工单详情
     *
     * @return 楼宇工单详情
     */
    @GetMapping("/building")
    @Operation(summary = "获取楼宇工单详情")
    public Result<BuildingDetailVO> buildingDetail(@Parameter(description = "楼宇编码") @RequestParam String buildingMetaNo,
                                                   @Parameter(description = "工单类型") @RequestParam String workOrderType) {
        log.info("获取楼宇工单详情:入参：{}", buildingMetaNo);
        BuildingDetailVO result = workOrderService.buildingDetail(buildingMetaNo, workOrderType);
        log.info("获取楼宇工单详情:返回：{}", result);
        return Result.ok(result);
    }

    /**
     * 获取工单详情
     *
     * @return 楼宇工单详情
     */
    @GetMapping("/detail/{workOrderNo}")
    @Operation(summary = "获取楼宇工单详情")
    public Result<WorkOrderDetailVO> workOrderDetail(@Parameter(description = "工单编号")
                                                     @PathVariable("workOrderNo") String workOrderNo) {
        log.info("workOrderDetail:入参：{}", workOrderNo);
        WorkOrderDetailVO result = workOrderService.workOrderDetail(workOrderNo);

        log.info("workOrderDetail:返回：{}", result);
        return Result.ok(result);
    }

    /**
     * 通知工单已完成
     *
     * @param workOrderNoticeDTO 通知参数
     * @return 操作结果
     */
    @PostMapping("/notice/finish")
    @Operation(summary = "通知工单已完成")
    public Result<Boolean> noticeWorkOrderFinished(@RequestBody WorkOrderNoticeDTO workOrderNoticeDTO) {
        boolean result = workOrderService.noticeWorkOrderFinished(workOrderNoticeDTO);
        return Result.ok(result);
    }


    /**
     * 获取我的楼宇
     *
     * @param query 查询参数
     * @return 我的楼宇列表
     */
    @PostMapping("/list-building")
    @Operation(summary = "获取我的楼宇")
    public Result<PageResponseVO<BuildingRating2WorkOrderVO>> listBuilding(
            @RequestBody PageRequestVO<BuildingSelectQuery> query) {
        query.getQuery().setSubmitUser(UserThreadLocal.getUser().getWno());
        return Result.ok(buildingRatingService.list2workOrder(query));
    }

    /**
     * 根据BC编码获取楼宇名称
     *
     * @param query 查询参数
     * @return map  key为BC编码 value为楼宇名称
     */
    @PostMapping("/query-building-name")
    @Operation(summary = "根据BC编码获取楼宇名称")
    public Result<Map<String, String>> queryBuildingName(@RequestBody @Validated BuildingNameQuery query) {
        return Result.ok(buildingRatingService.queryBuildingName(query));
    }

    /**
     * 获取合同以及点位
     *
     * @param buildingNo 楼宇编号
     * @return 合同以及点位
     */
    @GetMapping("/{buildingNo}/project-contract-points")
    @Operation(summary = "合同以及点位")
    public Result<List<ContractWithPointsVO>> projectContractPoints(@PathVariable("buildingNo") String buildingNo,
                                                                    @RequestParam(name = "workOrderType") String workOrderType) {
        log.info("projectContractPoints:入参：{}", buildingNo);
        List<ContractWithPointsVO> result = workOrderService.projectContractPoints(buildingNo, workOrderType);
        log.info("projectContractPoints:返回：{}", result);
        return Result.ok(result);
    }

    /**
     * 根据工单编号撤销工单
     *
     * @param request 包含工单编号集合的请求对象
     * @return 撤销结果列表
     */
    @Operation(summary = "根据工单编号撤销工单")
    @PostMapping("/bcs/api/installRemove/workOrder/cancelled")
    @RepeatSubmit
    public Result<String> cancelWorkOrders(@RequestBody WorkOrderCancelRequestDTO request) {
        log.info("cancelWorkOrders:入参：{}", request);
        List<WorkOrderCancelResultVO> resultList = workOrderService.cancelWorkOrders(request);
        log.info("cancelWorkOrders:返回：{}", resultList);

        // 判断整体成功状态：全部成功则为true，有任何失败则为false
        boolean allSuccess = resultList.stream()
                .allMatch(result -> Boolean.TRUE.equals(result.getCancelledSuccess()));

        String combinedMessage = resultList.stream()
                .filter(result -> !result.getCancelledSuccess())
                .map(item -> "[点位编码:" + item.getPointCode() + "撤回失败，失败原因：" + item.getMessage() + "]")
                .filter(message -> !message.trim().isEmpty())
                .reduce((msg1, msg2) -> msg1 + "\n" + msg2)
                .orElse("");

        // 创建返回结果并设置success和msg字段
        Result<String> result = new Result<>();
        result.setSuccess(allSuccess);
        if (!allSuccess) {
            result.setMsg(combinedMessage);
        }
        return result;
    }

    /**
     * 根据点位编码列表查询点位详情
     *
     * @param queryDTO 点位查询参数
     * @return 点位详情列表
     */
    @PostMapping("/points/query")
    @Operation(summary = "查询点位详情", description = "根据点位编码列表查询点位详情，支持检查安装状态")
    public Result<List<PointDetail2WorkOrder>> queryPointDetails(@Validated @RequestBody PointListQueryDTO queryDTO) {
        log.info("查询点位详情请求: 楼宇编码={}, 点位数量={}",
                queryDTO.getBuildingNo(), queryDTO.getPointCodes().size());
        List<PointDetail2WorkOrder> result = workOrderService.queryPointDetails(queryDTO);
        log.info("查询点位详情完成，返回点位数量: {}", result.size());
        return Result.ok(result);
    }

    /**
     * 工单点位信息编辑
     */
    @Operation(summary = "工单点位信息编辑")
    @PostMapping("/order-point-edit")
    @RepeatSubmit
    public Result<String> workOrderPointEdit(@Validated @RequestBody OrderPointUpdateDTO param) {
        workOrderService.workOrderPointEdit(param);
        return Result.ok();
    }

}

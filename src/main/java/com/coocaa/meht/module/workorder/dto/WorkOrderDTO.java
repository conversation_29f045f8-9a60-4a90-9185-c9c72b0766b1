package com.coocaa.meht.module.workorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 新建安装/拆除工单参数 DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Data
public class WorkOrderDTO {

    /**
     * 楼宇编码
     */
    @NotEmpty(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码", type = "String", example = "1")
    private String buildingMetaNo;

    /**
     * 楼宇名称
     */
//    @NotEmpty(message = "楼宇名称不能为空")
    @Schema(description = "楼宇名称", type = "String", example = "天府大厦")
    private String buildingName;

    /**
     * 楼宇类型
     */
//    @NotEmpty(message = "楼宇类型不能为空")
    @Schema(description = "楼宇类型", type = "String", example = "写字楼")
    private String buildingType;

    /**
     * 合同编码
     */
//    @NotEmpty(message = "合同编码不能为空")
    @Schema(description = "合同编码", type = "String", example = "HT2025001")
    private String contractCode;


    @Schema(description = "附件列表", type = "array")
    private List<WorkOrderAttachmentDTO> attachment;

    /**
     * 楼宇负责人工号
     */
//    @NotEmpty(message = "楼宇负责人工号不能为空")
    @Schema(description = "楼宇负责人工号", type = "String", example = "W123456")
    private String followerWno;

    /**
     * 楼宇负责人姓名
     */
//    @NotEmpty(message = "楼宇负责人姓名不能为空")
    @Schema(description = "楼宇负责人姓名", type = "String", example = "张三")
    private String followerName;

    /**
     * 城市ID
     */
//    @NotNull(message = "城市ID不能为空")
    @Schema(description = "城市ID", type = "Integer", example = "510100")
    private Integer cityId;

    /**
     * 城市名称
     */
//    @NotEmpty(message = "城市名称不能为空")
    @Schema(description = "城市名称", type = "String", example = "成都")
    private String cityName;

    @Schema(description = "地址")
    private String address;

    /**
     * 点位数
     */
//    @NotNull(message = "点位数不能为空")
    @Schema(description = "点位数", type = "Integer", example = "10")
    private Integer pointCount;

    /**
     * 工单类型 1安装工单 2拆除工单
     */
    @NotNull(message = "工单类型不能为空")
    @Schema(description = "工单类型 1安装工单 2拆除工单", type = "Integer", example = "1")
    private Integer workOrderType;

    /**
     * 施工日期
     */
    @NotEmpty(message = "施工日期不能为空")
    @Schema(description = "施工日期", type = "String", example = "2025-07-15")
    private String constructionDate;

    /**
     * 施工时段
     */
    @NotEmpty(message = "施工时段不能为空")
    @Schema(description = "施工时段", type = "String", example = "上午")
    private String constructionPeriod;

    /**
     * 施工说明
     */
//    @NotNull(message = "施工说明不能为空")
    @Schema(description = "施工说明", type = "object", example = "{\"remark\":\"注意安全\"}")
    private Map<String, String> constructionDescription;

    /**
     * 联系BD姓名
     */
//    @NotEmpty(message = "联系BD姓名不能为空")
    @Schema(description = "联系BD姓名", type = "String", example = "李四")
    private String bdContactPerson;

    /**
     * 联系BD工号
     */
//    @NotEmpty(message = "联系BD工号不能为空")
    @Schema(description = "联系BD工号", type = "String", example = "BD123456")
    private String bdContactPersonWno;

    /**
     * BD联系方式
     */
    @NotEmpty(message = "BD联系方式不能为空")
    @Schema(description = "BD联系方式", type = "String", example = "13800000000")
    private String bdContactPhone;

    /**
     * 物业对接人
     */
    @NotEmpty(message = "物业对接人不能为空")
    @Schema(description = "物业对接人", type = "String", example = "王五")
    private String propertyContactPerson;

    /**
     * 物业联系方式
     */
    @NotEmpty(message = "物业联系方式不能为空")
    @Schema(description = "物业联系方式", type = "String", example = "13900000000")
    private String propertyContactPhone;

    /**
     * 点位详情列表
     */
    @NotEmpty(message = "点位详情列表不能为空")
    @Schema(description = "点位详情列表", type = "array")
    private List<PointDetailDTO> pointDetailList;
}

package com.coocaa.meht.module.dataimport.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.dataimport.pojo.BasicInfoAllDTO;
import com.coocaa.meht.module.dataimport.pojo.BasicInfoAllVO;
import com.coocaa.meht.module.dataimport.pojo.BuildingLocationUpdateDTO;
import com.coocaa.meht.module.dataimport.pojo.BuildingRatingVO;
import com.coocaa.meht.module.dataimport.pojo.BusinessOpportunityParam;
import com.coocaa.meht.module.dataimport.pojo.PointData;
import com.coocaa.meht.module.dataimport.pojo.PointPlanUpdateStatusBO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyUpdateStatusBO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyVO;
import com.coocaa.meht.module.dataimport.pojo.UpdateCustomerDataParam;
import com.coocaa.meht.module.dataimport.pojo.UpdatePointPlanStatusDTO;
import com.coocaa.meht.module.dataimport.pojo.UpdateRatingStatusDTO;
import com.coocaa.meht.module.dataimport.service.BusinessDataService;
import com.coocaa.meht.module.dataimport.service.PriceApplyService;
import com.coocaa.meht.module.sys.dto.FeiShuUserInfoDto;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.PointPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 历史合同数据清洗
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Slf4j
@Tag(name = "数据清洗")
@RestController
@RequestMapping("/data-import")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DataImportController {
    private final PriceApplyService priceApplyService;
    private final BuildingRatingService buildingRatingService;
    private final BusinessDataService businessDataService;
    private final BusinessOpportunityService businessOpportunityService;
    private final PointPlanService pointPlanService;
    private final SysUserService sysUserService;

    @Anonymous
    @PostMapping("/price-apply")
    public Result<String> createPriceApply(@RequestBody PriceApplyVO priceApply) {
        String applyCode = priceApplyService.createPriceApply(priceApply);
        return Result.ok(applyCode);
    }

    @Anonymous
    @GetMapping("/business-code")
    public Result<String> getBusinessCode(@RequestParam("buildingNo") String buildingNo) {
        if (StringUtils.isBlank(buildingNo)) return Result.ok("");

        BusinessOpportunityEntity business = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getCode)
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingNo)
                .orderByDesc(BusinessOpportunityEntity::getId)
                .last("limit 1")
                .one();
        return Result.ok(Optional.ofNullable(business).map(BusinessOpportunityEntity::getCode).orElse(""));
    }

    @Anonymous
    @PostMapping("/building-rating/change-owner")
    public Result<Boolean> updateBuildingRatingOwner(@RequestBody BuildingRatingVO buildingRating) {
        boolean result = buildingRatingService.lambdaUpdate()
                .set(StringUtils.isNotBlank(buildingRating.getWno()), BuildingRatingEntity::getCreateBy, buildingRating.getWno())
                .set(StringUtils.isNotBlank(buildingRating.getWno()), BuildingRatingEntity::getSubmitUser, buildingRating.getWno())
                .set(StringUtils.isNotBlank(buildingRating.getWno()), BuildingRatingEntity::getUpdateBy, buildingRating.getWno())
                .set(Objects.nonNull(buildingRating.getCreateTime()), BuildingRatingEntity::getCreateTime, buildingRating.getCreateTime())
                .set(Objects.nonNull(buildingRating.getCreateTime()), BuildingRatingEntity::getUpdateTime, buildingRating.getCreateTime())
                .set(BuildingRatingEntity::getCrmPushStatus, 9)
                .eq(BuildingRatingEntity::getBuildingNo, buildingRating.getBuildingNo())
                .and(i -> i.isNull(BuildingRatingEntity::getCustomerId).or(j -> j.eq(BuildingRatingEntity::getCustomerId, "")))
                .update();
        return Result.ok(result);
    }


    @Operation(summary = "同步商机数据")
    @Anonymous
    @GetMapping("/syc-business-data")
    public Result<Boolean> sycBusinessData(@RequestParam(name = "buildingNo", required = false) String buildingNo) {
        return Result.ok(businessDataService.sysContractData(buildingNo));
    }

    @Operation(summary = "同步点位数据")
    @Anonymous
    @PostMapping("/syc-point-data")
    public Result<Boolean> sycPointData(@RequestBody PointData pointData) {
        return Result.ok(businessDataService.syncPointData(pointData));
    }

    @PostMapping("/update-business-status")
    @Anonymous
    public void updateBusinessStatus(@RequestBody List<BusinessOpportunityParam> params) {
        businessDataService.updateBusinessStatus(params);
    }

    @PostMapping("/basic-info-all")
    @Operation(summary = "楼宇基本数据详情")
    public Result<List<BasicInfoAllVO>> basicInfoAll(@RequestBody BasicInfoAllDTO param) {
        List<BasicInfoAllVO> basicInfoAll = businessDataService.getBasicInfoAll(param);
        return Result.ok(basicInfoAll);
    }

    @Operation(summary = "清洗跟进的业务类型")
    @GetMapping("/update-business-type")
    @Anonymous
    public void updateBusinessType() {
        businessDataService.updateBusinessType();
    }

    @Operation(summary = "清洗跟进角色、电话")
    @GetMapping("/update-role-phone")
    @Anonymous
    public void updateRolePhone() {
        businessDataService.updateRolePhone();
    }

    @Operation(summary = "清洗跟进有效无效")
    @GetMapping("/update-valid")
    @Anonymous
    public void updateValid() {
        businessDataService.updateValid();
    }


    @Operation(summary = "更新点位方案状态")
    @PostMapping("/update-point-plan-status")
    @Anonymous
    public void updatePointPlanStatus(@RequestBody @Validated PointPlanUpdateStatusBO bo) {
        pointPlanService.updatePointPlanStatus(bo);
    }

    @Operation(summary = "更新价格申请状态")
    @PostMapping("/update-price-apply-status")
    @Anonymous
    public void updatePointApplyStatus(@RequestBody @Validated PriceApplyUpdateStatusBO bo) {
        priceApplyService.updatePointApplyStatus(bo);
    }


    @Operation(summary = "洗楼宇数据mata")
    @PostMapping("/add-meta")
    @Anonymous
    public void addBuildingMeta(@RequestBody List<String> buildingNos) {
        businessDataService.addBuildingMeta(buildingNos);
    }

    @Operation(summary = "洗楼宇数据商机")
    @PostMapping("/add-meta-business")
    @Anonymous
    public void addMetaBuildingBusiness() {
        businessDataService.addMetaBuildingBusiness();
    }


    @Operation(summary = "修改客户数据")
    @PostMapping("/update-customer-data")
    @Anonymous
    public Result<Boolean> updateCustomerData(@RequestBody List<UpdateCustomerDataParam> params) {
        businessDataService.updateCustomerData(params);
        return Result.ok(true);
    }

    @Operation(summary = "刷公海时间")
    @GetMapping("/refresh-high-sea-time")
    @Anonymous
    public Result<Boolean> refreshHighSeaTime(@RequestParam(required = false) String enterSeaCalculateTime) {
        businessDataService.refreshHighSeaTime(enterSeaCalculateTime);
        return Result.ok(true);
    }

    @Operation(summary = "刷新楼宇评级标记字段")
    @PostMapping("/refresh-rating-flag")
    @Anonymous
    public Result<Map<String, List<Long>>> refreshRatingFlag(@RequestBody(required = false) List<Long> buildingIds) {
        return Result.ok(businessDataService.refreshRatingFlag(buildingIds));
    }

    @Operation(summary = "同步基因")
    @GetMapping("/refresh-gene")
    @Anonymous
    public Result<Boolean> refreshGene() {
        return Result.ok(businessDataService.refreshGene());
    }


    @Operation(summary = "洗记录数据")
    @Anonymous
    @PostMapping("/update-approve-record")
    public Result<Boolean> updateApproveRecord(@RequestBody List<String> buildingNoList) {
        businessDataService.updateApproveRecord(buildingNoList);
        return Result.ok();
    }

    @Operation(summary = "价格洗记录数据")
    @Anonymous
    @PostMapping("/update-approve-record-price")
    public Result<Boolean> updateApproveRecordPrice() {
        businessDataService.updateApproveRecordPrice();
        return Result.ok();
    }


    @Operation(summary = "删除录数据")
    @PostMapping("/delete-approve-record")
    public Result<Boolean> deleteApproveRecord(@RequestBody List<Integer> ids) {
        businessDataService.deleteApproveRecord(ids);
        return Result.ok();
    }


    @Operation(summary = "修改飞书用户信息")
    @Anonymous
    @PutMapping("/update-feishu-user")
    public Result<Boolean> updateFeiShuUser(@RequestBody FeiShuUserInfoDto feiShuUserInfoDto) {
        sysUserService.updateFeiShuUser(feiShuUserInfoDto);
        return Result.ok(true);
    }


    @Operation(summary = "清洗商机状态")
    @Anonymous
    @PostMapping("/refresh-business-status")
    public Result<Boolean> refreshBusinessStatus(@RequestBody List<String> businessCodes) {
        businessDataService.refreshBusinessStatus(businessCodes);
        return Result.ok(true);
    }

    @Operation(summary = "修改评级")
    @Anonymous
    @PostMapping("/update-rating-status")
    public Result<Boolean> updateRatingStatus(@RequestBody @Valid UpdateRatingStatusDTO updateRatingStatusDTO) {
        return Result.ok(businessDataService.updateRatingStatus(updateRatingStatusDTO));
    }

    @Operation(summary = "待审核的数据，统一处理成审核不通过")
    @Anonymous
    @GetMapping("/update-not-approve-rating")
    public Result<Boolean> updateNotApproveRating() {
        return Result.ok(businessDataService.updateNotApproveRating());
    }

    @Operation(summary = "清理重复点位")
    @Anonymous
    @PostMapping("/clean-repeat-point")
    public Result<Boolean> cleanRepeatPoint(@RequestBody List<String> businessCodes) {
        businessDataService.cleanRepeatPoint(businessCodes);
        return Result.ok(true);
    }


    @Operation(summary = "刷新快照点位")
    @Anonymous
    @PostMapping("/clean-repeat-point-snapshot")
    public Result<Boolean> cleanRepeatPointSnapshot(@RequestBody List<String> businessCodes) {
        businessDataService.cleanRepeatPointSnapshot(businessCodes);
        return Result.ok(true);
    }

    @Operation(summary = "更新点位方案状态")
    @Anonymous
    @PostMapping("/update-point-business-plan-status")
    public Result<Boolean> updatePointPlanStatus(@RequestBody UpdatePointPlanStatusDTO param) {
        businessDataService.updatePointPlanStatus(param);
        return Result.ok(true);
    }

    @Operation(summary = "刷申请标识")
    @Anonymous
    @GetMapping("/refresh-apply-flag")
    public Result<Boolean> refreshApplyFlag() {
        businessDataService.refreshApplyFlag();
        return Result.ok(true);
    }


    @Operation(summary = "刷楼栋大屏参数")
    @Anonymous
    @GetMapping("/refresh-building-screen")
    public Result<Boolean> refreshBuildingScreen() {
        businessDataService.refreshBuildingScreen();
        return Result.ok(true);
    }


    @Operation(summary = "历史审核节点数据清洗")
    @Anonymous
    @GetMapping("/refresh-approve-record")
    public Result<Boolean> refreshApproveRecord() {
        businessDataService.refreshApproveRecord();
        return Result.ok(true);
    }

    @Operation(summary = "清洗基因数据")
    @Anonymous
    @GetMapping("/refresh-building-gene")
    public Result<Boolean> refreshBuildingGene(@RequestParam(required = false) Integer id) {
        businessDataService.refreshBuildingGene(id);
        return Result.ok(true);
    }

    @Operation(summary = "清洗日租金")
    @Anonymous
    @GetMapping("/refresh-daily-rent")
    public Result<Boolean> refreshDailyRent() {
        businessDataService.refreshDailyRent();
        return Result.ok(true);
    }

    @Operation(summary = "清洗评级详情")
    @Anonymous
    @PostMapping("/refresh-detail")
    public Result<Boolean> refreshDetail(
            @RequestParam(required = false) Integer id,
            @RequestBody(required = false) List<String> buildingNos) {
        businessDataService.refreshDetail(id, buildingNos);
        return Result.ok(true);
    }

    @Operation(summary = "清洗价格申请")
    @Anonymous
    @PostMapping("/refresh-price-apply")
    public Result<Boolean> refreshPriceApply(@RequestBody(required = false) List<String> applyCodes) {
        businessDataService.refreshPriceApply(applyCodes);
        return Result.ok(true);
    }

    @Operation(summary = "清洗禁忌行业")
    @Anonymous
    @GetMapping("/refresh-forbidden-industry")
    public Result<Boolean> refreshForbiddenIndustry() {
        businessDataService.refreshForbiddenIndustry();
        return Result.ok(true);
    }


    @Operation(summary = "历史审核节点数据清洗")
    @Anonymous
    @GetMapping("/refresh-approve-record-creator")
    public Result<Boolean> refreshApproveRecordCreator() {
        businessDataService.refreshApproveRecordCreator();
        return Result.ok(true);
    }

    @Operation(summary = "刷新价格申请核心区")
    @Anonymous
    @GetMapping("/refresh-price-apply-core-area")
    public Result<Boolean> refreshPriceApplyCoreArea() {
        businessDataService.refreshPriceApplyCoreArea();
        return Result.ok(true);
    }


    /**
     * 洗数据
     */
    @Operation(summary = "楼宇洗数据")
    @PostMapping("/building-data-correct")
    public Result<String> buildingDataCorrect(@RequestParam("projectFile") MultipartFile projectFile) throws IOException {
        String s = businessDataService.buildingDataCorrect(projectFile);
        return Result.ok(s);
    }


    @Operation(summary = "更新楼宇位置信息")
    @PostMapping("/update-building-location")
    public Result<Boolean> updateBuildingLocation(@RequestBody @Valid BuildingLocationUpdateDTO buildingLocationUpdateDTO) {
        Boolean result = businessDataService.updateBuildingLocation(buildingLocationUpdateDTO);
        return Result.ok(result);
    }
}

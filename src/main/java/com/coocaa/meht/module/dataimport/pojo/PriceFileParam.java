package com.coocaa.meht.module.dataimport.pojo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@Data
public class PriceFileParam {
    @ExcelProperty(value = "合同ID")
    private String contractId;

    @ExcelProperty(value = "合同编码")
    private String contractNo;

    @ExcelProperty(value = "项目名称")
    private String buildingName;

    @ExcelProperty(value = "项目ID")
    private String projectId;

    @ExcelProperty(value = "商机编码 (BRR)")
    private String businessCode;

    @ExcelProperty(value = "价格申请编码")
    private String priceApplyCode;

    @ExcelProperty(value = "点位编码")
    private String pointCode;

    @ExcelIgnore
    private Integer rowNum;
}

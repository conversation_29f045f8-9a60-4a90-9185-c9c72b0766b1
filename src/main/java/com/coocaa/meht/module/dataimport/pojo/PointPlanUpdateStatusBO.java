package com.coocaa.meht.module.dataimport.pojo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  更新点位方案状态
 * @since 2025-04-08
 */
@Data
public class PointPlanUpdateStatusBO {

    @NotEmpty
    private List<Integer> pointPlanIds;

    @NotBlank
    private String status;
}

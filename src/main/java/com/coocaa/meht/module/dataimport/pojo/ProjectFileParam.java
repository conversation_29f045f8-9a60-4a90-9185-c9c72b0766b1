package com.coocaa.meht.module.dataimport.pojo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@Data
public class ProjectFileParam {
    /**
     * 数据行数
     */
    @ExcelIgnore
    private Integer rowNum;

    @ExcelProperty(value = "项目名称")
    private String buildingName;

    @ExcelProperty(value = "楼宇编码 (BC)")
    private String buildingMetaNo;

    @ExcelProperty(value = "楼宇编码(BRR)")
    private String buildingNo;

    @ExcelProperty(value = "新楼宇编码 (BC)")
    private String buildingMetaNoNew;

    @ExcelProperty(value = "新楼宇编码 (BRR)")
    private String buildingNoNew;

    @ExcelProperty(value = "省")
    private String mapProvince;

    @ExcelProperty(value = "市")
    private String mapCity;

    @ExcelProperty(value = "区")
    private String mapRegion;

    @ExcelProperty(value = "行政区域 编码")
    private String mapCode;

    @ExcelProperty(value = "经度")
    private String mapLatitude;

    @ExcelProperty(value = "纬度")
    private String mapLongitude;

    @ExcelProperty(value = "详细地址")
    private String mapAddress;

    @ExcelProperty(value = "楼宇类型")
    private String buildingType;

    @ExcelProperty(value = "点位数量")
    private String pointNum;

    @ExcelProperty(value = "楼宇BD工号")
    private String bDWorkNo;

    @ExcelProperty(value = "合同ID")
    private String contractId;

    @ExcelProperty(value = "合同编码")
    private String contractNo;

    @ExcelProperty(value = "项目ID")
    private String projectId;

    @ExcelProperty(value = "商机编码 (BRR)")
    private String businessCode;

    @ExcelProperty(value = "价格申请编码")
    private String priceApplyCode;

    @ExcelProperty(value = "是否保留（0否、1是）")
    private String isKeep;

    @ExcelProperty(value = "合同点位")
    private String points;

    @ExcelIgnore
    private List<String> pointCodes;

}

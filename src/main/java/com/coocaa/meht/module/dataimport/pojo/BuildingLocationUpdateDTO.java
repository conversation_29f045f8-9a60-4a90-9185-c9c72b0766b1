package com.coocaa.meht.module.dataimport.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 楼宇位置信息更新DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Data
@Schema(description = "楼宇位置信息更新DTO")
public class BuildingLocationUpdateDTO {

    @Schema(description = "楼宇编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "楼宇编码不能为空")
    private String buildingNo;

    @Schema(description = "详细地址")
    private String mapAddress;

    @Schema(description = "经度")
    private String mapLongitude;

    @Schema(description = "纬度")
    private String mapLatitude;

    @Schema(description = "地图楼宇编码")
    private String mapNo;
} 
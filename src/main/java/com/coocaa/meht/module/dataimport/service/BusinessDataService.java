package com.coocaa.meht.module.dataimport.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.exception.CommonException;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.CommonConstants;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.cos.ObjectUtils;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.enums.ApproveActionEnum;
import com.coocaa.meht.module.approve.enums.ApproveStatusEnum;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.dataimport.pojo.BasicInfoAllDTO;
import com.coocaa.meht.module.dataimport.pojo.BasicInfoAllVO;
import com.coocaa.meht.module.dataimport.pojo.BuildingLocationUpdateDTO;
import com.coocaa.meht.module.dataimport.pojo.BusinessOpportunityParam;
import com.coocaa.meht.module.dataimport.pojo.PointData;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyDTO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyPointDTO;
import com.coocaa.meht.module.dataimport.pojo.ProjectFileParam;
import com.coocaa.meht.module.dataimport.pojo.UpdateCustomerDataParam;
import com.coocaa.meht.module.dataimport.pojo.UpdatePointPlanStatusDTO;
import com.coocaa.meht.module.dataimport.pojo.UpdateRatingStatusDTO;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.dto.convert.BuildingMetaConvert;
import com.coocaa.meht.module.web.dto.convert.PointConvert;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;
import com.coocaa.meht.module.web.entity.BuildingCityRentEntity;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CityRentEntity;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.module.web.entity.PointContractSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.entity.PointPriceSnapshotEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.entity.WaitingHallBusinessEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.FollowTypeEnum;
import com.coocaa.meht.module.web.enums.PointPlanStatusEnum;
import com.coocaa.meht.module.web.enums.SceneTypeEnum;
import com.coocaa.meht.module.web.service.AgentPersonnelService;
import com.coocaa.meht.module.web.service.BuildingCityRentService;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CityRentService;
import com.coocaa.meht.module.web.service.CustomerFollowRecordService;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingPropertyCompanyService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.IPointContractSnapshotService;
import com.coocaa.meht.module.web.service.IPointPriceSnapshotService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.PriceApplyDeviceService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.service.ScreenApproveRecordService;
import com.coocaa.meht.module.web.service.WaitingHallBusinessService;
import com.coocaa.meht.module.web.service.impl.PriceApplyServiceImpl;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyPersonService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.module.web.vo.common.ConfigVO;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.rpc.dto.BusinessOpportunityStatusVO;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.StringUtils2;
import com.google.common.collect.Sets;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/1/14
 * @description 数据同步
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessDataService {

    @Autowired
    private PointService pointService;
    @Autowired
    private PointPlanService pointPlanService;

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Resource
    private KafkaProducerService kafkaProducerService;

    @Resource
    private SysUserService userService;

    @Resource
    private AgentPersonnelService agentPersonnelService;

    @Autowired(required = false)
    private RsaExample rsaExample;

    @Autowired
    private PointConvert pointConvert;

    @Autowired
    private CustomerFollowRecordService customerFollowRecordService;

    @Autowired
    private IBuildingPropertyCompanyService buildingPropertyCompanyService;

    @Autowired
    private IPropertyCompanyPersonService propertyCompanyPersonService;

    @Autowired
    private IBuildingMetaService buildingMetaService;

    @Resource
    private IBuildingMetaImgRelationService imgRelationService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SysFileService sysFileService;

    @Autowired
    private BuildingGeneService buildingGeneService;

    @Resource
    private BuildingDetailsService buildingDetailsService;

    @Resource
    private ScreenApproveRecordService screenApproveRecordService;

    @Autowired
    private PriceApplyService priceApplyService;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private IBuildingStatusChangeLogService buildingStatusChangeLogService;

    @Resource
    private IPointContractSnapshotService pointContractSnapshotService;

    @Resource
    private IPointPriceSnapshotService pointPriceSnapshotService;

    @Resource
    private PriceApplyDao priceApplyDao;

    @Resource
    private LargeScreenProperties largeScreenProperties;
    @Autowired
    private PriceApplyDeviceService priceApplyDeviceService;

    @Autowired
    private BuildingScreenService buildingScreenService;

    @Autowired
    private CityRentService cityRentService;

    @Autowired
    private BuildingCityRentService buildingCityRentService;

    @Autowired
    private BuildingParameterService buildingParameterService;

    @Resource
    private BuildingSnapshotService buildingSnapshotService;

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private FeignSspRpc feignSspRpc;

    @Autowired
    private WaitingHallBusinessService waitingHallBusinessService;

    @Autowired
    private CompleteRatingService completeRatingService;


    private static final Map<Integer, String> BUILDING_APPROVAL_STATUS_MAP = Map.of(
            1, ApproveActionEnum.PASS.getCode(),
            2, ApproveActionEnum.DISMISS.getCode(),
            3, ApproveActionEnum.REJECT.getCode()
    );

    private static final Map<Integer, String> PRICE_APPROVAL_STATUS_MAP = Map.of(
            2, ApproveActionEnum.PASS.getCode(),
            3, ApproveActionEnum.DISMISS.getCode(),
            4, ApproveActionEnum.REJECT.getCode()
    );

    public Boolean syncPointData(PointData pointData) {
        log.info("开始同步点位数据");
        boolean result = false;
        List<PointEntity> pointEntities = pointConvert.toPointEntitySys(pointData.getPointList());
        List<PointPlanEntity> pointPlanEntities = BeanUtil.copyToList(pointData.getPlanList(), PointPlanEntity.class);
        Set<String> collect = pointPlanService.list().stream().map(PointPlanEntity::getBuildingRatingNo).collect(Collectors.toSet());
        List<PointPlanEntity> pointPlanEntityList = pointPlanEntities.stream()
                .filter(pointPlanEntity -> !collect.contains(pointPlanEntity.getBuildingRatingNo())).collect(Collectors.toList());
        if (CollUtil.isEmpty(pointPlanEntityList)) {
            log.info("没有需要同步的数据");
            return true;
        }
        List<String> list = pointPlanEntityList.stream().map(PointPlanEntity::getBuildingRatingNo).toList();
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.list(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .in(BuildingRatingEntity::getBuildingNo, list));
        Map<String, BuildingRatingEntity> buildingRatingEntityMap = buildingRatingEntities.stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, Function.identity(), (o, n) -> n));
        Map<String, LoginUser> userMapping = getUserMapping(buildingRatingEntities);
        pointService.saveBatch(pointEntities);
        pointPlanService.saveBatch(pointPlanEntityList);
        //同步等候厅数据
        for (PointPlanEntity pointPlanEntity : pointPlanEntityList) {

            try {
                BuildingRatingEntity buildingRating = buildingRatingEntityMap.get(pointPlanEntity.getBuildingRatingNo());
                LoginUser loginUser = userMapping.get(buildingRating.getSubmitUser());
                SecurityUser.login(loginUser);
                pointService.cleaningWaitingHallBusiness(pointPlanEntity.getBuildingRatingNo(), pointPlanEntity.getBusinessCode());
            } catch (Exception e) {
                log.error("获取用户信息失败", e);
            } finally {
                SecurityUser.clearLogin();
            }

        }

        result = true;
        return result;
    }

    public boolean sysContractData(String buildingNo) {
        boolean result = false;
        long start = System.currentTimeMillis();
        log.info(StringUtils.isBlank(buildingNo) ? "开始同步合同数据" : "开始同步合同数据:" + buildingNo);
        List<BuildingRatingEntity> list = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getCrmPushStatus, 9)
                .eq(StringUtils.isNotBlank(buildingNo), BuildingRatingEntity::getBuildingNo, buildingNo)
                .list();
        if (ObjectUtil.isEmpty(list)) {
            log.info("没有需要同步的数据");
            return true;
        }
        List<String> buildingNos = list.stream().map(BuildingRatingEntity::getBuildingNo).toList();
        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.list(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .in(BusinessOpportunityEntity::getBuildingNo, buildingNos));

        Map<String, Long> businessOpportunityCountMap = businessOpportunityEntities.stream()
                .collect(Collectors.groupingBy(BusinessOpportunityEntity::getBuildingNo, Collectors.counting()));

        List<BusinessOpportunityEntity> businessOpportunityEntityList = new ArrayList<>();
        for (BuildingRatingEntity buildingRatingEntity : list) {
            buildingRatingEntity.setCrmPushStatus(0);
            BusinessOpportunityEntity businessOpportunity = new BusinessOpportunityEntity();
            businessOpportunity.setOwner(buildingRatingEntity.getSubmitUser());
            businessOpportunity.setBuildingNo(buildingRatingEntity.getBuildingNo());
            int num = businessOpportunityCountMap.getOrDefault(buildingRatingEntity.getBuildingNo(), 0L).intValue() + 1;
            businessOpportunity.setCode(buildingRatingEntity.getBuildingNo() + "-" + num);
            businessOpportunity.setStatus(BusinessChangeStatusEnum.CONTRACT_PHASE.getCode());
            businessOpportunity.setSubmitUser(buildingRatingEntity.getSubmitUser());
            businessOpportunity.setCreateBy(buildingRatingEntity.getCreateBy());
            businessOpportunity.setUpdateBy(buildingRatingEntity.getUpdateBy());
            businessOpportunity.setUpdateTime(buildingRatingEntity.getUpdateTime());
            businessOpportunity.setCreateTime(buildingRatingEntity.getCreateTime());
            businessOpportunity.setName(buildingRatingEntity.getBuildingName() + "-默认");
            businessOpportunityEntityList.add(businessOpportunity);

        }
        buildingRatingService.updateBatchById(list);
        businessOpportunityService.saveBatch(businessOpportunityEntityList);
        log.info("同步合同数据结束, 耗时:{}ms", System.currentTimeMillis() - start);
        return result;
    }


    public void updateBusinessStatus(List<BusinessOpportunityParam> params) {
        if (CollUtil.isEmpty(params)) {
            return;
        }
        for (BusinessOpportunityParam param : params) {
            BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
            businessStatusChangeVO.setBusinessCode(param.getCode());
            businessStatusChangeVO.setStatus(param.getStatus());
            kafkaProducerService.sendMessage(TopicConstants.BUSINESS_STATUS_CHANGE, com.alibaba.fastjson2.JSONObject.toJSONString(businessStatusChangeVO));
        }
    }

    public List<BasicInfoAllVO> getBasicInfoAll(BasicInfoAllDTO param) {
        List<BusinessOpportunityEntity> list = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getBuildingNo)
                .in(BusinessOpportunityEntity::getCode, param.getCodes())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<String> buildingNos = list.stream().map(BusinessOpportunityEntity::getBuildingNo).toList();

        List<BuildingMetaEntity> buildingMetaEntities = buildingMetaService.lambdaQuery()
                .select(BuildingMetaEntity::getBuildingRatingNo,
                        BuildingMetaEntity::getMapAddress,
                        BuildingMetaEntity::getMapProvince,
                        BuildingMetaEntity::getMapCity,
                        BuildingMetaEntity::getMapRegion,
                        BuildingMetaEntity::getBuildingType,
                        BuildingMetaEntity::getProjectLevel,
                        BuildingMetaEntity::getProjectLevelAi)
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNos)
                .list();

        List<BasicInfoAllVO> basicInfoAllVoList = BuildingMetaConvert.INSTANCE.toBasicInfoAllVoList(buildingMetaEntities);
        if (CollectionUtils.isNotEmpty(basicInfoAllVoList)) {
            basicInfoAllVoList.forEach(vo -> {
                vo.setMapAddress(StringUtils.isNotBlank(vo.getMapAddress()) ? rsaExample.decryptByPrivate(vo.getMapAddress()) : "");
            });
        }

        return basicInfoAllVoList;
    }

    public void updateBusinessType() {
        List<String> userCodes = userService.lambdaQuery().select(SysUserEntity::getEmpCode)
                .list().stream().map(SysUserEntity::getEmpCode).distinct().toList();
        if (CollUtil.isNotEmpty(userCodes)) {
            customerFollowRecordService.lambdaUpdate()
                    .set(CustomerFollowRecordEntity::getBusinessType, 0)
                    .in(BaseEntity::getCreateBy, userCodes)
                    .update();
        }


        List<String> agcode = agentPersonnelService.lambdaQuery()
                .select(AgentPersonnelEntity::getEmpCode)
                .list().stream().map(AgentPersonnelEntity::getEmpCode).distinct().toList();
        if (CollUtil.isNotEmpty(agcode)) {
            customerFollowRecordService.lambdaUpdate()
                    .set(CustomerFollowRecordEntity::getBusinessType, 1)
                    .in(BaseEntity::getCreateBy, agcode)
                    .update();
        }

    }

    public void updateRolePhone() {
        List<CustomerFollowRecordEntity> customerFollowRecordEntities = customerFollowRecordService.list();
        Set<String> collect = customerFollowRecordEntities.stream().map(CustomerFollowRecordEntity::getBusinessCode).collect(Collectors.toSet());

        List<BuildingPropertyCompanyEntity> buildingPropertyCompanyEntities = buildingPropertyCompanyService.lambdaQuery()
                .in(BuildingPropertyCompanyEntity::getProjectCode, collect)
                .list();
        Map<String, List<BuildingPropertyCompanyEntity>> buildingPropertyCompanyEntityMap = buildingPropertyCompanyEntities.stream().collect(Collectors.groupingBy(BuildingPropertyCompanyEntity::getProjectCode));

        List<Integer> list = buildingPropertyCompanyEntities.stream().map(BuildingPropertyCompanyEntity::getPropertyId).toList();
        Map<Integer, List<PropertyCompanyPersonEntity>> personMap = propertyCompanyPersonService.lambdaQuery()
                .in(PropertyCompanyPersonEntity::getCompanyId, list)
                .list().stream().collect(Collectors.groupingBy(PropertyCompanyPersonEntity::getCompanyId));

        for (CustomerFollowRecordEntity entity : customerFollowRecordEntities) {
            List<BuildingPropertyCompanyEntity> buildingPropertyCompanyEntityList = buildingPropertyCompanyEntityMap.get(entity.getBusinessCode());
            if (CollUtil.isEmpty(buildingPropertyCompanyEntityList)) {
                continue;
            }
            for (BuildingPropertyCompanyEntity buildingPropertyCompanyEntity : buildingPropertyCompanyEntityList) {
                List<PropertyCompanyPersonEntity> propertyCompanyPersonEntities = personMap.get(buildingPropertyCompanyEntity.getPropertyId());
                if (CollUtil.isEmpty(propertyCompanyPersonEntities)) {
                    continue;
                }
                for (PropertyCompanyPersonEntity propertyCompanyPersonEntity : propertyCompanyPersonEntities) {
                    if (entity.getVisitObjects().equals(propertyCompanyPersonEntity.getName())) {
                        entity.setRole(propertyCompanyPersonEntity.getRole());
                        entity.setPhone(propertyCompanyPersonEntity.getPhone());
                    }
                }

            }
        }
        customerFollowRecordService.updateBatchById(customerFollowRecordEntities);

    }

    public void updateValid() {
        List<CustomerFollowRecordEntity> list = customerFollowRecordService
                .lambdaQuery()
                .select(CustomerFollowRecordEntity::getId)
                .eq(CustomerFollowRecordEntity::getVisitType, FollowTypeEnum.PHONE.name())
                .list();
        Set<Integer> collect = list.stream().map(CustomerFollowRecordEntity::getId).collect(Collectors.toSet());

        customerFollowRecordService.lambdaUpdate()
                .set(CustomerFollowRecordEntity::getValid, 0)
                .in(CustomerFollowRecordEntity::getId, collect);
    }


    /**
     * 获取用户信息
     */
    private Map<String, LoginUser> getUserMapping(List<BuildingRatingEntity> buildingRatingList) {
        if (CollUtil.isEmpty(buildingRatingList)) return Collections.emptyMap();

        // 查询提交人信息
        Set<String> wnos = buildingRatingList.stream()
                .map(BuildingRatingEntity::getSubmitUser)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        return getUserMapping(wnos);
    }


    /**
     * 获取用户信息
     */
    private Map<String, LoginUser> getUserMapping(Collection<String> wnos) {
        if (CollUtil.isEmpty(wnos)) return Collections.emptyMap();

        Map<String, LoginUser> userMapping = Maps.newHashMapWithExpectedSize(wnos.size());

        // 查用户表
        userMapping.putAll(userService.lambdaQuery()
                .in(SysUserEntity::getEmpCode, wnos)
                .list().stream()
                .map(LoginUser::build)
                .peek(user -> {
                    user.setPhone(rsaExample.decryptByPrivate(user.getPhone()));
                    user.setEmail(rsaExample.decryptByPrivate(user.getEmail()));
                })
                .collect(Collectors.toMap(LoginUser::getUserCode, Function.identity(), (o, n) -> n)));

        // 查代理商表
        userMapping.putAll(agentPersonnelService.lambdaQuery()
                .in(AgentPersonnelEntity::getEmpCode, wnos)
                .list().stream()
                .map(user -> {
                    LoginUser loginUser = new LoginUser();
                    loginUser.setUserType(1);
                    loginUser.setId(user.getId());
                    loginUser.setUserCode(user.getEmpCode());
                    loginUser.setUserName(user.getEmpName());
                    loginUser.setPhone(rsaExample.decryptByPublic(user.getEmpMobile()));
                    return loginUser;
                })
                .collect(Collectors.toMap(LoginUser::getUserCode, Function.identity(), (o, n) -> n)));

        return userMapping;
    }


    public void addBuildingMeta(List<String> buildingNos) {
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.value, BuildingRatingEntity.Status.REJECTED.value)
                .eq(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMING.value)
                .in(BuildingRatingEntity::getBuildingNo, buildingNos)
                .list();

        for (BuildingRatingEntity buildingRating : buildingRatingEntities) {

            try {
                BuildingMetaEntity buildingMetaEntity = new BuildingMetaEntity();
                buildingMetaEntity.setBuildingMetaNo(getBuildingMetaNo());
                if (StringUtils.isBlank(buildingRating.getBuildingName())) {
                    buildingMetaEntity.setBuildingName(buildingRating.getBuildingName());
                    buildingMetaEntity.setBuildingNameAi(buildingRating.getBuildingName());
                }

                if (Objects.nonNull(buildingRating.getBuildingType())) {
                    buildingMetaEntity.setBuildingType(buildingRating.getBuildingType());
                }

                if (StringUtils.isNotBlank(buildingRating.getMapAddress())) {
                    buildingMetaEntity.setMapAddress(buildingRating.getMapAddress());
                    buildingMetaEntity.setMapProvince(buildingRating.getMapProvince());
                    buildingMetaEntity.setMapCity(buildingRating.getMapCity());
                    buildingMetaEntity.setMapRegion(buildingRating.getMapRegion());
                    buildingMetaEntity.setMapLatitude(buildingRating.getMapLatitude());
                    buildingMetaEntity.setMapLongitude(buildingRating.getMapLongitude());
                    buildingMetaEntity.setMapAdCode(buildingRating.getMapAdCode());
                }

                if (StringUtils.isNotBlank(buildingRating.getMapNo())) {
                    buildingMetaEntity.setMapNo(buildingRating.getMapNo());
                }

                if (StringUtils.isBlank(buildingRating.getBuildingNo())) {
                    buildingMetaEntity.setBuildingRatingNo(buildingRating.getBuildingNo());
                }

                if (Objects.nonNull(buildingRating.getBuildingScore())) {
                    buildingMetaEntity.setBuildingScore(buildingRating.getBuildingScore());
                    buildingMetaEntity.setBuildingAiScore(buildingRating.getBuildingScore());
                }

                if (StringUtils.isNotBlank(buildingRating.getProjectLevel())) {
                    buildingMetaEntity.setProjectLevel(buildingRating.getProjectLevel());
                    buildingMetaEntity.setProjectLevelAi(buildingRating.getProjectLevel());
                }

                if (StringUtils.isNotBlank(buildingRating.getCreateBy())) {
                    buildingMetaEntity.setCreateBy(buildingRating.getCreateBy());
                }

                if (StringUtils.isNotBlank(buildingRating.getSubmitUser())) {
                    buildingMetaEntity.setManager(buildingRating.getSubmitUser());
                }


                buildingMetaService.save(buildingMetaEntity);


                //存储图片
                List<BuildingMetaImgRelationEntity> imgList = new ArrayList<>();
                //楼盘大堂
                String buildingLobbyPic = buildingRating.getBuildingLobbyPic();
                if (StringUtils.isNotBlank(buildingLobbyPic)) {
                    imgList.addAll(saveMetaImg(buildingLobbyPic, 2, buildingMetaEntity.getBuildingMetaNo(), buildingRating.getCreateBy()));
                }

                //侯梯厅
                String buildingHallPic = buildingRating.getBuildingHallPic();
                if (StringUtils.isNotBlank(buildingHallPic)) {
                    imgList.addAll(saveMetaImg(buildingHallPic, 3, buildingMetaEntity.getBuildingMetaNo(), buildingMetaEntity.getCreateBy()));
                }

                //外墙材料
                String buildingExteriorPic = buildingRating.getBuildingExteriorPic();
                if (StringUtils.isNotBlank(buildingExteriorPic)) {
                    imgList.addAll(saveMetaImg(buildingExteriorPic, 1, buildingMetaEntity.getBuildingMetaNo(), buildingMetaEntity.getCreateBy()));
                }

                if (ObjectUtil.isNotEmpty(imgList)) {
                    log.info("楼宇编码{}，保存图片数量{}", buildingRating.getBuildingNo(), imgList.size());
                    imgRelationService.remove(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                            .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, buildingMetaEntity.getBuildingMetaNo()));
                    imgRelationService.saveBatch(imgList);
                }

            } catch (Exception e) {
                log.error("修改楼宇编码{}，错误信息:{}", buildingRating.getBuildingNo(), e.getMessage(), e);
            }


        }


    }


    public void addMetaBuildingBusiness() {
        Set<String> mapNos = Sets.newHashSet("3d66bf6de533fe19081cc71e", "6d4559797e0ad274c9c3db2e");
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getMapNo, mapNos)
                .list();

        Map<String, BuildingRatingEntity> buildingRatingEntityMap = buildingRatingEntities.stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, e -> e, (o, n) -> n));

        Map<String, BusinessOpportunityEntity> businessOpportunityEntityMap = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getBuildingNo, buildingRatingEntityMap.keySet())
                .list().stream().collect(Collectors.toMap(BusinessOpportunityEntity::getBuildingNo, e -> e, (o, n) -> n));


        for (BuildingRatingEntity buildingRatingEntity : buildingRatingEntities) {

            BusinessOpportunityEntity businessOpportunityNew = businessOpportunityEntityMap.get(buildingRatingEntity.getBuildingNo());
            if (Objects.isNull(businessOpportunityNew)) {
                log.info("添加商机，buildingNo:{}", buildingRatingEntity.getBuildingNo());
                BusinessOpportunityEntity businessOpportunity = new BusinessOpportunityEntity();
                businessOpportunity.setBuildingNo(buildingRatingEntity.getBuildingNo());
                businessOpportunity.setName(buildingRatingEntity.getBuildingName() + "-默认");
                businessOpportunity.setCustomerId("");
                businessOpportunity.setOwner(buildingRatingEntity.getCreateBy());
                businessOpportunity.setCode(buildingRatingEntity.getBuildingNo() + "-1");
                businessOpportunity.setStatus(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode());
                businessOpportunity.setSubmitUser(buildingRatingEntity.getCreateBy());
                businessOpportunity.setOwner(buildingRatingEntity.getCreateBy());
                businessOpportunity.setCreateBy(buildingRatingEntity.getCreateBy());
                businessOpportunity.setUpdateBy(buildingRatingEntity.getCreateBy());
                businessOpportunityService.save(businessOpportunity);
            }

        }


    }

    public String getBuildingMetaNo() {
        String localDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = "meta:id:" + localDate;
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return "BC" + localDate + StringUtils2.file5Code(increment.intValue());
    }

    public List<BuildingMetaImgRelationEntity> saveMetaImg(String ids, Integer imgType, String buildingMetaNo, String userCode) {
        List<BuildingMetaImgRelationEntity> imgRelationEntityList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            List<Long> fileIds = list.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            List<SysFileEntity> bySysFileIdList = sysFileService.getBySysFileIdList(fileIds);
            if (ObjectUtil.isNotEmpty(bySysFileIdList)) {
                bySysFileIdList.stream().forEach(file -> {
                    BuildingMetaImgRelationEntity entity = new BuildingMetaImgRelationEntity();
                    entity.setImgUrl(file.getUrl());
                    entity.setImgType(imgType);
                    entity.setCreateBy(StringUtils.isNotBlank(userCode) ? userCode : SecurityUser.getUserCode());
                    entity.setBuildingMetaNo(buildingMetaNo);
                    imgRelationEntityList.add(entity);
                });
            }
        }
        return imgRelationEntityList;
    }


    public void updateCustomerData(List<UpdateCustomerDataParam> params) {
        for (UpdateCustomerDataParam param : params) {
            try {
                buildingRatingService.lambdaUpdate()
                        .set(StringUtils.isNotBlank(param.getSubmitUser()), BuildingRatingEntity::getSubmitUser, param.getSubmitUser())
                        .set(Objects.nonNull(param.getHighSeaFlag()), BuildingRatingEntity::getHighSeaFlag, param.getHighSeaFlag())
                        .set(Objects.nonNull(param.getEnterSeaCalculateTime()), BuildingRatingEntity::getEnterSeaCalculateTime, param.getEnterSeaCalculateTime())
                        .eq(BuildingRatingEntity::getId, param.getId())
                        .update();
            } catch (Exception e) {
                log.error("失败id：{}，更新客户数据失败:{}", param.getId(), e.getMessage());
            }
        }

    }

    public void refreshHighSeaTime(String enterSeaCalculateTime) {
        List<BuildingRatingEntity> list = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId, BuildingRatingEntity::getSubmitTime)
                .list();
        if (StringUtils.isNotBlank(enterSeaCalculateTime)) {
            for (BuildingRatingEntity buildingRating : list) {
                try {
                    buildingRatingService.lambdaUpdate()
                            .set(BuildingRatingEntity::getEnterSeaCalculateTime, enterSeaCalculateTime)
                            .eq(BuildingRatingEntity::getId, buildingRating.getId())
                            .update();
                } catch (Exception e) {
                    log.error("失败id：{}，更新客户数据失败:{}", buildingRating.getId(), e.getMessage());
                }
            }

        } else {
            for (BuildingRatingEntity entity : list) {
                try {
                    buildingRatingService.lambdaUpdate()
                            .set(BuildingRatingEntity::getEnterSeaCalculateTime, entity.getSubmitTime())
                            .eq(BuildingRatingEntity::getId, entity.getId())
                            .update();
                } catch (Exception e) {
                    log.error("失败id：{}，更新客户数据失败:{}", entity.getId(), e.getMessage());
                }
            }
        }

    }

    public Map<String, List<Long>> refreshRatingFlag(List<Long> buildingIds) {
        // 获取所有已审核的数据
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingType)
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                // 过滤掉飞书合同洗入的数据，这部分数据虽然状态是已审核，但需要完善评级来补充信息
                .ne(BuildingRatingEntity::getBuildingDesc, CommonConstants.BUILDING_RATING_DES_FLAG)
                .in(CollUtil.isNotEmpty(buildingIds), BuildingRatingEntity::getId, buildingIds)
                .list();

        Map<String, List<Long>> result = new HashMap<>();

        for (BuildingRatingEntity entity : buildingRatingEntities) {
            try {
                boolean isLargeScreen = false;
                LambdaUpdateWrapper<BuildingRatingEntity> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(BuildingRatingEntity::getId, entity.getId())
                        .set(BuildingRatingEntity::getSmallScreenRatingFlag, BooleFlagEnum.YES.getCode());
                if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() == entity.getBuildingType()
                        && buildingGeneService.isScreen(entity.getBuildingNo())) {
                    isLargeScreen = true;
                    wrapper.set(BuildingRatingEntity::getLargeScreenRatingFlag, BooleFlagEnum.YES.getCode());
                }
                buildingRatingService.update(wrapper);
                log.info("评级刷新成功，id：{}，{}屏评级已完成", entity.getId(), isLargeScreen ? "大" : "小");
                if (isLargeScreen) {
                    List<Long> larges = result.computeIfAbsent("large", k -> new ArrayList<>());
                    larges.add(entity.getId());
                } else {
                    List<Long> smalls = result.computeIfAbsent("small", k -> new ArrayList<>());
                    smalls.add(entity.getId());
                }
            } catch (Exception e) {
                log.error("评级刷新异常，id：{}", entity.getId(), e);
                List<Long> errors = result.computeIfAbsent("error", k -> new ArrayList<>());
                errors.add(entity.getId());
            }
        }

        return result;
    }

    public Boolean refreshGene() {
        log.info("开始刷新楼宇基因数据");
        long startTime = System.currentTimeMillis();

        try {
            // 获取所有未删除的楼宇详情
            List<BuildingDetailsEntity> buildingDetailsEntities = buildingDetailsService.lambdaQuery()
                    .eq(BuildingDetailsEntity::getDeleted, 0)
                    .list();

            if (CollectionUtils.isEmpty(buildingDetailsEntities)) {
                log.info("没有找到有效的楼宇详情数据");
                return true;
            }

            log.info("找到 {} 条楼宇详情记录", buildingDetailsEntities.size());

            // 提取楼宇编号
            List<String> buildingNoList = buildingDetailsEntities.stream()
                    .map(BuildingDetailsEntity::getBuildingNo)
                    .collect(Collectors.toList());

            // 查询已有基因数据
            Map<String, BuildingGeneEntity> geneEntityMap = buildingGeneService.lambdaQuery()
                    .in(BuildingGeneEntity::getBuildingRatingNo, buildingNoList)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(
                            BuildingGeneEntity::getBuildingRatingNo,
                            Function.identity(),
                            (o, n) -> n));

            List<BuildingGeneEntity> addList = new ArrayList<>();
            List<BuildingGeneEntity> updateList = new ArrayList<>();
            int skippedCount = 0;

            // 处理每个楼宇详情
            for (BuildingDetailsEntity detail : buildingDetailsEntities) {
                try {
                    String buildingNo = detail.getBuildingNo();
                    BuildingGeneEntity geneEntity = geneEntityMap.get(buildingNo);

                    if (geneEntity == null) {
                        // 创建新的基因实体
                        geneEntity = new BuildingGeneEntity();
                        geneEntity.setBuildingRatingNo(buildingNo);
                        geneEntity.setMaxFloorCount(Integer.parseInt(detail.getBuildingNumberInput()));
                        geneEntity.setMonthlyAvgPrice(new BigDecimal(detail.getBuildingPriceInput()));
                        geneEntity.setBuildingAge(Integer.parseInt(detail.getBuildingAgeInput()));
                        addList.add(geneEntity);
                    } else {
                        // 判断是否需要更新现有基因实体
                        boolean needsUpdate = false;

                        if (geneEntity.getMaxFloorCount() == null && StrUtil.isBlank(detail.getBuildingNumberInput())) {
                            geneEntity.setMaxFloorCount(Integer.parseInt(detail.getBuildingNumberInput()));
                            needsUpdate = true;
                        }

                        if (geneEntity.getMonthlyAvgPrice() == null && StrUtil.isBlank(detail.getBuildingPriceInput())) {
                            geneEntity.setMonthlyAvgPrice(new BigDecimal(detail.getBuildingPriceInput()));
                            needsUpdate = true;
                        }

                        if (geneEntity.getBuildingAge() == null && StrUtil.isBlank(detail.getBuildingAgeInput())) {
                            geneEntity.setBuildingAge(Integer.parseInt(detail.getBuildingAgeInput()));
                            needsUpdate = true;
                        }

                        if (needsUpdate) {
                            updateList.add(geneEntity);
                        } else {
                            skippedCount++;
                        }
                    }
                } catch (NumberFormatException e) {
                    log.error("楼宇基因数据处理失败，楼宇编号：{}，错误原因：数值转换异常", detail.getBuildingNo(), e);
                } catch (Exception e) {
                    log.error("楼宇基因数据处理失败，楼宇编号：{}，错误原因：{}", detail.getBuildingNo(), e.getMessage(), e);
                }
            }

            // 批量保存新增的基因实体
            if (CollectionUtils.isNotEmpty(addList)) {
                log.info("新增 {} 条楼宇基因数据", addList.size());
                buildingGeneService.saveBatch(addList);
            }

            // 批量更新现有的基因实体
            if (CollectionUtils.isNotEmpty(updateList)) {
                log.info("更新 {} 条楼宇基因数据", updateList.size());
                buildingGeneService.updateBatchById(updateList);
            }

            log.info("楼宇基因数据刷新完成，新增：{}，更新：{}，跳过：{}，总耗时：{}ms",
                    addList.size(), updateList.size(), skippedCount, System.currentTimeMillis() - startTime);
            return true;
        } catch (Exception e) {
            log.error("楼宇基因数据刷新失败，错误原因：{}", e.getMessage(), e);
            return false;
        }
    }

    public void updateApproveRecord(List<String> buildingNoList) {
        log.info("开始刷新记录数据");
        List<ScreenApproveRecordEntity> list = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                .list();
        Map<String, List<ScreenApproveRecordEntity>> collect = list.stream().collect(Collectors.groupingBy(ScreenApproveRecordEntity::getNaturalKey));

        List<BuildingRatingEntity> buildingRatingEntityList = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.value
                        , BuildingRatingEntity.Status.REJECTED.value,
                        BuildingRatingEntity.Status.FAILED_AUDIT.value,
                        BuildingRatingEntity.Status.AUDITED.value)
                .in(CollUtil.isNotEmpty(buildingNoList), BuildingRatingEntity::getBuildingNo, buildingNoList)
                .list();

        List<ScreenApproveRecordEntity> screenApproveRecordEntities = new ArrayList<>();

        for (BuildingRatingEntity buildingRating : buildingRatingEntityList) {
            try {
                List<ScreenApproveRecordEntity> screenApproveRecordEntityList = collect.get(buildingRating.getBuildingNo());
                if (CollUtil.isEmpty(screenApproveRecordEntityList)) {
                    ScreenApproveRecordEntity submit = new ScreenApproveRecordEntity();
                    submit.setNaturalKey(buildingRating.getBuildingNo());
                    submit.setApproveUser(buildingRating.getSubmitUser());
                    submit.setApproveLevel(0);
                    submit.setApproveTime(buildingRating.getSubmitTime());
                    submit.setSceneType(SceneTypeEnum.BUILDING.getType());
                    submit.setOperateType(ScreenApproveRecordEntity.OperateType.NEW_DATA.getCode());
                    submit.setCreateBy(buildingRating.getSubmitUser());
                    submit.setUpdateBy(buildingRating.getSubmitUser());
                    submit.setCreateTime(buildingRating.getSubmitTime());
                    submit.setUpdateTime(buildingRating.getSubmitTime());
                    screenApproveRecordEntities.add(submit);

                    ScreenApproveRecordEntity screenApproveRecordEntity = new ScreenApproveRecordEntity();
                    screenApproveRecordEntity.setSceneType(SceneTypeEnum.BUILDING.getType());
                    screenApproveRecordEntity.setNaturalKey(buildingRating.getBuildingNo());
                    screenApproveRecordEntity.setOperateType(ScreenApproveRecordEntity.OperateType.NEW_DATA.getCode());
                    screenApproveRecordEntity.setStatus(buildingRating.getStatus());
                    screenApproveRecordEntity.setApproveLevel(1);
                    Integer status = buildingRating.getStatus();
                    screenApproveRecordEntity.setStatus(status);
                    screenApproveRecordEntity.setApproveUser(buildingRating.getApproveUser());
                    screenApproveRecordEntity.setCreateBy(buildingRating.getSubmitUser());
                    screenApproveRecordEntity.setUpdateBy(buildingRating.getSubmitUser());
                    screenApproveRecordEntity.setCreateTime(buildingRating.getSubmitTime());
                    screenApproveRecordEntity.setUpdateTime(buildingRating.getSubmitTime());
                    if (status.equals(BuildingRatingEntity.Status.WAIT_AUDIT.value)
                            || status.equals(BuildingRatingEntity.Status.FAILED_AUDIT.value)
                            || status.equals(BuildingRatingEntity.Status.AUDITED.value)) {
                        screenApproveRecordEntity.setApproveTime(buildingRating.getApproveTime());
                        screenApproveRecordEntity.setRemark(buildingRating.getApproveDesc());
                    } else if (status.equals(BuildingRatingEntity.Status.REJECTED.value)) {
                        screenApproveRecordEntity.setApproveTime(buildingRating.getRejectTime());
                        screenApproveRecordEntity.setRemark(buildingRating.getRejectDesc());
                    }


                    screenApproveRecordEntities.add(screenApproveRecordEntity);
                }
            } catch (Exception e) {
                log.error("刷新记录数据失败buildingNo：{},错误信息{}", buildingRating.getBuildingNo(), e.getMessage());
            }

        }


        int totalSize = screenApproveRecordEntities.size();
        int batchSize = (int) Math.ceil(totalSize / 4.0);

        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<ScreenApproveRecordEntity> subList = screenApproveRecordEntities.subList(i, end);
            screenApproveRecordService.saveBatch(subList);
        }

        log.info("记录数据刷新完成");

    }

    public void updateApproveRecordPrice() {

        Set<String> codes = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getApproveType, ApprovalTypeEnum.PRICE_APPROVAL.getCode())
                .list().stream().map(ScreenApproveRecordEntity::getNaturalKey).collect(Collectors.toSet());

        List<PriceApplyEntity> list = priceApplyService.lambdaQuery()
                .select(PriceApplyEntity::getApproveTime, PriceApplyEntity::getApplyCode
                        , BaseEntity::getCreateBy, PriceApplyEntity::getStatus, PriceApplyEntity::getApproveBy, BaseEntity::getCreateTime)
                .in(PriceApplyEntity::getStatus, PriceApplyEntity.Status.PASSED.getCode(), PriceApplyEntity.Status.REJECTED.getCode())
                .list();

        List<ScreenApproveRecordEntity> screenApproveRecordEntities = new ArrayList<>();

        for (PriceApplyEntity priceApply : list) {
            try {
                if (!codes.contains(priceApply.getApplyCode())) {
                    ScreenApproveRecordEntity submit = new ScreenApproveRecordEntity();
                    submit.setNaturalKey(priceApply.getApplyCode());
                    submit.setApprovalFlag(0);
                    submit.setSubmitTime(priceApply.getCreateTime());
                    submit.setCreateTime(priceApply.getCreateTime());
                    submit.setCreateBy(priceApply.getCreateBy());
                    submit.setUpdateBy(priceApply.getCreateBy());
                    submit.setUpdateTime(priceApply.getCreateTime());
                    submit.setApproveType(ApprovalTypeEnum.PRICE_APPROVAL.getCode());
                    screenApproveRecordEntities.add(submit);

                    ScreenApproveRecordEntity screenApproveRecordEntity = new ScreenApproveRecordEntity();
                    screenApproveRecordEntity.setNaturalKey(priceApply.getApplyCode());
                    screenApproveRecordEntity.setApproveLevel(1);
                    screenApproveRecordEntity.setApprovalFlag(1);
                    screenApproveRecordEntity.setApproveUser(priceApply.getApproveBy());
                    screenApproveRecordEntity.setApproveTime(priceApply.getApproveTime());
                    screenApproveRecordEntity.setCreateTime(priceApply.getCreateTime());
                    screenApproveRecordEntity.setSubmitTime(priceApply.getApproveTime());
                    screenApproveRecordEntity.setApproveType(ApprovalTypeEnum.PRICE_APPROVAL.getCode());
                    screenApproveRecordEntity.setApprovalResult(priceApply.getStatus().equals(2) ? ApproveActionEnum.PASS.getCode() : ApproveActionEnum.REJECT.getCode());
                    screenApproveRecordEntity.setNodeStatus(ApproveStatusEnum.COMPLETED.getCode());
                    screenApproveRecordEntities.add(screenApproveRecordEntity);
                }

            } catch (Exception e) {
                log.error("刷新记录数据价格失败：{},错误信息{}", e);
            }

        }


        int totalSize = screenApproveRecordEntities.size();
        int batchSize = (int) Math.ceil(totalSize / 4.0);

        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<ScreenApproveRecordEntity> subList = screenApproveRecordEntities.subList(i, end);
            screenApproveRecordService.saveBatch(subList);
        }

        log.info("记录数据刷新完成");

    }

    public void deleteApproveRecord(List<Integer> ids) {
        log.info("删除记录");
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        screenApproveRecordService.removeByIds(ids);
    }

    /**
     * 更新楼宇评级状态和审核人信息
     *
     * @param updateRatingStatusDTO 包含楼宇编号列表、状态和审核人的DTO对象
     * @return 更新是否成功
     */
    public Boolean updateRatingStatus(UpdateRatingStatusDTO updateRatingStatusDTO) {
        log.info("开始修改楼宇评级状态和审核人信息: {}", JSON.toJSONString(updateRatingStatusDTO));

        // 参数校验
        if (updateRatingStatusDTO == null || CollUtil.isEmpty(updateRatingStatusDTO.getBuildingNoList())) {
            log.warn("更新评级状态失败: 参数为空或楼宇编号列表为空");
            return false;
        }

        // 查询需要更新的楼宇评级实体列表
        List<BuildingRatingEntity> ratingEntityList = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getBuildingNo, updateRatingStatusDTO.getBuildingNoList())
                .list();

        // 检查查询结果是否为空
        if (CollUtil.isEmpty(ratingEntityList)) {
            log.warn("更新评级状态失败: 未找到指定楼宇编号的评级记录, 楼宇编号列表: {}", updateRatingStatusDTO.getBuildingNoList());
            return false;
        }

        log.info("找到{}条楼宇评级记录需要更新", ratingEntityList.size());

        // 遍历更新每个楼宇评级实体
        for (BuildingRatingEntity buildingRating : ratingEntityList) {
            // 记录更新前的状态，用于日志记录
            Integer oldStatus = buildingRating.getStatus();
            String oldApproveUser = buildingRating.getApproveUser();

            // 更新楼宇评级状态和审核人
            buildingRating.setStatus(updateRatingStatusDTO.getStatus());
            buildingRating.setApproveUser(updateRatingStatusDTO.getApproveUser());

            log.debug("楼宇[{}]评级状态从[{}]更新为[{}], 审核人从[{}]更新为[{}]",
                    buildingRating.getBuildingNo(),
                    oldStatus,
                    updateRatingStatusDTO.getStatus(),
                    oldApproveUser,
                    updateRatingStatusDTO.getApproveUser());

            // 同步更新审批记录中的审核人信息（仅更新审核人为空的记录）
            boolean updated = screenApproveRecordService.lambdaUpdate()
                    .set(ScreenApproveRecordEntity::getApproveUser, updateRatingStatusDTO.getApproveUser())
                    .eq(ScreenApproveRecordEntity::getNaturalKey, buildingRating.getBuildingNo())
                    .eq(ScreenApproveRecordEntity::getApproveUser, "")
                    .update();

            if (updated) {
                log.debug("已更新楼宇[{}]对应的审批记录审核人信息", buildingRating.getBuildingNo());
            }
        }

        // 批量更新楼宇评级实体
        boolean result = buildingRatingService.updateBatchById(ratingEntityList);
        log.info("楼宇评级状态和审核人信息更新{}，共更新{}条记录", result ? "成功" : "失败", ratingEntityList.size());

        return result;
    }

    /**
     * v1.7.7上线时使用,待审核的数据，统一处理成审核不通过
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateNotApproveRating() {
        // 1. 查询所有待审核和已驳回的楼宇评级
        List<BuildingRatingEntity> toFailList = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .in(BuildingRatingEntity::getStatus, Arrays.asList(
                        BuildingRatingEntity.Status.WAIT_AUDIT.value,
                        BuildingRatingEntity.Status.REJECTED.value))
                .list();

        if (CollUtil.isEmpty(toFailList)) {
            log.info("无待审核或已驳回数据");
            return false;
        }

        // 2. 批量更新状态为"审核未通过"
        toFailList.forEach(item -> {
            item.setStatus(BuildingRatingEntity.Status.FAILED_AUDIT.value);
            item.setBuildingStatus(BuildingRatingEntity.BuildingStatus.UN_CONFIRM.value);
        });
        buildingRatingService.updateBatchById(toFailList);

        // 3. 获取所有相关的楼宇编号
        List<String> buildingNoList = toFailList.stream()
                .map(BuildingRatingEntity::getBuildingNo)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .toList();

        // 4. 查询所有相关的审批记录（非0级）
        List<ScreenApproveRecordEntity> allScreenRecords = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                .eq(ScreenApproveRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(ScreenApproveRecordEntity::getNaturalKey, buildingNoList)
                .ne(ScreenApproveRecordEntity::getApproveLevel, 0)
                .list();

        if (CollUtil.isNotEmpty(allScreenRecords)) {
            // 5. 处理审批记录
            Map<String, List<ScreenApproveRecordEntity>> recordMap = allScreenRecords.stream()
                    .collect(Collectors.groupingBy(ScreenApproveRecordEntity::getNaturalKey));

            List<ScreenApproveRecordEntity> toUpdate = new ArrayList<>();
            for (String buildingNo : buildingNoList) {
                List<ScreenApproveRecordEntity> records = recordMap.get(buildingNo);
                if (CollUtil.isEmpty(records)) continue;

                // 找到最新的待审核记录，否则取最新一条
                ScreenApproveRecordEntity target = records.stream()
                        .filter(r -> ObjectUtil.equal(r.getStatus(), BuildingRatingEntity.Status.WAIT_AUDIT.value))
                        .findFirst()
                        .orElse(records.stream().max(Comparator.comparing(ScreenApproveRecordEntity::getCreateTime)).orElse(null));

                if (target != null) {
                    target.setStatus(BuildingRatingEntity.Status.FAILED_AUDIT.value);
                    target.setApproveTime(LocalDateTime.now());
                    target.setRemark("系统自动拒绝");
                    toUpdate.add(target);
                }
            }
            if (CollUtil.isNotEmpty(toUpdate)) {
                screenApproveRecordService.updateBatchById(toUpdate);
            }
        }

        // 更新商机为关闭
        List<BusinessOpportunityEntity> opportunityEntityList = businessOpportunityService.lambdaQuery().in(BusinessOpportunityEntity::getBuildingNo, buildingNoList).list();
        if (CollUtil.isNotEmpty(opportunityEntityList)) {
            opportunityEntityList.forEach(item -> {
                item.setStatus(BusinessChangeStatusEnum.CLOSE.getCode());
            });
            businessOpportunityService.updateBatchById(opportunityEntityList);
        }

        // 更新楼宇库为待认证
        buildingMetaService.lambdaUpdate()
                .set(BuildingMetaEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue())
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNoList)
                .update();


        log.info("待审核和已驳回数据已全部置为审核未通过，共处理{}条楼宇", toFailList.size());
        return true;
    }

    public void cleanRepeatPoint(List<String> businessCodes) {
        if (CollUtil.isEmpty(businessCodes)) {
            return;
        }

        List<PointEntity> points = pointService.lambdaQuery()
                .select(PointEntity::getId, PointEntity::getCode)
                .in(PointEntity::getBusinessCode, businessCodes)
                .list();

        Map<String, List<PointEntity>> pointMap = points.stream().collect(Collectors.groupingBy(PointEntity::getCode, Collectors.toList()));
        Set<String> codes = pointMap.entrySet().stream().filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toSet());


        List<PointEntity> repeatPoints = points.stream().filter(point -> codes.contains(point.getCode())).toList();
        if (CollUtil.isEmpty(repeatPoints)) {
            return;
        }

        List<Integer> repeatIds = repeatPoints.stream().map(PointEntity::getId).toList();
        Map<String, List<PointEntity>> repeatPointsMap = repeatPoints.stream().collect(Collectors.groupingBy(PointEntity::getCode));

        List<Integer> reserveIds = new ArrayList<>();
        List<PointContractSnapshotEntity> pointContractSnapshotEntities = pointContractSnapshotService.lambdaQuery()
                .select(PointContractSnapshotEntity::getPointId)
                .in(PointContractSnapshotEntity::getPointId, repeatIds)
                .list();
        if (CollUtil.isNotEmpty(pointContractSnapshotEntities)) {
            reserveIds.addAll(pointContractSnapshotEntities.stream()
                    .map(PointContractSnapshotEntity::getPointId)
                    .distinct()
                    .toList());
        }


        List<PointPriceSnapshotEntity> pointPriceSnapshotEntities = pointPriceSnapshotService.lambdaQuery()
                .select(PointPriceSnapshotEntity::getPointId)
                .in(PointPriceSnapshotEntity::getPointId, repeatIds)
                .list();
        if (CollUtil.isNotEmpty(pointPriceSnapshotEntities)) {
            reserveIds.addAll(
                    pointPriceSnapshotEntities.stream()
                            .map(PointPriceSnapshotEntity::getPointId)
                            .distinct()
                            .toList());
        }


        List<Integer> deleteIds = new ArrayList<>();

        for (Map.Entry<String, List<PointEntity>> entry : repeatPointsMap.entrySet()) {
            List<Integer> ids = new ArrayList<>(entry.getValue().stream().map(PointEntity::getId).toList());
            int size = ids.size();
            ids.removeAll(reserveIds);
            if (size > ids.size()) {
                deleteIds.addAll(ids);
            } else {
                ids.remove(ids.size() - 1);
                deleteIds.addAll(ids);
            }
        }

        if (CollUtil.isNotEmpty(deleteIds)) {
            log.info("清理的点位重复数据：{}", JSON.toJSONString(deleteIds));
            pointService.removeByIds(deleteIds);
        }
    }

    public void cleanRepeatPointSnapshot(List<String> businessCodes) {
        if (CollUtil.isEmpty(businessCodes)) {
            return;
        }

        List<PointEntity> points = pointService.lambdaQuery()
                .select(PointEntity::getId, PointEntity::getCode)
                .in(PointEntity::getBusinessCode, businessCodes)
                .list();

        Map<String, List<PointEntity>> pointMap = points.stream().collect(Collectors.groupingBy(PointEntity::getCode, Collectors.toList()));

        Set<String> codes = pointMap.entrySet().stream().filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toSet());

        List<PointEntity> repeatPoints = points.stream().filter(point -> codes.contains(point.getCode())).toList();

        Map<String, List<PointEntity>> repeatPointsMap = repeatPoints.stream().collect(Collectors.groupingBy(PointEntity::getCode));

        for (Map.Entry<String, List<PointEntity>> stringListEntry : repeatPointsMap.entrySet()) {
            List<PointEntity> pointEntities = stringListEntry.getValue();
            List<Integer> list = pointEntities.stream().map(PointEntity::getId).toList();

            List<PointPriceSnapshotEntity> pointPriceSnapshotEntities = pointPriceSnapshotService.lambdaQuery()
                    .in(PointPriceSnapshotEntity::getPointId, list)
                    .list();

            if (CollUtil.isNotEmpty(pointPriceSnapshotEntities)) {
                pointPriceSnapshotService.lambdaUpdate()
                        .set(PointPriceSnapshotEntity::getPointId, list.get(0))
                        .in(PointPriceSnapshotEntity::getId, pointPriceSnapshotEntities.stream().map(PointPriceSnapshotEntity::getId).toList())
                        .update();
            }

            List<PointContractSnapshotEntity> pointContractSnapshotEntities = pointContractSnapshotService.lambdaQuery()
                    .in(PointContractSnapshotEntity::getPointId, list)
                    .list();

            if (CollUtil.isNotEmpty(pointContractSnapshotEntities)) {
                pointContractSnapshotService.lambdaUpdate()
                        .set(PointContractSnapshotEntity::getPointId, list.get(0))
                        .in(PointContractSnapshotEntity::getId, pointContractSnapshotEntities.stream().map(PointContractSnapshotEntity::getId).toList())
                        .update();
            }

        }

    }

    public void updatePointPlanStatus(UpdatePointPlanStatusDTO param) {
        if (CollUtil.isEmpty(param.getBusinessCodes())) {
            return;
        }

        if (StrUtil.isBlank(param.getStatus())) {
            return;
        }

        log.info("更新点位计划状态：{}", JSON.toJSONString(param));

        pointPlanService.lambdaUpdate()
                .set(PointPlanEntity::getStatus, param.getStatus())
                .in(PointPlanEntity::getBusinessCode, param.getBusinessCodes())
                .update();
    }

    public void refreshApplyFlag() {
        List<String> largeScreenDictCodes = largeScreenProperties.getLargeDictKey();

        List<String> smallScreenDictCodes = new ArrayList<>();
        smallScreenDictCodes.add("0013-1");
        smallScreenDictCodes.add("0013-2");
        smallScreenDictCodes.add("0013-3");
        smallScreenDictCodes.add("0013-4");

        List<PriceApplyPointDTO> priceApplyPointDTOS = priceApplyDao.selectIncentivePriceByApplyCode();

        Map<Integer, List<PriceApplyPointDTO>> collect = priceApplyPointDTOS.stream().collect(Collectors.groupingBy(PriceApplyPointDTO::getDeviceId));

        List<PriceApplyDeviceEntity> deviceEntities = new ArrayList<>();

        List<PriceApplyDTO> priceApplyDTOS = new ArrayList<>();

        for (Map.Entry<Integer, List<PriceApplyPointDTO>> integerListEntry : collect.entrySet()) {
            PriceApplyDeviceEntity priceApplyDeviceEntity = new PriceApplyDeviceEntity();
            Integer key = integerListEntry.getKey();
            List<PriceApplyPointDTO> priceApplyPoints = integerListEntry.getValue();
            List<String> size = priceApplyPoints.stream().map(PriceApplyPointDTO::getPointSize).toList();

            //比较largeScreenDictCodes和size是否有交集
            Collection<String> intersectionLarge = CollectionUtil.intersection(largeScreenDictCodes, size);
            //比较smallScreenDictCodes和size是否有交集
            Collection<String> intersectionSmall = CollectionUtil.intersection(smallScreenDictCodes, size);


            if (CollUtil.isNotEmpty(intersectionLarge) && CollUtil.isNotEmpty(intersectionSmall)) {
                priceApplyDeviceEntity.setId(key);
                priceApplyDeviceEntity.setLargeScreenFlag(3);
            } else if (CollUtil.isNotEmpty(intersectionLarge) && CollUtil.isEmpty(intersectionSmall)) {
                priceApplyDeviceEntity.setId(key);
                priceApplyDeviceEntity.setLargeScreenFlag(2);
            } else if (CollUtil.isEmpty(intersectionLarge) && CollUtil.isNotEmpty(intersectionSmall)) {
                priceApplyDeviceEntity.setId(key);
                priceApplyDeviceEntity.setLargeScreenFlag(1);
            }

            if (Objects.nonNull(priceApplyDeviceEntity.getId())) {
                deviceEntities.add(priceApplyDeviceEntity);
                PriceApplyDTO priceApplyDTO = new PriceApplyDTO();
                priceApplyDTO.setId(priceApplyPoints.get(0).getApplyId());
                priceApplyDTO.setLargeScreenFlag(priceApplyDeviceEntity.getLargeScreenFlag());
                priceApplyDTOS.add(priceApplyDTO);
            }

        }

        Map<Integer, List<PriceApplyDTO>> listMap = priceApplyDTOS.stream().collect(Collectors.groupingBy(PriceApplyDTO::getId));

        List<PriceApplyEntity> priceApplyEntities = new ArrayList<>();
        for (Map.Entry<Integer, List<PriceApplyDTO>> integerListEntry : listMap.entrySet()) {
            Integer key = integerListEntry.getKey();
            List<PriceApplyDTO> value = integerListEntry.getValue();
            Set<Integer> sets = value.stream().map(PriceApplyDTO::getLargeScreenFlag).collect(Collectors.toSet());
            PriceApplyEntity priceApply = new PriceApplyEntity();
            priceApply.setId(key);
            if (sets.contains(3) || (sets.contains(2) && sets.contains(1))) {
                priceApply.setLargeScreenFlag(3);
                priceApplyEntities.add(priceApply);

            }


            if (sets.contains(2) && !sets.contains(1) && !sets.contains(3)) {
                priceApply.setLargeScreenFlag(2);
                priceApplyEntities.add(priceApply);
            }

            if (sets.contains(1) && !sets.contains(2) && !sets.contains(3)) {
                priceApply.setLargeScreenFlag(1);
                priceApplyEntities.add(priceApply);
            }


        }

        priceApplyDeviceService.updateBatchById(deviceEntities);

        priceApplyService.updateBatchById(priceApplyEntities);


    }

    public void refreshBusinessStatus(List<String> businessCodes) {


        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getId, BusinessOpportunityEntity::getCode, BusinessOpportunityEntity::getStatus)
                .notIn(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.CLOSE.getCode())
                .in(CollUtil.isNotEmpty(businessCodes), BusinessOpportunityEntity::getCode, businessCodes)
                .list();

        Set<String> codes = businessOpportunityEntities.stream().map(BusinessOpportunityEntity::getCode).collect(Collectors.toSet());

        Map<String, List<CustomerFollowRecordEntity>> customerFollowRecordMap = customerFollowRecordService.lambdaQuery()
                .select(CustomerFollowRecordEntity::getId, CustomerFollowRecordEntity::getBusinessCode)
                .in(CustomerFollowRecordEntity::getBusinessCode, codes)
                .orderByAsc(BaseEntity::getCreateTime)
                .list().stream().collect(Collectors.groupingBy(CustomerFollowRecordEntity::getBusinessCode));

        Map<String, List<PointPlanEntity>> planMap = pointPlanService.lambdaQuery()
                .select(PointPlanEntity::getId, PointPlanEntity::getBusinessCode)
                .in(PointPlanEntity::getBusinessCode, codes)
                .orderByAsc(PointPlanEntity::getCreateTime)
                .list().stream().collect(Collectors.groupingBy(PointPlanEntity::getBusinessCode));

        Map<String, List<PriceApplyEntity>> priceMap = priceApplyService.lambdaQuery()
                .select(PriceApplyEntity::getId, PriceApplyEntity::getBusinessCode)
                .in(PriceApplyEntity::getBusinessCode, codes)
                .orderByAsc(BaseEntity::getCreateTime)
                .list().stream().collect(Collectors.groupingBy(PriceApplyEntity::getBusinessCode));

        ResultTemplate<List<BusinessOpportunityStatusVO>> cmsBusinessOpportunityStatus = feignCmsRpc.getBusinessOpportunityStatus(codes);

        Map<String, List<BusinessOpportunityStatusVO>> cmsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(cmsBusinessOpportunityStatus.getData())) {
            cmsMap = cmsBusinessOpportunityStatus.getData().stream()
                    .collect(Collectors.groupingBy(BusinessOpportunityStatusVO::getProjectCode));
        }


        Map<String, List<BuildingStatusChangeLogEntity>> buildingStatusChangeLogMap = buildingStatusChangeLogService.lambdaQuery()
                .eq(BuildingStatusChangeLogEntity::getType, "0042-4")
                .in(BuildingStatusChangeLogEntity::getBizCode, codes)
                .orderByDesc(BuildingStatusChangeLogEntity::getChangeTime)
                .list().stream().collect(Collectors.groupingBy(BuildingStatusChangeLogEntity::getBizCode));

        List<BuildingStatusChangeLogEntity> buildingStatusChangeLogEntities = new ArrayList<>();

        for (BusinessOpportunityEntity businessOpportunityEntity : businessOpportunityEntities) {
            try {
                String status = BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode();

                if (CollUtil.isNotEmpty(customerFollowRecordMap.get(businessOpportunityEntity.getCode()))) {
                    status = BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode();
                }

                if (CollUtil.isNotEmpty(planMap.get(businessOpportunityEntity.getCode()))) {
                    status = BusinessChangeStatusEnum.REACHING_INTENTION.getCode();
                }

                if (CollUtil.isNotEmpty(priceMap.get(businessOpportunityEntity.getCode()))) {
                    status = BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode();
                }

                if (CollUtil.isNotEmpty(cmsMap.get(businessOpportunityEntity.getCode()))) {
                    status = cmsMap.get(businessOpportunityEntity.getCode()).get(0).getStatus();
                }

                if (CollUtil.isNotEmpty(buildingStatusChangeLogMap.get(businessOpportunityEntity.getCode()))) {
                    List<BuildingStatusChangeLogEntity> buildingStatusChangeLogList = buildingStatusChangeLogMap.get(businessOpportunityEntity.getCode());
                    String finalStatus = status;
                    List<BuildingStatusChangeLogEntity> list = buildingStatusChangeLogList.stream()
                            .filter(buildingStatusChangeLogEntity -> buildingStatusChangeLogEntity.getStatus().equals(finalStatus)).toList();

                    if (CollUtil.isEmpty(list)) {
                        BuildingStatusChangeLogEntity buildingStatusChangeLogEntity = new BuildingStatusChangeLogEntity();
                        buildingStatusChangeLogEntity.setType("0042-4");
                        buildingStatusChangeLogEntity.setBizCode(businessOpportunityEntity.getCode());
                        buildingStatusChangeLogEntity.setBizId(businessOpportunityEntity.getId().longValue());
                        buildingStatusChangeLogEntity.setStatus(status);
                        buildingStatusChangeLogEntity.setContent("");
                        buildingStatusChangeLogEntity.setOperatorName("系统");
                        buildingStatusChangeLogEntity.setOperatorWno("CC0000");
                        buildingStatusChangeLogEntity.setCreateTime(LocalDateTime.now());
                        LocalDateTime changeTime = null;
                        if (status.equals(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode())) {
                            changeTime = businessOpportunityEntity.getCreateTime();
                        } else if (status.equals(BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode())) {
                            changeTime = customerFollowRecordMap.get(businessOpportunityEntity.getCode()).get(0).getCreateTime();
                        } else if (status.equals(BusinessChangeStatusEnum.REACHING_INTENTION.getCode())) {
                            changeTime = planMap.get(businessOpportunityEntity.getCode()).get(0).getCreateTime();
                        } else if (status.equals(BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode())) {
                            changeTime = priceMap.get(businessOpportunityEntity.getCode()).get(0).getCreateTime();
                        } else if (status.equals(BusinessChangeStatusEnum.CONTRACT_PHASE.getCode())) {
                            String dateString = cmsMap.get(businessOpportunityEntity.getCode()).get(0).getApplyTime();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            changeTime = LocalDateTime.parse(dateString, formatter);
                        } else if (status.equals(BusinessChangeStatusEnum.DEAL.getCode())) {
                            String dateString = cmsMap.get(businessOpportunityEntity.getCode()).get(0).getApplyTime();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            changeTime = LocalDateTime.parse(dateString, formatter);
                        }

                        buildingStatusChangeLogEntity.setChangeTime(changeTime);
                        buildingStatusChangeLogEntities.add(buildingStatusChangeLogEntity);
                    }
                }

                businessOpportunityEntity.setStatus(status);

            } catch (Exception e) {
                log.error("刷新商机状态失败businessCode：{},错误信息{}", businessOpportunityEntity.getCode(), e.getMessage());
            }

        }

        int totalSize = businessOpportunityEntities.size();
        int batchSize = (int) Math.ceil(totalSize / 8.0);

        for (int i = 0; i < totalSize; i += batchSize) {
            int end = Math.min(i + batchSize, totalSize);
            List<BusinessOpportunityEntity> subList = businessOpportunityEntities.subList(i, end);
            businessOpportunityService.updateBatchById(subList);
        }

        int totalLogSize = buildingStatusChangeLogEntities.size();
        int batchLogSize = (int) Math.ceil(totalLogSize / 8.0);

        for (int i = 0; i < totalLogSize; i += batchLogSize) {
            int end = Math.min(i + batchLogSize, totalLogSize);
            List<BuildingStatusChangeLogEntity> subList = buildingStatusChangeLogEntities.subList(i, end);
            buildingStatusChangeLogService.saveBatch(subList);
        }

        log.info("刷新商机状态完成");

    }


    public void refreshBuildingScreen() {
        log.info("开始刷新buildingScreen");
        List<BuildingGeneEntity> buildingGeneEntities = buildingGeneService.lambdaQuery()
                .select(BuildingGeneEntity::getBuildingRatingNo, BuildingGeneEntity::getSpec
                        , BuildingGeneEntity::getElevatorCount, BuildingGeneEntity::getCompanyCount
                        , BuildingGeneEntity::getBuildingSpacing, BuildingGeneEntity::getBuildingCeilingHeight
                        , BuildingGeneEntity::getSubmitCoefficient, BuildingGeneEntity::getFinalCoefficient
                        , BuildingGeneEntity::getSpecialDesc, BuildingGeneEntity::getCreateBy
                        , BuildingGeneEntity::getCreateTime, BuildingGeneEntity::getUpdateTime
                        , BuildingGeneEntity::getUpdateBy, BuildingGeneEntity::getTotalUnitCount)
                .list();
        List<BuildingScreenEntity> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(buildingGeneEntities)) {
            buildingGeneEntities.forEach(buildingGeneEntity -> {
                BuildingScreenEntity buildingScreenEntity = new BuildingScreenEntity();
                buildingScreenEntity.setCreateBy(buildingGeneEntity.getCreateBy());
                buildingScreenEntity.setCreateTime(buildingGeneEntity.getCreateTime());
                buildingScreenEntity.setUpdateBy(buildingGeneEntity.getUpdateBy());
                buildingScreenEntity.setUpdateTime(buildingGeneEntity.getUpdateTime());
                buildingScreenEntity.setBuildingRatingNo(buildingGeneEntity.getBuildingRatingNo());
                buildingScreenEntity.setSpec(buildingGeneEntity.getSpec());
                buildingScreenEntity.setTotalBuildingCount(buildingGeneEntity.getTotalUnitCount());
                buildingScreenEntity.setCompanyCount(buildingGeneEntity.getCompanyCount());
                buildingScreenEntity.setElevatorCount(buildingGeneEntity.getElevatorCount());
                buildingScreenEntity.setBuildingSpacing(buildingGeneEntity.getBuildingSpacing());
                buildingScreenEntity.setBuildingCeilingHeight(buildingGeneEntity.getBuildingCeilingHeight());
                buildingScreenEntity.setSubmitCoefficient(buildingGeneEntity.getSubmitCoefficient());
                buildingScreenEntity.setFinalCoefficient(buildingGeneEntity.getFinalCoefficient());
                buildingScreenEntity.setSpecialDesc(buildingGeneEntity.getSpecialDesc());
                list.add(buildingScreenEntity);
            });
        }

        buildingScreenService.saveBatch(list);
        log.info("结束刷新buildingScreen");
    }

    /**
     * 刷新历史审批记录数据，用于数据迁移
     * <p>
     * 该方法会遍历所有非草稿状态（approveLevel != 0）且未关联实例（instanceCode为空）的审批记录，
     * 并根据旧的场景（sceneType）和状态（status）字段，填充新的审批类型（approveType）、
     * 审批结果（approvalResult）和节点状态（nodeStatus）字段。
     */
    public void refreshApproveRecord() {
        log.info("开始刷新历史审批记录...");

        List<ScreenApproveRecordEntity> records = screenApproveRecordService.lambdaQuery()
                .select(ScreenApproveRecordEntity::getId,
                        ScreenApproveRecordEntity::getSceneType,
                        ScreenApproveRecordEntity::getOperateType,
                        ScreenApproveRecordEntity::getStatus,
                        ScreenApproveRecordEntity::getApproveLevel,
                        ScreenApproveRecordEntity::getInstanceCode)
                .eq(ScreenApproveRecordEntity::getInstanceCode, "")
                .list();

        if (CollectionUtil.isEmpty(records)) {
            log.info("没有需要刷新的历史审批记录。");
            return;
        }

        log.info("查询到 {} 条需要处理的审批记录。", records.size());

        for (ScreenApproveRecordEntity record : records) {
            switch (record.getSceneType()) {
                // 场景1：楼宇评级
                case 1:
                    handleBuildingApprovalRecord(record);
                    break;
                // 场景2：价格申请
                case 2:
                    handlePriceApprovalRecord(record);
                    break;
                default:
                    log.warn("发现未知的场景类型（SceneType），记录ID：{}，SceneType：{}", record.getId(), record.getSceneType());
                    break;
            }
        }

        // 批量更新已修改的记录
        screenApproveRecordService.updateBatchById(records);
        log.info("成功刷新 {} 条历史审批记录。", records.size());
    }

    public void refreshBuildingGene(Integer id) {
        List<BuildingGeneEntity> list = buildingGeneService.lambdaQuery()
                .select(BuildingGeneEntity::getTotalUnitCount,
                        BuildingGeneEntity::getTotalBuildingCount,
                        BuildingGeneEntity::getId,
                        BuildingGeneEntity::getMonthlyAvgPrice)
                .gt(Objects.nonNull(id), BuildingGeneEntity::getId, id)
                .list();
        for (BuildingGeneEntity buildingGene : list) {
            Integer totalUnitCount = buildingGene.getTotalUnitCount();
            Integer totalBuildingCount = buildingGene.getTotalBuildingCount();

            LambdaUpdateWrapper<BuildingGeneEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BuildingGeneEntity::getId, buildingGene.getId())
                    .eq(BuildingGeneEntity::getDeleteFlag, 0);
            updateWrapper.set(BuildingGeneEntity::getTotalUnitCount, totalBuildingCount);
            updateWrapper.set(BuildingGeneEntity::getTotalBuildingCount, totalUnitCount);

            // 洗入日租金
            BigDecimal monthlyAvgPrice = buildingGene.getMonthlyAvgPrice();
            if (Objects.nonNull(monthlyAvgPrice)) {
                BigDecimal dailyPrice = monthlyAvgPrice.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
                BigDecimal price = dailyPrice.stripTrailingZeros();
                // 总精度减去小数位数得到整数位数
                int integerLength = price.precision() - price.scale();
                if (integerLength <= 6) {
                    updateWrapper.set(BuildingGeneEntity::getDailyPrice, dailyPrice);
                } else {
                    log.warn("日租金精度过大，无法设置日租金，记录ID：{}，日租金：{}", buildingGene.getId(), dailyPrice);
                }
            }

            buildingGeneService.update(updateWrapper);
            log.info("刷新基因成功，id：{}", buildingGene.getId());
        }
    }

    /**
     * 处理楼宇评级相关的审批记录
     *
     * @param record 审批记录实体
     */
    private void handleBuildingApprovalRecord(ScreenApproveRecordEntity record) {
        // 根据 operateType 设置 approveType
        if (record.getOperateType() == 1) {
            record.setApproveType(ApprovalTypeEnum.BUILDING_APPROVAL.getCode());
        } else if (record.getOperateType() == 2) {
            record.setApproveType(ApprovalTypeEnum.COMPLETE_BUILDING_APPROVAL.getCode());
        }
        // 根据 status 设置最终结果和节点状态
        setApprovalResultFromStatus(record, BUILDING_APPROVAL_STATUS_MAP, "楼宇评级");
    }

    /**
     * 处理价格申请相关的审批记录
     *
     * @param record 审批记录实体
     */
    private void handlePriceApprovalRecord(ScreenApproveRecordEntity record) {
        record.setApproveType(ApprovalTypeEnum.PRICE_APPROVAL.getCode());
        setApprovalResultFromStatus(record, PRICE_APPROVAL_STATUS_MAP, "价格申请");
    }

    /**
     * 根据状态映射，为审批记录设置审批结果和节点状态
     *
     * @param record    要修改的审批记录
     * @param statusMap 状态码到审批结果编码的映射
     * @param sceneName 场景名称，用于日志记录
     */
    private void setApprovalResultFromStatus(ScreenApproveRecordEntity record, Map<Integer, String> statusMap, String sceneName) {
        String approvalResult = statusMap.get(record.getStatus());
        if (record.getApproveLevel() == 0) {
            record.setApprovalFlag(0);
        } else if (record.getApproveLevel() == 1) {
            record.setApprovalFlag(1);
        }

        if (approvalResult != null) {
            record.setApprovalResult(approvalResult);
            record.setNodeStatus(ApproveStatusEnum.COMPLETED.getCode());
        } else {
            log.warn("{}场景下发现未知的状态值，记录ID：{}，Status：{}", sceneName, record.getId(), record.getStatus());
        }
    }

    public void refreshDailyRent() {
        List<CityRentEntity> cityRentEntities = cityRentService.lambdaQuery().list();
        for (CityRentEntity cityRentEntity : cityRentEntities) {
            BigDecimal officeRent = cityRentEntity.getOfficeRent();
            if (Objects.nonNull(officeRent) && officeRent.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal officeRentDaily = officeRent.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
                cityRentEntity.setOfficeRentDaily(officeRentDaily);
            }

            BigDecimal residRent = cityRentEntity.getResidRent();
            if (Objects.nonNull(residRent) && residRent.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal residRentDaily = residRent.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
                cityRentEntity.setResidRentDaily(residRentDaily);
            }
        }

        cityRentService.updateBatchById(cityRentEntities);

        List<BuildingCityRentEntity> buildingCityRentEntities = buildingCityRentService.lambdaQuery().list();
        for (BuildingCityRentEntity cityRentEntity : buildingCityRentEntities) {
            BigDecimal officeRent = cityRentEntity.getOfficeRent();
            if (Objects.nonNull(officeRent) && officeRent.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal officeRentDaily = officeRent.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
                cityRentEntity.setOfficeRentDaily(officeRentDaily);
            }
        }

        buildingCityRentService.updateBatchById(buildingCityRentEntities);
    }

    public void refreshDetail(Integer id, List<String> buildingNos) {
        List<BuildingDetailsEntity> detailsEntities = buildingDetailsService.lambdaQuery()
                .in(CollUtil.isNotEmpty(buildingNos), BuildingDetailsEntity::getBuildingNo, buildingNos)
                .gt(Objects.nonNull(id), BuildingDetailsEntity::getId, id)
                .list();

        List<BuildingParameterEntity> parameterEntities = buildingParameterService.lambdaQuery()
                .list();

        for (BuildingParameterEntity parameterEntity : parameterEntities) {
            if ("月租金".equals(parameterEntity.getParameterName())) {
                buildingParameterService.lambdaUpdate()
                        .set(BuildingParameterEntity::getParameterName, "日租金")
                        .eq(BuildingParameterEntity::getId, parameterEntity.getId())
                        .update();
            }
        }

        Map<Long, BuildingParameterEntity> parameterMapping = parameterEntities
                .stream()
                .collect(Collectors.toMap(BuildingParameterEntity::getId, Function.identity()));

        for (BuildingDetailsEntity detailsEntity : detailsEntities) {

            log.info("开始处理，id:{}", detailsEntity.getId());

            // 存储parameter表id的项目存储对应的parameterName
            setParameterName(detailsEntity.getBuildingGrade(), parameterMapping, detailsEntity::setGradeName);
            setParameterName(detailsEntity.getBuildingLocation(), parameterMapping, detailsEntity::setLocationName);
            setParameterName(detailsEntity.getBuildingExterior(), parameterMapping, detailsEntity::setExteriorName);
            setParameterName(detailsEntity.getBuildingLobby(), parameterMapping, detailsEntity::setLobbyName);
            setParameterName(detailsEntity.getBuildingGarage(), parameterMapping, detailsEntity::setGarageName);
            setParameterName(detailsEntity.getBuildingHall(), parameterMapping, detailsEntity::setHallName);
            setParameterName(detailsEntity.getBuildingBrand(), parameterMapping, detailsEntity::setBrandName);
            setParameterName(detailsEntity.getBuildingRating(), parameterMapping, detailsEntity::setRatingName);
            setParameterName(detailsEntity.getBuildingSettled(), parameterMapping, detailsEntity::setSettledName);

            if (StrUtil.isNotBlank(detailsEntity.getBuildingPriceInput())) {
                log.info("开始处理价格，buildingPriceInput:{}", detailsEntity.getBuildingPriceInput());
                String priceStr = detailsEntity.getBuildingPriceInput().replaceAll("[^0-9\\.]", "");
                log.info("处理价格，priceStr:{}", priceStr);
                if (StrUtil.isNotBlank(priceStr)) {
                    BigDecimal price = new BigDecimal(priceStr);
                    detailsEntity.setDailyPriceInput(price.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP));
                }
            }

            if (StrUtil.isNotBlank(detailsEntity.getThirdBuildingPrice())) {
                log.info("开始处理AI价格，thirdBuildingPrice:{}", detailsEntity.getThirdBuildingPrice());
                String priceStr = detailsEntity.getThirdBuildingPrice().replaceAll("[^0-9\\.]", "");
                log.info("处理AI价格，priceStr:{}", priceStr);
                if (StrUtil.isNotBlank(priceStr)) {
                    try {
                        BigDecimal price = new BigDecimal(priceStr);
                        BigDecimal dailyPrice = price.divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
                        String dailyPriceStr = dailyPrice.toString();
                        if (dailyPriceStr.length() <= 20) {
                            detailsEntity.setThirdDailyPrice(dailyPriceStr);
                        } else {
                            log.warn("AI日租金精度过大，无法设置日租金，记录ID：{}，日租金：{}", detailsEntity.getId(), dailyPrice);
                        }
                    } catch (Exception ignored) {

                    }
                }
            }

            buildingDetailsService.updateById(detailsEntity);
        }
    }

    private void setParameterName(Long parameterId, Map<Long, BuildingParameterEntity> parameterMapping, Consumer<String> consumer) {
        if (Objects.isNull(parameterId)) {
            return;
        }
        BuildingParameterEntity parameter = parameterMapping.get(parameterId);
        consumer.accept(Objects.isNull(parameter) ? "" : parameter.getParameterName());
    }

    public void refreshPriceApply(List<String> applyCodes) {
        List<PriceApplyEntity> entities = priceApplyService.lambdaQuery()
                .in(CollUtil.isNotEmpty(applyCodes), PriceApplyEntity::getApplyCode, applyCodes)
                .list();

        for (PriceApplyEntity priceApply : entities) {
            if (StrUtil.isBlank(priceApply.getBuildingNo())) {
                continue;
            }

            // 设置楼宇基本信息
            BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                    .eq(BuildingRatingEntity::getBuildingNo, priceApply.getBuildingNo())
                    .eq(BuildingRatingEntity::getRatingVersion, priceApply.getRatingVersion())
                    .orderByDesc(BuildingRatingEntity::getId)
                    .last("limit 1")
                    .one();

            if (Objects.isNull(ratingEntity)) {
                continue;
            }

            String level = StringUtils.isBlank(ratingEntity.getProjectReviewLevel()) ? ratingEntity.getProjectLevel() : ratingEntity.getProjectReviewLevel();
            priceApply.setProjectLevel(level);
            priceApply.setMapProvince(ratingEntity.getMapProvince());
            priceApply.setMapCity(ratingEntity.getMapCity());
            priceApply.setMapRegion(ratingEntity.getMapRegion());
            priceApply.setMapAddress(ratingEntity.getMapAddress());
            priceApply.setBuildingType(ratingEntity.getBuildingType());
            if (Objects.isNull(priceApply.getBuildingName())) {
                priceApply.setBuildingName(ratingEntity.getBuildingName());
            }

            if (PriceApplyServiceImpl.SCREEN_FLAG_SMALL != priceApply.getLargeScreenFlag()) {
                // 设置大屏复核系数
                BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                        .eq(BuildingScreenEntity::getBuildingRatingNo, priceApply.getBuildingNo())
                        .last("limit 1")
                        .one();
                if (Objects.nonNull(screenEntity)) {
                    priceApply.setFinalCoefficient(screenEntity.getFinalCoefficient());
                }
            }

            // 设置水位价
            ConfigVO cityWaterMarkPrice = priceApplyService.getCityWaterMarkPrice(ratingEntity);
            if (Objects.isNull(cityWaterMarkPrice)) {
                continue;
            }

            // 设置水位价 把大屏水位价和小屏水位价保存
            String smallScreen = cityWaterMarkPrice.getValue();
            String bigScreen = priceApplyService.isCoreArea(ratingEntity) ? cityWaterMarkPrice.getExt1() : cityWaterMarkPrice.getExt2();
            // 大屏
            priceApply.setBigWaterMarkPrice(StringUtils.isBlank(bigScreen) ? BigDecimal.ZERO : new BigDecimal(bigScreen));
            // 小屏
            priceApply.setSmallWaterMarkPrice(StringUtils.isBlank(smallScreen) ? BigDecimal.ZERO : new BigDecimal(smallScreen));

            priceApplyService.updateById(priceApply);
        }
    }


    public void refreshForbiddenIndustry() {
        log.info("开始刷新楼宇基因中的禁用行业信息...");

        // 步骤 1: 查询所有的楼宇基因实体
        List<BuildingGeneEntity> geneList = buildingGeneService.list();
        if (CollUtil.isEmpty(geneList)) {
            log.info("楼宇基因表为空，无需刷新。");
            return;
        }
        log.info("从数据库中查询到 {} 条楼宇基因数据。", geneList.size());

        // 步骤 2: 收集所有楼宇评级编号，用于查询元数据
        List<String> buildingRatingNoList = geneList.stream()
                .map(BuildingGeneEntity::getBuildingRatingNo)
                .filter(StrUtil::isNotBlank).distinct().toList();

        if (CollUtil.isEmpty(buildingRatingNoList)) {
            log.warn("未能在楼宇基因数据中找到有效的楼宇评级编号。");
            return;
        }

        // 步骤 3: 根据评级编号批量查询楼宇元数据，并构建映射Map
        // Key: buildingRatingNo, Value: forbiddenIndustry (null会转换为空字符串)
        List<BuildingMetaEntity> buildingMetaEntities = buildingMetaService.listByBuildingNos(buildingRatingNoList);
        Map<String, String> metaMap = buildingMetaEntities.stream()
                .filter(meta -> Objects.nonNull(meta.getBuildingRatingNo()))
                .collect(Collectors.toMap(
                        BuildingMetaEntity::getBuildingRatingNo,
                        meta -> Objects.toString(meta.getForbiddenIndustry(), ""),
                        (oldVal, newVal) -> newVal
                ));
        log.info("从元数据表中查询到 {} 条有效的禁忌行业信息。", metaMap.size());


        // 步骤 4: 识别需要更新的记录，并更新其禁忌行业字段
        List<BuildingGeneEntity> genesToUpdate = new ArrayList<>();
        for (BuildingGeneEntity gene : geneList) {
            String newForbiddenIndustry = metaMap.getOrDefault(gene.getBuildingRatingNo(), "");
            String oldForbiddenIndustry = Objects.toString(gene.getForbiddenIndustry(), "");

            // 只有当禁忌行业信息发生变化时，才将其加入待更新列表
            if (!Objects.equals(newForbiddenIndustry, oldForbiddenIndustry)) {
                gene.setForbiddenIndustry(newForbiddenIndustry);
                genesToUpdate.add(gene);
            }
        }

        // 步骤 5: 如果有需要更新的记录，则执行批量更新
        if (CollUtil.isNotEmpty(genesToUpdate)) {
            log.info("准备更新 {} 条楼宇基因的禁忌行业信息。", genesToUpdate.size());
            buildingGeneService.updateBatchById(genesToUpdate, 1000);
        } else {
            log.info("所有楼宇基因的禁忌行业信息均是最新，无需更新。");
        }

        log.info("禁忌行业信息刷新完成。");
    }

    public void refreshApproveRecordCreator() {
        log.info("开始刷新审批记录的创建人信息...");
        List<ScreenApproveRecordEntity> recordList = screenApproveRecordService.lambdaQuery()
                .select(ScreenApproveRecordEntity::getNaturalKey, ScreenApproveRecordEntity::getId)
                .eq(ScreenApproveRecordEntity::getCreateBy, "")
                .list();
        if (CollUtil.isEmpty(recordList)) {
            log.info("未找到需要刷新的审批记录，操作结束。");
            return;
        }
        log.info("共需处理 {} 条审批记录。", recordList.size());
        List<String> ratingCode = recordList.stream().map(ScreenApproveRecordEntity::getNaturalKey)
                .filter(StrUtil::isNotBlank).distinct().toList();

        Map<String, String> map = buildingRatingService.lambdaQuery().select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getCreateBy)
                .in(BuildingRatingEntity::getBuildingNo, ratingCode).list()
                .stream().collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getCreateBy, (e1, e2) -> e1));

        int updateCount = 0;
        for (ScreenApproveRecordEntity record : recordList) {
            if (StringUtils.isBlank(record.getNaturalKey())) {
                log.warn("审批记录ID:{} 的 naturalKey 为空，跳过。", record.getId());
                continue;
            }
            String submitUser = map.get(record.getNaturalKey());
            if (StringUtils.isBlank(submitUser)) {
                log.warn("未找到楼宇编号:{} 对应的提交人，审批记录ID:{}，跳过。", record.getNaturalKey(), record.getId());
                continue;
            }
            record.setCreateBy(submitUser);
            record.setUpdateBy(submitUser);
            updateCount++;
        }
        if (updateCount > 0) {
            screenApproveRecordService.updateBatchById(recordList);
            log.info("成功刷新 {} 条审批记录的创建人信息。", updateCount);
        } else {
            log.info("无审批记录被更新。");
        }
    }

    /**
     * 刷新价格申请的核心区域信息
     * <p>
     * 该方法用于更新价格申请表中核心区域（locationName）为空的记录。
     * 通过直接查询楼宇详情数据，提取核心区域信息并更新到价格申请表中。
     * 处理步骤：
     * 1. 查询所有locationName为空的价格申请记录
     * 2. 获取这些记录关联的楼宇编号
     * 3. 直接查询对应的楼宇详情数据
     * 4. 根据详情信息，更新价格申请的核心区域
     */
    public void refreshPriceApplyCoreArea() {
        log.info("开始刷新价格申请核心区域信息...");
        long startTime = System.currentTimeMillis();

        // 1. 查询locationName为空的价格申请记录
        List<PriceApplyEntity> priceApplyList = priceApplyService.lambdaQuery()
                .select(PriceApplyEntity::getId, PriceApplyEntity::getBuildingNo)
                .eq(PriceApplyEntity::getLocationName, "")
                .list();

        // 2. 校验是否有需要处理的数据
        if (CollUtil.isEmpty(priceApplyList)) {
            log.info("无价格申请需要刷新核心区域信息，处理完成。");
            return;
        }
        log.info("查询到 {} 条需要刷新核心区域信息的价格申请记录", priceApplyList.size());

        // 3. 提取所有有效的楼宇编号
        List<String> buildingNoList = priceApplyList.stream()
                .map(PriceApplyEntity::getBuildingNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
        log.info("共涉及 {} 个不同的楼宇编号", buildingNoList.size());

        // 4. 直接查询楼宇详情数据
        Map<String, BuildingDetailsEntity> buildingDetailsMap = buildingDetailsService.lambdaQuery()
                .select(BuildingDetailsEntity::getBuildingNo, BuildingDetailsEntity::getLocationName)
                .in(BuildingDetailsEntity::getBuildingNo, buildingNoList)
                .eq(BuildingDetailsEntity::getDeleted, 0)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        BuildingDetailsEntity::getBuildingNo,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue
                ));
        log.debug("查询到 {} 个楼宇的详情数据", buildingDetailsMap.size());

        // 5. 处理每条价格申请记录
        int updatedCount = 0;
        int noDetailsCount = 0;
        List<PriceApplyEntity> updateLust = new ArrayList<>();
        for (PriceApplyEntity priceApply : priceApplyList) {
            try {
                // 获取当前楼宇的详情
                BuildingDetailsEntity buildingDetails = buildingDetailsMap.get(priceApply.getBuildingNo());
                if (buildingDetails == null) {
                    noDetailsCount++;
                    log.debug("楼宇 {} 没有找到详情数据", priceApply.getBuildingNo());
                    continue;
                }

                // 更新核心区域信息
                if (StringUtils.isNotBlank(buildingDetails.getLocationName())) {
                    priceApply.setLocationName(buildingDetails.getLocationName());
                    updatedCount++;
                    updateLust.add(priceApply);
                    log.debug("成功为价格申请ID:{} 设置核心区域:{}",
                            priceApply.getId(), buildingDetails.getLocationName());
                }
            } catch (Exception e) {
                log.error("处理价格申请ID:{} 时发生异常", priceApply.getId(), e);
            }
        }

        // 6. 批量更新价格申请记录
        if (updatedCount > 0) {
            priceApplyService.updateBatchById(updateLust);
            log.info("成功更新 {} 条价格申请的核心区域信息", updatedCount);
        }

        // 7. 记录处理结果统计
        log.info("价格申请核心区域刷新完成。总处理:{}, 成功更新:{}, 无详情记录:{}, 耗时:{}ms",
                priceApplyList.size(), updatedCount, noDetailsCount,
                System.currentTimeMillis() - startTime);
    }

    /**
     * 数据清理
     */
    public String buildingDataCorrect(MultipartFile projectFile) throws IOException {
        // 读项目文件
        List<ProjectFileParam> projectFileParamList = readProject(projectFile.getInputStream());
        // 洗数据
        List<ProjectFileParam> projectFileParamListResult = makeData(projectFileParamList);

        return makeExcel(projectFileParamListResult);
    }

    /**
     * 编码处理
     */
    private List<ProjectFileParam> makeData(List<ProjectFileParam> projectFileParamList) {
        List<BuildingRatingEntity> buildingRatingEntities = new ArrayList<>();
        List<BuildingMetaEntity> buildingMetaEntities = new ArrayList<>();
        List<BusinessOpportunityEntity> businessOpportunityEntities = new ArrayList<>();
        List<PointPlanEntity> pointPlanEntities = new ArrayList<>();

        List<PriceApplyEntity> priceApplyUpdateEntities = new ArrayList<>();
        List<WaitingHallBusinessEntity> waitingHallBusinessUpdateEntities = new ArrayList<>();
        List<PointEntity> pointUpdateEntities = new ArrayList<>();

        // 价格申请编码
        Set<String> applyCodes = projectFileParamList.stream().map(ProjectFileParam::getPriceApplyCode)
                .collect(Collectors.toSet());
        List<PriceApplyEntity> priceApplyEntities = priceApplyService.lambdaQuery()
                .select(PriceApplyEntity::getId, PriceApplyEntity::getApplyCode, PriceApplyEntity::getBusinessCode,
                        PriceApplyEntity::getBuildingNo)
                .in(PriceApplyEntity::getApplyCode, applyCodes)
                .list();

        // key: code
        Map<String, PriceApplyEntity> pricApplyMap = priceApplyEntities.stream().collect(Collectors.toMap(PriceApplyEntity::getApplyCode, item -> item));

        // 处理点位
        Map<String, Integer> pointMap = handlePoint(projectFileParamList, waitingHallBusinessUpdateEntities, pointUpdateEntities);

        projectFileParamList.forEach(projectFileParam -> {
            if (StringUtils.isNotBlank(projectFileParam.getPoints())) {
                List<String> list = Arrays.asList(projectFileParam.getPoints().split(","));
                List<String> distinctList = list.stream().distinct().toList();
                projectFileParam.setPointCodes(distinctList);
            }

            if (StringUtils.isBlank(projectFileParam.getBDWorkNo())) {
                projectFileParam.setBDWorkNo("CC0000");
            }

            if (Objects.equals("0", projectFileParam.getIsKeep())) {
                // 构建楼宇Rating
                String buildingNo = codeGenerator.generateRatingCode();
                BuildingRatingEntity buildingRating = makeBuildingRatingEntity(projectFileParam, buildingNo);
                projectFileParam.setBuildingNoNew(buildingNo);
                buildingRatingEntities.add(buildingRating);

                // 构建meta
                String buildingMetaNo = buildingMetaService.getBuildingMetaNo();
                projectFileParam.setBuildingMetaNoNew(buildingMetaNo);
                BuildingMetaEntity buildingMetaEntity = makeBuildingMetaEntity(projectFileParam, buildingMetaNo, buildingNo);
                buildingMetaEntities.add(buildingMetaEntity);

                // 构建商机
                String businessCode = buildingNo + "-1";
                BusinessOpportunityEntity businessOpportunity = makeBusinessEntity(projectFileParam, businessCode, buildingNo);
                projectFileParam.setBusinessCode(businessCode);
                businessOpportunityEntities.add(businessOpportunity);

                if (StringUtils.isNotBlank(projectFileParam.getPriceApplyCode())) {
                    // 构建点位方案
                    PointPlanEntity pointPlanEntity = makePointPlanEntity(projectFileParam, businessCode, buildingNo);
                    pointPlanEntities.add(pointPlanEntity);

                    // 构建价格
                    PriceApplyEntity priceApplyEntity = makePriceApplyEntity(projectFileParam, businessCode, buildingNo, pricApplyMap);
                    if (Objects.nonNull(priceApplyEntity)) {
                        priceApplyUpdateEntities.add(priceApplyEntity);
                    }

                    // 处理点位和等待大厅关系
                    handlePointAndWaitingHall(projectFileParam, waitingHallBusinessUpdateEntities, pointMap, pointUpdateEntities);
                }
            }
        });
        // 保存修改数据
        saveOrUpdate(buildingRatingEntities, buildingMetaEntities, businessOpportunityEntities,
                pointPlanEntities, priceApplyUpdateEntities, waitingHallBusinessUpdateEntities, pointUpdateEntities);
        return projectFileParamList;
    }

    /**
     * 保存修改数据
     */
    private void saveOrUpdate(List<BuildingRatingEntity> buildingRatingEntities,
                              List<BuildingMetaEntity> buildingMetaEntities,
                              List<BusinessOpportunityEntity> businessOpportunityEntities,
                              List<PointPlanEntity> pointPlanEntities,
                              List<PriceApplyEntity> priceApplyUpdateEntities,
                              List<WaitingHallBusinessEntity> waitingHallBusinessUpdateEntities,
                              List<PointEntity> pointUpdateEntities) {
        buildingRatingService.saveBatch(buildingRatingEntities);
        buildingMetaService.saveBatch(buildingMetaEntities);
        businessOpportunityService.saveBatch(businessOpportunityEntities);
        pointPlanService.saveBatch(pointPlanEntities);

        priceApplyService.updateBatchById(priceApplyUpdateEntities);
        waitingHallBusinessService.updateBatchById(waitingHallBusinessUpdateEntities);
        pointService.updateBatchById(pointUpdateEntities);
    }


    /**
     * 生成文件处理
     */
    private String makeExcel(List<ProjectFileParam> projectFileParamList) throws IOException {

        // 创建临时文件
        File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
        tempFile.deleteOnExit();

        try {
            // 写入Excel
            writeExcelFile(tempFile, projectFileParamList);

            // 上传到云存储
            return uploadToCloud(tempFile);
        } finally {
            // 主动删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }


    private String uploadToCloud(File tempFile) {
        String feature = "meht";
        String fileName = UUID.randomUUID() + ".xlsx";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    private void writeExcelFile(File tempFile, List<ProjectFileParam> projectData) {
        if (tempFile == null) {
            throw new IllegalArgumentException("临时文件不能为空");
        }
        if (projectData == null) {
            throw new IllegalArgumentException("楼宇不能为空");
        }
        // 使用同一个ExcelWriter写入多个sheet
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            // 写入第一个sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "导入结果").head(ProjectFileParam.class).build();
            excelWriter.write(projectData, writeSheet1);
        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new CommonException("写入Excel文件失败: " + e.getMessage());
        }

    }

    /**
     * 处理点位和等待大厅关系
     */
    private void handlePointAndWaitingHall(ProjectFileParam projectFileParam,
                                           List<WaitingHallBusinessEntity> waitingHallBusinessEntityList,
                                           Map<String, Integer> pointMap,
                                           List<PointEntity> pointUpdateEntities) {
        List<String> pointCodes = projectFileParam.getPointCodes();

        Map<Integer, List<WaitingHallBusinessEntity>> waitingMap = waitingHallBusinessEntityList.stream()
                .collect(Collectors.groupingBy(WaitingHallBusinessEntity::getWaitingHallId));

        Map<String, List<PointEntity>> pointUpdateMap = pointUpdateEntities.stream()
                .collect(Collectors.groupingBy(PointEntity::getCode));

        for (String code : pointCodes) {
            // 处理等候厅
            Integer waitingHallId = pointMap.get(code);
            List<WaitingHallBusinessEntity> waitingHallBusinessEntities = waitingMap.get(waitingHallId);
            if (CollectionUtil.isNotEmpty(waitingHallBusinessEntities)) {
                for (WaitingHallBusinessEntity waitingHallBusinessEntity : waitingHallBusinessEntities) {
                    waitingHallBusinessEntity.setBusinessCode(projectFileParam.getBusinessCode());
                }
            }

            // 处理点位
            List<PointEntity> pointEntities = pointUpdateMap.get(code);
            if (CollectionUtil.isNotEmpty(pointEntities)) {
                for (PointEntity pointEntity : pointEntities) {
                    pointEntity.setBusinessCode(projectFileParam.getBusinessCode());
                    pointEntity.setBuildingRatingNo(projectFileParam.getBuildingNoNew());
                }
            }
        }

    }

    /**
     * 处理点位
     */
    private Map<String, Integer> handlePoint(List<ProjectFileParam> projectFileParamList,
                                             List<WaitingHallBusinessEntity> waitingHallBusinessEntityList,
                                             List<PointEntity> pointUpdateEntities) {
        List<PointDetail> points = new ArrayList<>();
        Set<String> buildingNos = projectFileParamList.stream().map(ProjectFileParam::getBuildingNo).collect(Collectors.toSet());
        for (String buildingNo : buildingNos) {
            try {
                ResultTemplate<List<PointDetail>> listResultTemplate = feignSspRpc.pointList(buildingNo);
                if (Objects.nonNull(listResultTemplate) && CollectionUtil.isNotEmpty(listResultTemplate.getData())) {
                    points.addAll(listResultTemplate.getData());
                }
            } catch (Exception e) {
                log.error("ssp报错{}", buildingNo);
            }
        }

        Set<Integer> collect = points.stream().map(PointDetail::getWaitingHallId).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(collect)) {
            List<WaitingHallBusinessEntity> list = waitingHallBusinessService.lambdaQuery()
                    .select(WaitingHallBusinessEntity::getId, WaitingHallBusinessEntity::getWaitingHallId, WaitingHallBusinessEntity::getBusinessCode)
                    .in(WaitingHallBusinessEntity::getWaitingHallId, collect)
                    .list();
            waitingHallBusinessEntityList.addAll(list);
        }

        List<PointEntity> pointEntities = pointService.lambdaQuery()
                .select(PointEntity::getId, PointEntity::getBusinessCode, PointEntity::getCode, PointEntity::getBuildingRatingNo)
                .in(PointEntity::getBuildingRatingNo, buildingNos)
                .list();
        pointUpdateEntities.addAll(pointEntities);


        return points.stream().collect(Collectors.toMap(PointDetail::getPointCode, PointDetail::getWaitingHallId, (a, b) -> b));
    }

    private BuildingRatingEntity makeBuildingRatingEntity(ProjectFileParam projectFileParam, String buildingNo) {
        BuildingRatingEntity buildingRating = new BuildingRatingEntity();
        buildingRating.setBuildingNo(buildingNo);
        buildingRating.setBuildingName(projectFileParam.getBuildingName());
        Integer buildingType = switch (projectFileParam.getBuildingType()) {
            case "写字楼" -> 0;
            case "商住楼" -> 1;
            case "综合体" -> 2;
            case "产业园区" -> 3;
            default -> null;
        };
        buildingRating.setBuildingType(buildingType);
        buildingRating.setBuildingStatus(3);
        buildingRating.setStatus(1);
        buildingRating.setBuildingDesc("重复楼宇编码清洗数据");
        buildingRating.setMapProvince(projectFileParam.getMapProvince());
        buildingRating.setMapCity(projectFileParam.getMapCity());
        buildingRating.setMapRegion(projectFileParam.getMapRegion());
        buildingRating.setMapAddress(rsaExample.encryptByPublic(projectFileParam.getMapAddress()));
        buildingRating.setMapLatitude(rsaExample.encryptByPublic(projectFileParam.getMapLatitude()));
        buildingRating.setMapLongitude(rsaExample.encryptByPublic(projectFileParam.getMapLongitude()));
        buildingRating.setMapAdCode(projectFileParam.getMapCode());
        buildingRating.setSubmitUser(projectFileParam.getBDWorkNo());
        buildingRating.setSubmitTime(LocalDateTime.now());
        buildingRating.setCreateBy(projectFileParam.getBDWorkNo());
        buildingRating.setUpdateBy(projectFileParam.getBDWorkNo());
        buildingRating.setMapNo(buildingNo);
        return buildingRating;
    }

    private BuildingMetaEntity makeBuildingMetaEntity(ProjectFileParam projectFileParam, String buildingMetaNo, String buildingNo) {
        BuildingMetaEntity buildingMetaEntity = new BuildingMetaEntity();
        buildingMetaEntity.setBuildingMetaNo(buildingMetaNo);
        Integer buildingType = switch (projectFileParam.getBuildingType()) {
            case "写字楼" -> 0;
            case "商住楼" -> 1;
            case "综合体" -> 2;
            case "产业园区" -> 3;
            default -> null;
        };
        buildingMetaEntity.setBuildingType(buildingType);
        buildingMetaEntity.setManager(projectFileParam.getBDWorkNo());
        buildingMetaEntity.setBuildingDesc("重复楼宇编码清洗数据");
        buildingMetaEntity.setBuildingStatus(3);
        buildingMetaEntity.setBuildingRatingNo(buildingNo);
        buildingMetaEntity.setBuildingName(projectFileParam.getBuildingName());
        buildingMetaEntity.setCreateBy(projectFileParam.getBDWorkNo());
        buildingMetaEntity.setUpdateBy(projectFileParam.getBDWorkNo());
        buildingMetaEntity.setMapAdCode(projectFileParam.getMapCode());
        buildingMetaEntity.setMapAddress(rsaExample.encryptByPublic(projectFileParam.getMapAddress()));
        buildingMetaEntity.setMapLatitude(rsaExample.encryptByPublic(projectFileParam.getMapLatitude()));
        buildingMetaEntity.setMapLongitude(rsaExample.encryptByPublic(projectFileParam.getMapLongitude()));
        buildingMetaEntity.setMapProvince(projectFileParam.getMapProvince());
        buildingMetaEntity.setMapCity(projectFileParam.getMapCity());
        buildingMetaEntity.setMapRegion(projectFileParam.getMapRegion());
        buildingMetaEntity.setMapNo(buildingMetaNo);

        return buildingMetaEntity;
    }

    private BusinessOpportunityEntity makeBusinessEntity(ProjectFileParam projectFileParam, String businessCode, String buildingNo) {
        BusinessOpportunityEntity buildingOpportunityEntity = new BusinessOpportunityEntity();
        buildingOpportunityEntity.setCode(businessCode);
        buildingOpportunityEntity.setBuildingNo(buildingNo);
        buildingOpportunityEntity.setName(projectFileParam.getBuildingName() + "-默认");
        buildingOpportunityEntity.setStatus(BusinessChangeStatusEnum.CONTRACT_PHASE.getCode());
        buildingOpportunityEntity.setSubmitUser(projectFileParam.getBDWorkNo());
        buildingOpportunityEntity.setOwner(projectFileParam.getBDWorkNo());
        buildingOpportunityEntity.setCreateBy(projectFileParam.getBDWorkNo());
        buildingOpportunityEntity.setUpdateBy(projectFileParam.getBDWorkNo());
        return buildingOpportunityEntity;
    }

    private PointPlanEntity makePointPlanEntity(ProjectFileParam projectFileParam, String businessCode, String buildingNo) {
        PointPlanEntity pointPlanEntity = new PointPlanEntity();
        pointPlanEntity.setCreateBy(projectFileParam.getBDWorkNo());
        pointPlanEntity.setUpdateBy(projectFileParam.getBDWorkNo());
        pointPlanEntity.setBuildingRatingNo(buildingNo);
        pointPlanEntity.setStatus(PointPlanStatusEnum.SIGNED.getCode());
        pointPlanEntity.setBusinessCode(businessCode);
        return pointPlanEntity;

    }

    private PriceApplyEntity makePriceApplyEntity(ProjectFileParam projectFileParam, String businessCode, String buildingNo, Map<String, PriceApplyEntity> pricMap) {

        PriceApplyEntity priceApply = pricMap.get(projectFileParam.getPriceApplyCode());
        if (Objects.nonNull(priceApply)) {
            priceApply.setBusinessCode(businessCode);
            priceApply.setBuildingNo(buildingNo);
            return priceApply;
        }

        return null;
    }

    /**
     * 读取项目文件
     */
    private List<ProjectFileParam> readProject(InputStream inputStreamProject) {
        List<ProjectFileParam> projectFileParamList = new ArrayList<>();

        // 读项目文件
        EasyExcel.read(inputStreamProject, ProjectFileParam.class, new AnalysisEventListener<ProjectFileParam>() {
            private int rowIndex = 0;

            @Override
            public void invoke(ProjectFileParam data, AnalysisContext context) {
                rowIndex++;
                projectFileParamList.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("读取完成，总行数：{}", rowIndex);
            }
        }).sheet(0).headRowNumber(1).doRead();

        return projectFileParamList;
    }

    /**
     * 更新楼宇位置信息
     *
     * @param buildingLocationUpdateDTO 楼宇位置信息更新DTO
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBuildingLocation(BuildingLocationUpdateDTO buildingLocationUpdateDTO) {
        String buildingNo = buildingLocationUpdateDTO.getBuildingNo();
        log.info("开始更新楼宇位置信息，楼宇编码：{}", buildingNo);

        try {
            // 1. 更新BuildingRating表
            boolean ratingUpdated = updateBuildingRatingLocation(buildingLocationUpdateDTO);
            log.info("BuildingRating表更新结果：{}", ratingUpdated);

            // 2. 更新CompleteRating表
            boolean completeUpdated = updateCompleteRatingLocation(buildingLocationUpdateDTO);
            log.info("CompleteRating表更新结果：{}", completeUpdated);

            // 3. 更新BuildingMeta表
            boolean metaUpdated = updateBuildingMetaLocation(buildingLocationUpdateDTO);
            log.info("BuildingMeta表更新结果：{}", metaUpdated);

            return ratingUpdated || completeUpdated || metaUpdated;
        } catch (Exception e) {
            log.error("更新楼宇位置信息失败，楼宇编码：{}", buildingNo, e);
            throw new CommonException("更新楼宇位置信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新BuildingRating表的位置信息
     */
    private boolean updateBuildingRatingLocation(BuildingLocationUpdateDTO dto) {

        if (StrUtil.isNotBlank(dto.getMapNo())) {
            LambdaQueryWrapper<BuildingRatingEntity> existWrapper = Wrappers.lambdaQuery();
            existWrapper.eq(BuildingRatingEntity::getMapNo, dto.getMapNo());
            boolean exists = buildingRatingService.exists(existWrapper);
            if (exists) {
                throw new BusinessException("该mapNo已存在");
            }
        }

        // 先查询现有记录，获取原始的创建人、创建时间、更新人、更新时间
        BuildingRatingEntity existingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                .one();

        if (existingEntity == null) {
            return false;
        }

        LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = Wrappers.lambdaUpdate(BuildingRatingEntity.class)
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                // 保持原有的创建人、创建时间、更新人、更新时间不变
                .set(BuildingRatingEntity::getCreateBy, existingEntity.getCreateBy())
                .set(BuildingRatingEntity::getCreateTime, existingEntity.getCreateTime())
                .set(BuildingRatingEntity::getUpdateBy, existingEntity.getUpdateBy())
                .set(BuildingRatingEntity::getUpdateTime, existingEntity.getUpdateTime());

        boolean hasUpdate = false;

        if (StrUtil.isNotBlank(dto.getMapAddress())) {
            updateWrapper.set(BuildingRatingEntity::getMapAddress, rsaExample.encryptByPublic(dto.getMapAddress()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapLongitude())) {
            updateWrapper.set(BuildingRatingEntity::getMapLongitude, rsaExample.encryptByPublic(dto.getMapLongitude()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapLatitude())) {
            updateWrapper.set(BuildingRatingEntity::getMapLatitude, rsaExample.encryptByPublic(dto.getMapLatitude()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapNo())) {
            updateWrapper.set(BuildingRatingEntity::getMapNo, dto.getMapNo());
            hasUpdate = true;
        }

        if (hasUpdate) {
            return buildingRatingService.update(updateWrapper);
        }
        return false;
    }

    /**
     * 更新CompleteRating表的位置信息
     */
    private boolean updateCompleteRatingLocation(BuildingLocationUpdateDTO dto) {
        // 先查询现有记录，获取原始的创建人、创建时间、更新人、更新时间
        CompleteRatingEntity existingEntity = completeRatingService.lambdaQuery()
                .eq(CompleteRatingEntity::getBuildingRatingNo, dto.getBuildingNo())
                .one();

        if (existingEntity == null) {
            return false;
        }

        LambdaUpdateWrapper<CompleteRatingEntity> updateWrapper = Wrappers.lambdaUpdate(CompleteRatingEntity.class)
                .eq(CompleteRatingEntity::getBuildingRatingNo, dto.getBuildingNo())
                // 保持原有的创建人、创建时间、更新人、更新时间不变
                .set(CompleteRatingEntity::getCreateBy, existingEntity.getCreateBy())
                .set(CompleteRatingEntity::getCreateTime, existingEntity.getCreateTime())
                .set(CompleteRatingEntity::getUpdateBy, existingEntity.getUpdateBy())
                .set(CompleteRatingEntity::getUpdateTime, existingEntity.getUpdateTime());

        boolean hasUpdate = false;

        if (StrUtil.isNotBlank(dto.getMapAddress())) {
            updateWrapper.set(CompleteRatingEntity::getMapAddress, rsaExample.encryptByPublic(dto.getMapAddress()));
            hasUpdate = true;
        }

        if (hasUpdate) {
            return completeRatingService.update(updateWrapper);
        }
        return false;
    }

    /**
     * 更新BuildingMeta表的位置信息
     */
    private boolean updateBuildingMetaLocation(BuildingLocationUpdateDTO dto) {
        // 先查询现有记录，获取原始的创建人、创建时间、更新人、更新时间
        BuildingMetaEntity existingEntity = buildingMetaService.lambdaQuery()
                .eq(BuildingMetaEntity::getBuildingRatingNo, dto.getBuildingNo())
                .one();

        if (existingEntity == null) {
            return false;
        }

        LambdaUpdateWrapper<BuildingMetaEntity> updateWrapper = Wrappers.lambdaUpdate(BuildingMetaEntity.class)
                .eq(BuildingMetaEntity::getBuildingRatingNo, dto.getBuildingNo())
                // 保持原有的创建人、创建时间、更新人、更新时间不变
                .set(BuildingMetaEntity::getCreateBy, existingEntity.getCreateBy())
                .set(BuildingMetaEntity::getCreateTime, existingEntity.getCreateTime())
                .set(BuildingMetaEntity::getUpdateBy, existingEntity.getUpdateBy())
                .set(BuildingMetaEntity::getUpdateTime, existingEntity.getUpdateTime());

        boolean hasUpdate = false;

        if (StrUtil.isNotBlank(dto.getMapAddress())) {
            updateWrapper.set(BuildingMetaEntity::getMapAddress, rsaExample.encryptByPublic(dto.getMapAddress()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapLongitude())) {
            updateWrapper.set(BuildingMetaEntity::getMapLongitude, rsaExample.encryptByPublic(dto.getMapLongitude()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapLatitude())) {
            updateWrapper.set(BuildingMetaEntity::getMapLatitude, rsaExample.encryptByPublic(dto.getMapLatitude()));
            hasUpdate = true;
        }
        if (StrUtil.isNotBlank(dto.getMapNo())) {
            updateWrapper.set(BuildingMetaEntity::getMapNo, dto.getMapNo());
            hasUpdate = true;
        }

        if (hasUpdate) {
            return buildingMetaService.update(updateWrapper);
        }
        return false;
    }

}

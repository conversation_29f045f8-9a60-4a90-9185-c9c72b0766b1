package com.coocaa.meht.module.dataimport.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyConvert;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyDevicePointVO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyDeviceVO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyUpdateStatusBO;
import com.coocaa.meht.module.dataimport.pojo.PriceApplyVO;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.DataHandlerService;
import com.coocaa.meht.module.web.service.PriceApplyDevicePointService;
import com.coocaa.meht.module.web.service.PriceApplyDeviceService;
import com.coocaa.meht.module.web.service.impl.PriceApplyServiceImpl;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 保存价格申请
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApplyService extends ServiceImpl<PriceApplyDao, PriceApplyEntity> {
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final StringRedisTemplate stringRedisTemplate;
    private final PriceApplyDeviceService applyDeviceService;
    private final DataHandlerService crmDataHandlerService;
    private final SysUserService sysUserService;
    private final BuildingRatingService buildingRatingService;

    private final PriceApplyDevicePointService priceApplyDevicePointService;
    private final PriceApplyServiceImpl priceApplyServiceImpl;

    @Autowired(required = false)
    private RsaExample rsaExample;


    @Transactional(rollbackFor = Exception.class)
    public String createPriceApply(PriceApplyVO applyParam) {
        // 检查是否可以申请
        Set<Integer> notAllowedStatus = Sets.newHashSet(PriceApplyEntity.Status.PENDING.getCode());
        List<PriceApplyEntity> existed = lambdaQuery()
                .select(PriceApplyEntity::getId, PriceApplyEntity::getApplyCode)
                .eq(PriceApplyEntity::getBuildingNo, applyParam.getBuildingNo())
                .eq(PriceApplyEntity::getBusinessCode, applyParam.getBusinessCode())
                .in(PriceApplyEntity::getStatus, notAllowedStatus).list();
        if (CollUtil.isNotEmpty(existed) && existed.size() > 1 && StringUtils.isNotEmpty(existed.get(0).getApplyCode())) {
            return existed.get(0).getApplyCode();
        }

        // 保存价格申请数据
        boolean result = false;
        PriceApplyEntity priceApply = PriceApplyConvert.INSTANCE.toEntity(applyParam);
        try {
            SysUserEntity sysUser = sysUserService.getByCode(applyParam.getWno());
            if (Objects.nonNull(sysUser)) {
                sysUser.setMobile(rsaExample.decryptByPrivate(sysUser.getMobile()));
                priceApply.setCrmUserId(crmDataHandlerService.getCrmOwnerUserId(sysUser.getRealName(), sysUser.getMobile()));
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        // 保存楼盘名称
        String buildingName = Optional.ofNullable(buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getBuildingName)
                .eq(BuildingRatingEntity::getBuildingNo, applyParam.getBuildingNo())
                .orderByDesc(BuildingRatingEntity::getId).last("limit 1").one()
        ).map(BuildingRatingEntity::getBuildingName).orElse("");

        // 保存价格申请数据
        priceApply.setId(null);
        priceApply.setBuildingName(buildingName);
        priceApply.setTotalAmount(calculateAmount(applyParam.getDevices()));
        priceApply.setApplyCode(getApplyCode(applyParam.getCreateTime()));
        priceApply.setCreateBy(applyParam.getWno());
        priceApply.setCreateTime(applyParam.getCreateTime());
        priceApply.setUpdateBy(applyParam.getWno());
        priceApply.setUpdateTime(applyParam.getCreateTime());
        priceApply.setStatus(PriceApplyEntity.Status.PASSED.getCode());
        result |= save(priceApply);

        // 保存价格申请设备数据
        if (CollectionUtil.isNotEmpty(applyParam.getDevices())) {
            List<PriceApplyDeviceVO> devices = applyParam.getDevices();
            if (CollectionUtil.isEmpty(devices)) throw new ServerException("该申请无设备，请选择设备");
            List<PriceApplyDevicePointEntity> allPoints = new ArrayList<>();
            for (PriceApplyDeviceVO device : devices) {
                PriceApplyDeviceEntity priceApplyDeviceEntity = BeanUtil.copyProperties(device, PriceApplyDeviceEntity.class);
                priceApplyDeviceEntity.setCreateBy(applyParam.getWno());
                priceApplyDeviceEntity.setCreateTime(applyParam.getCreateTime());
                priceApplyDeviceEntity.setUpdateBy(applyParam.getWno());
                priceApplyDeviceEntity.setUpdateTime(applyParam.getCreateTime());

                result = applyDeviceService.save(priceApplyDeviceEntity);
                if (result) {
                    List<PriceApplyDevicePointVO> points = device.getPoints();
                    if (CollectionUtil.isNotEmpty(device.getPoints())) {
                        List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = points.stream()
                                .map(point -> new PriceApplyDevicePointEntity(null, priceApply.getId(), priceApplyDeviceEntity.getId(), point.getName(), point.getCode()))
                                .toList();
                        allPoints.addAll(priceApplyDevicePointEntities);
                    }
                }
            }
            result = priceApplyDevicePointService.saveBatch(allPoints);
        }

        if (result) {
            try {
                priceApplyServiceImpl.sendPointMessage(priceApply.getId());
            } catch (Exception e) {
                log.error("价格申请创建成功发送消息失败");
            }
        }

        return priceApply.getApplyCode();
    }


    /**
     * 计算合同总金额
     */
    private BigDecimal calculateAmount(List<PriceApplyDeviceVO> devices) {
        if (CollectionUtil.isEmpty(devices)) {
            return BigDecimal.ZERO;
        }

        return devices.stream()
                .filter(device -> Objects.nonNull(device.getSignPrice()))
                .filter(device -> Objects.nonNull(device.getQuantity()))
                .map(device -> device.getSignPrice().multiply(new BigDecimal(device.getQuantity())))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }


    /**
     * 生成申请编号
     * JGSQ+年月日+流水号（4位，从0001开始）
     */
    private String getApplyCode(LocalDateTime createTime) {
        String today = DATE_FORMATTER.format(createTime);
        String cacheKey = "meht:price:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("JGSQ%s%04d", today, index);
    }

    public void updatePointApplyStatus(PriceApplyUpdateStatusBO bo) {
        if (CollectionUtil.isEmpty(bo.getPriceApplyIds()) || StrUtil.isBlank(bo.getStatus())) {
            return;
        }
        this.lambdaUpdate().in(PriceApplyEntity::getId, bo.getPriceApplyIds())
                .set(PriceApplyEntity::getStatus, bo.getStatus()).update();

    }
}

package com.coocaa.meht.module.dataimport.pojo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-12
 */
@Data
public class BusinessOpportunityDeleteParam {
    /**
     * 商机id
     */
    @NotNull(message = "商机id不能为空")
    @Min(value = 1, message = "商机id不能小于1")
    private Integer businessId;

    /**
     * 鉴权code
     */
    @NotEmpty(message = "鉴权code不能为空")
    private String code;
}

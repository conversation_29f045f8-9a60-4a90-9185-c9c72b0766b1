package com.coocaa.meht.module.dataimport.pojo;


import com.coocaa.meht.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/15
 * @description 点位信息
 */
@Data
public class PointDataVO {
    @Schema(description = "点位编码")
    private String code;
    @Schema(description = "点位过期时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private LocalDateTime expireTime;
    @Schema(description = "商机编码，商机编码与楼宇编码一致")
    private String businessCode;
    @Schema(description = "点位名称")
    private String buildingRatingNo;
    @Schema(description = "设备规格")
    private String deviceSize;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
}

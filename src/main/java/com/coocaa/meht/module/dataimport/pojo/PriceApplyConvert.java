package com.coocaa.meht.module.dataimport.pojo;

import cn.hutool.core.collection.CollectionUtil;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.dto.PriceApplyDeviceDto;
import com.coocaa.meht.module.web.dto.PriceApplyDto;
import com.coocaa.meht.module.web.dto.PriceApplyListCmsDto;
import com.coocaa.meht.module.web.dto.PriceApplyListDto;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 价格申请转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-28
 */
@Mapper
public interface PriceApplyConvert {
    PriceApplyConvert INSTANCE = Mappers.getMapper(PriceApplyConvert.class);

    /**
     * DTO 转换成 实体
     */
    @Mapping(target = "status", constant = "1")
    PriceApplyEntity toEntity(PriceApplyVO dto);

    /**
     * DTO 转换成 实体
     */
    PriceApplyDeviceEntity toEntity(PriceApplyDeviceDto dto);


    @Mapping(target = "applyId",source = "id")
    @Mapping(target = "statusName",expression = "java(com.coocaa.meht.module.web.entity.PriceApplyEntity.Status.getDesc(priceApplyEntity.getStatus()))")
    PriceApplyListDto toPriceApplyList(PriceApplyEntity priceApplyEntity);

    List<PriceApplyListDto> toPriceApplyList(List<PriceApplyEntity> priceApplyEntityList);

    @Mapping(target = "priceApplyId",source = "id")
    @Mapping(target = "statusName",expression = "java(com.coocaa.meht.module.web.entity.PriceApplyEntity.Status.getDesc(priceApplyEntity.getStatus()))")
    @Mapping(target = "isDeposit", expression = "java(intToBool(priceApplyEntity.getIsDeposit()))")
    @Mapping(target = "fileIds", expression = "java(strToList(priceApplyEntity.getFileIds()))")
    PriceApplyDetailDto toPriceApplyDetailDto(PriceApplyEntity priceApplyEntity);


    @Mapping(target = "applyId",source = "id")
    PriceApplyListCmsDto toCmsPriceApplyList(PriceApplyEntity priceApplyEntity);

    List<PriceApplyListCmsDto> toCmsPriceApplyList(List<PriceApplyEntity> priceApplyEntityList);

    default int boolToInt(Boolean bool) {
        return Objects.equals(bool, Boolean.TRUE) ? 1 : 0;
    }

    default boolean intToBool(Integer value) {
        return Objects.equals(value, 1);
    }

    default String listToStr(List<Integer> coll) {
        return CollectionUtil.isNotEmpty(coll) ? StringUtils.join(coll, ",") : "";
    }

    default List<Long> strToList(String str) {
        return StringUtils.isNotBlank(str)
                ? Arrays.stream(StringUtils.split(str, ","))
                .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList())
                : Collections.emptyList();
    }


}

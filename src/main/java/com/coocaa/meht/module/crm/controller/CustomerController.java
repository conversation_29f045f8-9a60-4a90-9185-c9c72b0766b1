package com.coocaa.meht.module.crm.controller;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.module.crm.dto.CrmNamePhoneDto;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpAddReq;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.dto.req.CustomerListReq;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.crm.vo.CustomerListVO;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.dto.BusinessOpportunityDto;
import com.coocaa.meht.module.web.entity.FollowRecordPicEntity;
import com.coocaa.meht.module.web.service.DataHandlerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * CRM 客户
 */
@Tag(name = "我的楼宇")
@RestController
@RequestMapping({"/crm/customer", "/customer"})
public class CustomerController {


    @Resource
    private CrmCustomerService crmCustomerService;

    @Resource
    private DataHandlerService crmDataHandlerService;

    @PostMapping("/search")
    public Result<PageResult<CustomerListVO>> list(@RequestBody CustomerListReq req) {
        return Result.ok(crmCustomerService.listCrmCustomer(req));

    }

    @GetMapping("/followup/lately")
    public Result<FollowUpVO> latelyFollowUp(@RequestParam("buildingNo") String buildingNo ,
                                             @RequestParam(value = "businessCode" , required = false) String businessCode) {

        CrmFollowUpListReq crmFollowUpListReq = new CrmFollowUpListReq();
        crmFollowUpListReq.setBuildingNo(buildingNo);
        crmFollowUpListReq.setBusinessCode(businessCode);
        List<FollowUpVO> list = crmCustomerService.listCrmFollowup(crmFollowUpListReq).getList();
        return Result.ok(CollUtil.isNotEmpty(list) ? list.get(0) : null);
    }

    @Operation(summary = "查询跟进记录")
    @PostMapping("/followup/list")
    public Result<PageResult<FollowUpVO>> listFollowUp(@RequestBody CrmFollowUpListReq req) {
        return Result.ok(crmCustomerService.listCrmFollowup(req));
    }

    /**
     * 根据id查详情
     *
     * @param id  id
     * @return  跟进详情
     */
    @GetMapping("/followup/{id}")
    public Result<FollowUpVO> detail(@PathVariable("id") Integer id) {
        CrmFollowUpListReq req = new CrmFollowUpListReq();
        req.setId(id);
        PageResult<FollowUpVO> data = crmCustomerService.listCrmFollowup(req);
        if (CollUtil.isNotEmpty(data.getList())) {
            return Result.ok(data.getList().get(0));
        }
        return Result.ok(null);
    }

    @Operation(summary = "添加修改跟进记录")
    @PostMapping("/edit/followup")
    @RepeatSubmit(interval = 5000, timeUnit = TimeUnit.MILLISECONDS, message = "跟进记录不允许重复提交")
    public Result<String> customer(@RequestBody CrmFollowUpAddReq req) {
        crmCustomerService.addOrUpdateCrmFollowUp(req);
        return Result.ok("success");
    }

    @Operation(summary = "删除跟进记录")
    @PostMapping("/delete/{id}")
    public Result<String> delete(@PathVariable Integer id) {
        crmCustomerService.deleteCrmFollowUp(id);
        return Result.ok("success");
    }

    @Operation(summary = "跟进跟进id删除跟进图片")
    @PostMapping("/delete/followup-pic/{id}")
    public Result<String> deleteFollowupPic(@PathVariable Integer id) {
        crmCustomerService.deleteFollowupPic(id);
        return Result.ok("success");
    }

    @Operation(summary = "跟进跟进id查询跟进图片")
    @PostMapping("/followup-pic/{id}")
    public Result<List<FollowRecordPicEntity>> getFollowupPic(@PathVariable Integer id) {
        List<FollowRecordPicEntity> followupPic = crmCustomerService.getFollowupPic(id);
        return Result.ok(followupPic);
    }

    /**
     * 商机场景
     */
    @Operation(summary = "查询场景")
    @GetMapping("/query-scene")
    public Result<List<CodeNameVO>> listQueryScenes() {
        List<CodeNameVO> sceneTypeEnum = crmDataHandlerService.getSceneTypeEnum();
        for (CodeNameVO codeNameVO : sceneTypeEnum) {
            codeNameVO.setName(codeNameVO.getName() + "楼宇");
        }
        return Result.ok(sceneTypeEnum);
    }

    @GetMapping("/sync/customer")
    @Anonymous
    public Result<String> syncCustomerId() {
        crmDataHandlerService.syncCrmCustomerId();
        return Result.ok("success");
    }

    @GetMapping("/fix/ownerUserId")
    @Anonymous
    public Result<String> fixOwnerUserId() {
        crmDataHandlerService.fixOwnerUserId();
        return Result.ok("success");
    }

    @PostMapping("/getCrmOwnerUserId")
    @Anonymous
    public Result<String> getCrmOwnerUserId(@RequestBody CrmNamePhoneDto dto) {
        return Result.ok(crmDataHandlerService.getCrmOwnerUserId(dto.getUserName(), dto.getPhone()));
    }

    @GetMapping("/getToken/{forceRefresh}")
    @Anonymous
    public Result<String> getToken(@PathVariable(name = "forceRefresh") boolean forceRefresh) {
        return Result.ok(crmDataHandlerService.getToken(forceRefresh));
    }

    @Operation(summary = "根据楼栋号查询商机信息")
    @GetMapping("/customer/business/{buildingNo}")
    public Result<List<BusinessOpportunityDto>> getCustomerBusiness(@PathVariable(name = "buildingNo") String buildingNo) {
        return Result.ok(crmDataHandlerService.getCustomerBusiness(buildingNo));
    }


    /**
     * 搜索客户
     */
    @Operation(summary = "搜索客户")
    @PostMapping("/list")
    public Result<PageResult<CustomerListVO>> customerList(@RequestBody CustomerListReq req) {
        return Result.ok(crmCustomerService.customerList(req));

    }


}

package com.coocaa.meht.module.crm.enums;

import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/10
 * @description 场景枚举
 */
@Getter
@AllArgsConstructor
public enum SceneTypeEnum {
    ALL_CUSTOMERS("全部"),
    MY_CUSTOMERS("我负责的"),
    SUBORDINATE_CUSTOMERS("下属负责的");


    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据编码获取描述
     */
    public static List<CodeNameVO> getNameList() {
        List<CodeNameVO> codeNameVOS = new ArrayList<>();
        for (SceneTypeEnum value : SceneTypeEnum.values()) {
            CodeNameVO codeNameVO = new CodeNameVO();
            codeNameVO.setCode(value.name());
            codeNameVO.setName(value.getDesc());
            codeNameVOS.add(codeNameVO);
        }
        return codeNameVOS;
    }


}

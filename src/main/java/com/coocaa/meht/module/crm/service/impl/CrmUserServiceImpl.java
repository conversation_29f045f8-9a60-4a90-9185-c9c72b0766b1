package com.coocaa.meht.module.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.module.crm.dto.CrmUserDto;
import com.coocaa.meht.module.crm.service.CrmUserService;
import com.coocaa.meht.module.web.dto.crm.CrmResult2Dto;
import com.coocaa.meht.module.web.dto.crm.CrmResult3Dto;
import com.coocaa.meht.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.coocaa.meht.common.CommonConstants.CRM_SUCCESS_STATUS_CODE;

@Service
@Slf4j
public class CrmUserServiceImpl extends CrmBaseService implements CrmUserService{

    private static final String LOGIN_USER_INFO_URL = "/adminUser/queryLoginUser";

    private static final String QUERY_CHILD_USER_ID = "/adminUser/queryChildUserId";

    @Override
    public CrmUserDto getCrmUserInfo() {
        CrmResult3Dto crmResult3Dto = callCrmApi(LOGIN_USER_INFO_URL, null, CrmResult3Dto.class);
        if (!Objects.equals(crmResult3Dto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            log.warn("[getCrmUserInfo][调用crm接口获取当前用户详情,result:{}]",JsonUtils.toJson(crmResult3Dto));
            return null;
        }
        return JsonUtils.fromJson(JsonUtils.toJson(crmResult3Dto.getData()), CrmUserDto.class);
    }

    @Override
    public List<CrmUserDto> listLowerLevelUserInfo(CrmUserDto crmUserDto) {


        return null;
    }

    @Override
    public List<String> listChildUserId(String userId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", userId);

        CrmResult2Dto crmResult2Dto = callGetCrmApi(QUERY_CHILD_USER_ID, map, CrmResult2Dto.class);
        if (!Objects.equals(crmResult2Dto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            log.warn("[listChildUserId][调用crm接口获取下级用户失败,userId:{},result:{}]",userId,JsonUtils.toJson(crmResult2Dto));
            return new ArrayList<>();
        }

        List<Object> data = crmResult2Dto.getData();
        if (CollUtil.isEmpty(data)) {
            return new ArrayList<>();
        }

        return data.stream().map(o -> (String) o).collect(Collectors.toList());
    }
}

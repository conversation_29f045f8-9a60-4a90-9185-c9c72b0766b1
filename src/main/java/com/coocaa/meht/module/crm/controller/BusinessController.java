package com.coocaa.meht.module.crm.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.crm.dto.CmsBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmBusinessFlowDto;
import com.coocaa.meht.module.crm.dto.req.CmsBusinessReq;
import com.coocaa.meht.module.crm.dto.req.CrmBusinessReq;
import com.coocaa.meht.module.crm.service.CrmBusinessService;
import com.coocaa.meht.module.crm.vo.BusinessVO;
import com.coocaa.meht.module.web.dto.req.BusinessReq;
import com.coocaa.meht.module.web.service.DataHandlerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * CRM商机
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26
 */
@RestController
@RequestMapping({"/crm/business", "/business"})
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Tag(name = "商机管理")
public class BusinessController {
    private final CrmBusinessService crmBusinessService;
    private final DataHandlerService crmDataHandlerService;

    /**
     * 商机列表
     */
    @PostMapping("/search")
    @Operation(summary = "商机列表")
    public Result<PageResult<BusinessVO>> list(@RequestBody CrmBusinessReq req) {
        return Result.ok(crmBusinessService.list(req));
    }

    /**
     * 商机详情
     */
    @GetMapping("/{businessCode}")
    @Operation(summary = "商机详情")
    public Result<BusinessVO> getDetail(@PathVariable(name = "businessCode") String businessCode) {
        return crmBusinessService.getDetail(businessCode);
    }

    /**
     * 添加商机
     */
    @Operation(summary = "添加商机")
    @PostMapping("/add/business")
    public Result<Boolean> addBusiness(@RequestBody BusinessReq businessReq) {
        return crmBusinessService.addBusiness(businessReq);
    }

    /**
     * 完善商机
     */
    @Operation(summary = "完善商机")
    @PostMapping("/update/business")
    public Result<Boolean> updateBusiness(@RequestBody BusinessReq businessReq) {
        return crmBusinessService.updateBusiness(businessReq);
    }


    /**
     * 商机场景
     */
    @GetMapping("/query-scene")
    public Result<List<CodeNameVO>> listQueryScenes() {
        List<CodeNameVO> sceneTypeEnum = crmDataHandlerService.getSceneTypeEnum();
        for (CodeNameVO codeNameVO : sceneTypeEnum) {
            codeNameVO.setName(codeNameVO.getName() + "商机");
        }

        return Result.ok(sceneTypeEnum);
    }

    @PostMapping("/cms/list")
    @Anonymous
    public Result<PageResult<CmsBusinessDto>> searchBusiness(@RequestBody CmsBusinessReq cmsBusinessReq) {
        PageResult<CmsBusinessDto> pageResult = crmBusinessService.listCmsBusinessByPhone(cmsBusinessReq);
        return Result.ok(pageResult);
    }


    /**
     * 客户商机状态校验
     */
    @GetMapping("/check/business/status/{buildingNo}")
    public Result<Boolean> checkBusinessStatus(@PathVariable("buildingNo") String buildingNo) {
        return crmBusinessService.checkBusinessStatus(buildingNo);
    }


}

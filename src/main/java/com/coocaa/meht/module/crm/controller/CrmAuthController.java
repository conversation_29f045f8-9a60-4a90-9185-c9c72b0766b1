package com.coocaa.meht.module.crm.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.crm.service.CrmProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * CRM认证
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26
 */
@Slf4j
@RestController
@RequestMapping("/crm/auth")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CrmAuthController {
    private final CrmProxyService proxyService;
    private final StringRedisTemplate stringRedisTemplate;


    /**
     * 飞书登录CRM
     */
    @Anonymous
    @PostMapping(path = "/login", produces = {"application/json;charset=UTF-8"})
    public String crmLogin(@RequestBody String loginParams) {
        return proxyService.proxyPost("/login", loginParams);
    }

    /**
     * 用户名密码登陆
     */
    @Anonymous
    @GetMapping(path = "/token")
    public Result<String> getCrmToken() {
        return Result.ok(proxyService.getCrmToken());
    }

    /**
     * 退出CRM登陆
     */
    @Anonymous
    @PostMapping(path = "/{mobile}")
    public Result<Boolean> crmLogout(@PathVariable("mobile") String mobile) {
        log.info("强制退出CRM, 用户:{}", mobile);
        return Result.ok(stringRedisTemplate.delete("meht:crm:user:token:" + mobile));
    }

}

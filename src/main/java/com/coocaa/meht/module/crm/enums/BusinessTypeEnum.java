package com.coocaa.meht.module.crm.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    SELF(0, "自营"),
    PROXY(1, "代理");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取描述
     *
     * @return
     */
    public static String getDesc(Integer code) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.getCode().equals(code)) {
                return businessTypeEnum.getDesc();
            }
        }
        return null;
    }

}

package com.coocaa.meht.module.crm.service;

import com.coocaa.meht.module.crm.dto.CrmUserDto;

import java.util.List;

public interface CrmUserService {

    /**
     * 获取crm用户信息
     * @return
     */
    CrmUserDto getCrmUserInfo();

    /**
     * 获取下级用户信息
     * @return
     */
    List<CrmUserDto> listLowerLevelUserInfo(CrmUserDto crmUserDto);

    /**
     * 获取当前用户的下属用户id
     * @param userId
     * @return
     */
    List<String> listChildUserId(String userId);

}

package com.coocaa.meht.module.crm.enums;

import com.coocaa.meht.common.bean.CodeNameVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 转移标识枚举
 */
@Getter
@AllArgsConstructor
public enum TransferFlagEnum {
    BATCH_CUSTOMERS("批量转移客户"),
    SINGLE_CUSTOMERS("指定转移客户");


    /**
     * 状态描述
     */
    private final String desc;


    /**
     * 根据编码获取描述
     */
    public static List<CodeNameVO> getNameList() {
        List<CodeNameVO> codeNameVOS = new ArrayList<>();
        for (TransferFlagEnum value : TransferFlagEnum.values()) {
            CodeNameVO codeNameVO = new CodeNameVO();
            codeNameVO.setCode(value.name());
            codeNameVO.setName(value.getDesc());
            codeNameVOS.add(codeNameVO);
        }
        return codeNameVOS;
    }
}

package com.coocaa.meht.module.crm.dto;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CmsBusinessDto {


    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 商机编码
     */
    private String businessCode;
    /**
     * 商机名称
     */
    private String businessName;

    /**
     * 商机名称
     */
    private String buildingName;

    /**
     * 楼宇类型
     */
    private Integer buildingType;

    /**
     * 楼宇类型名
     */
    private String buildingTypeName;


    /**
     * 等级评价
     */
    private String projectLevel;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区名称
     */
    private String region;

    /**
     * 楼宇地址 省市区
     */
    private String address;

    /**
     * 等级ai评价
     */
    private String projectAiLevel;

    /**
     * 评级版本
     */
    private String ratingVersion;

    /**
     * 地理位置名称
     */
    private String locationName;

}

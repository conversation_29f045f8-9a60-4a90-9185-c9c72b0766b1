package com.coocaa.meht.module.crm.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/10
 * @description 客户列表VO
 */
@Data
public class CustomerListVO {
    /**
     * 楼宇id
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 楼宇名称
     */
    private String customerName;

    /**
     * 详细地址
     */
    private String fieldCakork;


    /**
     * 楼宇评级等级
     */
    private String level;

    /**
     * 楼宇编号
     */
    private String fieldWpgtbg;

    /**
     * 负责人
     */
    private String createUserName;

    /**
     * 楼宇类型名称
     */
    private String buildingTypeName;

    /**
     * 楼层数
     */
    private Long buildingNumber;

    /**
     * 月租金
     */
    private Long buildingPrice;

    /**
     * 楼号
     */
    private String buildingNo;
    /**
     * 负责人id
     */
    private String ownerUserId;

    /**
     * AI评级
     */
    private String projectAiLevel;
    /**
     * AI得分
     */
    private BigDecimal buildingAiScore;
    /**
     * 竞媒点位数量
     */
    private BigDecimal competitorPointCount;
    /**
     * 目标点位数量
     */
    private BigDecimal targetPointCount;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 是否可以完善评级标识：true-可以, false-不可以
     */
    private boolean improveRatingFlag;

    /**
     * 楼宇状态：0-待审核，1-已审核，2-已驳回
     */
    private Integer status;

    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    private Integer buildingType;


    /**
     * 掉入公海时间
     */
    private LocalDateTime enterSeaTime;

    /**
     * 城市
     */
    private String city;

    /**
     * 评级版本
     */
    private String ratingVersion;

    /**
     * 完善评级编号
     */
    private String completeRatingNo;

}

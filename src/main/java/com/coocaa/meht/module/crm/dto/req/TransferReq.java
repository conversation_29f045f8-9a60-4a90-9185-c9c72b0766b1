package com.coocaa.meht.module.crm.dto.req;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 客户转移请求参数
 * @since 1.0
 */
@Data
public class TransferReq {
    @Schema(description = "转移标识", example = "BATCH_CUSTOMERS(批量转移客户),SINGLE_CUSTOMERS(指定转移客户)")
    @NotBlank(message = "转移标识不能为空")
    private String transferFlag;

    @Schema(description = "楼栋编码")
    private String buildingNo;

    @Schema(description = "新负责人工号")
    @NotBlank(message = "转移用户工号不能为空")
    private String userCode;

    @Schema(description = "原负责人工号")
    private String transferUserCode;
}

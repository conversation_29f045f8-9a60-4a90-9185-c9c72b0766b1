package com.coocaa.meht.module.crm.service;

import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpAddReq;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.dto.req.CustomerListReq;
import com.coocaa.meht.module.crm.vo.CustomerListVO;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.FollowRecordPicEntity;

import java.util.List;
import java.util.Map;

public interface CrmCustomerService {

    /**
     * 通过楼宇名称查询 (客户)
     * @param req
     * @return
     */
    PageResult<CustomerListVO> listCrmCustomer(CustomerListReq req);


    /**
     * 查询更进记录
     * @param req
     * @return
     */
    PageResult<FollowUpVO> listCrmFollowup(CrmFollowUpListReq req);

    /**
     * 添加更进记录
     * @param req
     */
    void addOrUpdateCrmFollowUp(CrmFollowUpAddReq req);

    /**
     * 删除跟进记录
     * @param id 跟进记录id
     */
    void deleteCrmFollowUp(Integer id);

    /**
     * 查询商机查询场景列表
     *
     * @return 场景列表
     */
    Result<List<KeyValue<String, String>>> listQueryScenes();

    /**
     * 保存商机变更记录
     */
    void addBusinessChangeLog(Integer businessId, String BusinessCode, String status);

    /**
     * 删除跟进记录图片
     * @param id 跟进记录图片id
     */
    void deleteFollowupPic(Integer id);

    /**
     * 查询跟进记录图片
     * @param id 跟进记录id
     * @return
     */
    List<FollowRecordPicEntity> getFollowupPic(Integer id);

    /**
     * 是否完善评级标识
     * @return true:是 false:否
     */
    boolean isImproveRatingFlag(BuildingRatingEntity record, Map<String, String> waitAuditCompletemap);

    /**
     * 客户列表
     */
    PageResult<CustomerListVO> customerList(CustomerListReq req);

}

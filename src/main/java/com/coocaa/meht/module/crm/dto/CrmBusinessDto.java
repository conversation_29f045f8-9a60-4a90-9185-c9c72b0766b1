package com.coocaa.meht.module.crm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
@Data
@Accessors(chain = true)
public class CrmBusinessDto {
    /**
     * 商机ID
     */
    private String businessId;

    /**
     * 商机编码
     */
    private String businessCode;
    /**
     * 本地库商机状态
     */
    private String businessStatus;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 楼宇名称
     */
    private String name;


    /**
     * 商机名称
     */
    private String businessName;

    /**
     * 楼宇地址
     */
    private String address;

    /**
     * 城市地区
     */
    private String area;

    /**
     * 楼宇类型
     */
    private String type;

    /**
     * 楼宇评级
     */
    private String level;

    /**
     * 楼层数
     */
    private String floorCount;

    /**
     * 月租金
     */
    private String rent;

    /**
     * 商机随机编码
     */
    private String batchId;

    /**
     * 商机组类型ID
     */
    private String typeId;

    /**
     * 负责人
     */
    @Schema(description = "负责人")
    private String owner;

    @Schema(description = "目标点位数量")
    private Integer targetPointCount;

    @Schema(description = "竞媒点位数量")
    private Integer competitorPointCount;

    /**
     * 状态
     */
    private String status;

    private Integer pointPlanId;

    @Schema(description = "点位方案状态")
    private String pointPlanStatus;

    private Integer pricePointPlanId;

    private Integer contractPointPlanId;

    /**
     * 创建人
     */
    private String createBy;
}

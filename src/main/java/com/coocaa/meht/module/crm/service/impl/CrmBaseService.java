package com.coocaa.meht.module.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.coocaa.meht.common.CommonConstants;
import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.dto.crm.CrmFieldResultDto;
import com.coocaa.meht.utils.AESECBUtil;
import com.coocaa.meht.utils.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;


/**
 * 封装CRM接口调用
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
@Slf4j
@Component
public class CrmBaseService {
    @Value("${crm.admin.host:https://beta-crm.coocaa.com/api}")
    protected String baseUrl;

    @Value("${crm.user.password:iM+hgLSVyRd1e5/qwvKa/Q==}")
    private String userPwd;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 调用CRM接口
     *
     * @param apiUrl 调用地址
     * @param query  查询参数
     * @return CRM 返回结果
     */
    protected <R, Q> R callCrmApi(String apiUrl, Q query, Class<R> clazz) {
        String result = callCrmApi(apiUrl, query);
        return JsonUtils.fromJson(result, clazz);
    }

    /**
     * 调用CRM接口
     *
     * @param apiUrl 调用地址
     * @return CRM 返回结果
     */
    protected String callCrmApi(String apiUrl) {
        return callCrmApi(apiUrl, null);
    }

    /**
     * 调用CRM接口 get
     *
     * @param apiUrl 调用地址
     * @return CRM 返回结果
     */
    protected <R> R callGetCrmApi(String apiUrl, Map<String, Object> query, Class<R> clazz) {
        String url = baseUrl + apiUrl;
        HttpRequest request = HttpRequest.get(url).timeout((int) TimeUnit.SECONDS.toMillis(30));

        try {
            // 设置CRM token
            String crmToken = getCrmToken();
            if (StringUtils.isNotBlank(crmToken)) {
                request.header("admin-token", crmToken);
            }
        } catch (Exception ex) {
            log.info("未获取到CRM授权令牌", ex);
        }

        // Post Body
        Optional.ofNullable(query).ifPresent(q -> {
            request.form(query);
        });
        String result = request.execute().body();
        log.info("调用CRM接口: {} \n\t参数: {} \n\t返回: {}", url, JsonUtils.toJson(query), StringUtils.substring(result, 0, 100));
        return JsonUtils.fromJson(result, clazz);
    }

    /**
     * 调用CRM接口
     *
     * @param apiUrl 调用地址
     * @param query  查询参数
     * @return CRM 返回结果
     */
    public <Q> String callCrmApi(String apiUrl, Q query) {
        String url = baseUrl + apiUrl;
        HttpRequest request = HttpRequest.post(url).timeout((int) TimeUnit.SECONDS.toMillis(30));

        try {
            // 设置CRM token
            String crmToken = getCrmToken();
            if (StringUtils.isNotBlank(crmToken)) {
                request.header("admin-token", crmToken);
            }
        } catch (Exception ex) {
            log.info("未获取到CRM授权令牌", ex);
        }

        // Post Body
        Optional.ofNullable(query).ifPresent(q -> {
            if (query instanceof Map) {
                request.form((Map) query);
            } else if (query instanceof String) {
                request.body((String) query);
            } else {
                request.body(JsonUtils.toJson(query));
            }
        });
        String result = request.execute().body();
        log.info("调用CRM接口: {} \n\t参数: {} \n\t返回: {}", url, JsonUtils.toJson(query), StringUtils.substring(result, 0, 100));
        return result;
    }

    /**
     * 获取CRM授权令牌
     */
    protected String getCrmToken() {
        String cacheKey = getCrmUserTokenCacheKey();
        String crmToken = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(crmToken)) {
            log.info("从缓存中拿到用户({})的CRM授权令牌:{}", SecurityUser.getUser().getPhone(), crmToken);
            return crmToken;
        }

        try {
            // 生成密码
            RSA rsa = SecureUtil.rsa(CommonConstants.CRM_LOGIN_PASSWORD_PRIVATE_KEY, CommonConstants.CRM_LOGIN_PASSWORD_PUBLIC_KEY);
            String passwordEncrypt = rsa.encryptBase64(AESECBUtil.decryptStr(userPwd) + System.currentTimeMillis(), KeyType.PublicKey);

            // 生成登陆参数，type=2为移动端，Token过期时间较长
            HashMap<String, Object> loginParam = new HashMap<>();
            loginParam.put("username", SecurityUser.getUser().getPhone());
            loginParam.put("password", passwordEncrypt);
            loginParam.put("type", "2");

            // 调用CRM接口登陆
            String loginResult = HttpRequest.post(baseUrl + "/login")
                    .timeout((int) TimeUnit.SECONDS.toMillis(30))
                    .body(JsonUtils.toJson(loginParam))
                    .execute().body();

            // 解析Token
            JSONObject result = parseToJson(loginResult);
            crmToken = result.getStr("adminToken");
            if (StringUtils.isNotBlank(crmToken)) {
                stringRedisTemplate.opsForValue().set(cacheKey, crmToken, 1, TimeUnit.HOURS);
                log.info("通过密码登陆拿到用户({})的CRM授权令牌:{}", SecurityUser.getUser().getPhone(), crmToken);
                return crmToken;
            }
        } catch (Exception ex) {
            log.error("获取CRM授权令牌失败", ex);
        }

        return null;
    }


    /**
     * 解析成JSON对象
     *
     * @param json CRM调用返回结果
     * @return JSON对象
     */
    protected JSONObject parseToJson(String json) {
        return (JSONObject) parse(json);
    }

    /**
     * 解析成JSON数组
     *
     * @param json CRM调用返回结果
     * @return JSON对象
     */
    protected JSONArray parseToJsonArray(String json) {
        return (JSONArray) parse(json);
    }


    /**
     * 判断是否成功
     *
     * @param json CRM调用返回结果
     * @return 是否成功
     */
    protected boolean isSuccess(JSON json) {
        if (Objects.nonNull(json) && Objects.equals(json.getByPath("code", Integer.class), CommonConstants.CRM_SUCCESS_STATUS_CODE)) {
            return true;
        }
        throw new ServerException("CRM接口调用失败");
    }

    /**
     * 判断是否成功
     *
     * @param json CRM调用返回结果
     * @return 是否成功
     */
    protected boolean isSuccess(String json) {
        if (StringUtils.isNotBlank(json) && isSuccess(JSONUtil.parse(json))) {
            return true;
        }
        throw new ServerException("CRM接口调用失败");
    }

    /**
     * 根据类型查询场景列表
     *
     * @param type 类型
     * @return 场景列表
     */
    protected List<KeyValue<String, String>> listQueryScenes(String type, Set<String> includeNamePrefixSet) {
        String url = "/crmScene/queryScene?type=" + type;
        String crmRsp = callCrmApi(url);
        if (StringUtils.isBlank(crmRsp)) {
            return Collections.emptyList();
        }

        JSONArray jsonArray = parseToJsonArray(crmRsp);
        if (Objects.isNull(jsonArray) || jsonArray.isEmpty()) {
            return Collections.emptyList();
        }

        // 需要过滤数据
        boolean filter = CollectionUtil.isNotEmpty(includeNamePrefixSet);

        // 解析数据
        List<KeyValue<String, String>> scenes = Lists.newArrayListWithExpectedSize(jsonArray.size());
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String name = jsonObject.getStr("name");
            if (filter && includeNamePrefixSet.stream().noneMatch(prefix -> StringUtils.containsIgnoreCase(name, prefix))) {
                continue;
            }
            scenes.add(new KeyValue<>(jsonObject.getStr("sceneId"), name));
        }

        return scenes;
    }


    /**
     * 查询字段
     *
     * @param label
     * @return
     */
    protected CrmFieldResultDto getField(Integer label) {
        String crmCustomerKey = "meht:crm:query_field:" + label;
        String cache = stringRedisTemplate.opsForValue().get(crmCustomerKey);
        if (StringUtils.isNotBlank(cache)) {
            return JsonUtils.fromJson(cache, CrmFieldResultDto.class);
        }

        String result = callCrmApi("/crmScene/queryField?label=" + label);
        CrmFieldResultDto crmFieldResultDto = JsonUtils.fromJson(result, CrmFieldResultDto.class);

        if (crmFieldResultDto.getCode() == CommonConstants.CRM_NOT_LOGIN_STATUS_CODE) {
            log.info("[getField][请求悟空查询Field,结果:{},请求参数:{}]", result, label);
            if (!Objects.equals(crmFieldResultDto.getCode(), CommonConstants.CRM_SUCCESS_STATUS_CODE)) {
                throw new ServerException("获取crm字段失败");
            }
            stringRedisTemplate.opsForValue().set(crmCustomerKey, result, 10 * 60, TimeUnit.SECONDS);
        }
        return crmFieldResultDto;
    }


    /**
     * 解析成JSON对象
     *
     * @param json CRM调用返回结果
     * @return JSON对象
     */
    private Object parse(String json) {
        String exceptionMessage = "CRM接口调用失败";
        if (StringUtils.isBlank(json)) {
            throw new ServerException(exceptionMessage);
        }

        // 解析json, 并判断是否成功
        JSON parsedJson = Optional.ofNullable(JSONUtil.parse(json)).orElseThrow(() -> new ServerException(exceptionMessage));
        Integer respCode = parsedJson.getByPath("code", Integer.class);

        // 如果Token过期了, 需要重新登陆
        if (Objects.equals(respCode, CommonConstants.CRM_NOT_LOGIN_STATUS_CODE)) {
            stringRedisTemplate.delete(getCrmUserTokenCacheKey());
        } else if (Objects.equals(respCode, CommonConstants.CRM_SUCCESS_STATUS_CODE)) {
            return Optional.ofNullable(parsedJson.getByPath("data"))
                    .orElseThrow(() -> new ServerException(exceptionMessage));
        } else {
            String respMsg = parsedJson.getByPath("msg", String.class);
            throw new ServerException(respMsg);
        }
        throw new ServerException(exceptionMessage);
    }

    /**
     * 生成用户Token的缓存Key
     */
    private String getCrmUserTokenCacheKey() {
        return "meht:crm:user:token:" + SecurityUser.getUser().getPhone();
    }
}

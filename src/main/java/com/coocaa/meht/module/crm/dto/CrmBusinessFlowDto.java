package com.coocaa.meht.module.crm.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商机流转信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26
 */
@Data
@Accessors(chain = true)
public class CrmBusinessFlowDto {
    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 当前状态
     */
    private Boolean current;

    /**
     * 最终状态 [1:成交, 2:解约, 3:无效]
     */
    private String finalStatus;

    /**
     * 结束原因
     */
    private String remark;
}

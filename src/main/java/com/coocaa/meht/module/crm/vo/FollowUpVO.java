package com.coocaa.meht.module.crm.vo;


import com.coocaa.meht.module.web.entity.FollowRecordPicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/11
 * @description 跟进
 */
@Data
public class FollowUpVO {
    private int id;

    /**
     * 商机名称
     */
    @Schema(description = "商机名称")
    private String businessName;
    /**
     * 商机名称
     */
    @Schema(description = "商机code")
    private String businessCode;

    /**
     * 跟进对象
     */
    private String followTarget;

    /**
     * 跟进方式 面访|电话
     */
    private String followMethod;

    /**
     * 跟进方式 面访|电话
     */
    private String category;

    /**
     * 沟通时间
     */
    private String followUpTime;

    /**
     * 沟通目的
     */
    private String followUpGoal;

    /**
     * 沟通结果
     */
    private String followUpResult;

    /**
     * 楼栋号
     */
    private String buildingNo;
    /**
     * 批次号
     */
    private String batchId;

    /**
     * 创建人
     */
    private String createBy;


    @Schema(description = "客户跟进记录图片")
    private List<FollowRecordPicEntity> customerFollowRecordPicList;

}

package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.web.dto.point.WaitingHallDTO;
import com.coocaa.meht.module.web.dto.point.WaitingHallDetail;
import com.coocaa.meht.module.web.entity.WaitingHallEntity;
import com.coocaa.meht.rpc.dto.WaitingHallAddParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;

/**
 * <AUTHOR>
 * @since 2024/12/12
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface WaitingHallConvert {
    @Mapping(source = "waitingHall",target = "waitingHallName")
    WaitingHallEntity toWaitingHallEntity(WaitingHallDTO waitingHallDTO);

    WaitingHallDetail toWaitingHallDetail(WaitingHallEntity entity);

    @Mapping(source = "waitingHall",target = "waitingHallName")
    WaitingHallAddParam toWaitingHallAddParam(WaitingHallDTO waitingHallDTO);
}

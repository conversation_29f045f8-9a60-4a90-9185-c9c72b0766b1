package com.coocaa.meht.module.web.dto.point;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @program: cheese-meht-web-api
 * @ClassName WaitingHallVO
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-16 17:31
 * @Version 1.0
 * todo 后期删除
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WaitingHallVO {
    private Integer id;
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    private String floor;
    private String waitingHallName;
    private String waitingHallType;
    private String createBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTime;
    private String updateBy;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime updateTime;
    private Integer pointPlanId;
    private String floorDesc;
}

package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.PointPicEntity;

import java.util.List;

public interface PointPicService extends IService<PointPicEntity> {
    void removeByPointId(Integer pointId);
    void removeByPointIds(List<Integer> pointIds);

    List<PointPicEntity> listByPointIds(List<Integer> pointIds);


}
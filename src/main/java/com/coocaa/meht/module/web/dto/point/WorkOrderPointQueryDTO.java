package com.coocaa.meht.module.web.dto.point;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 点位工单
 */
@Data
public class WorkOrderPointQueryDTO {
    @Schema(description = "主键", type = "Integer", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "工单id", type = "Integer", example = "GD250110002")
    private Integer orderId;

    @Schema(description = "工单编号", type = "String", example = "GD0001")
    private String orderCode;

    @Schema(description = "点位编码", type = "String", example = "P001")
    private String pointCode;

    @Schema(description = "点位名称(楼栋单元楼层等候厅组合)", type = "String", example = "A座1单元2层等候厅")
    private String pointName;

    @Schema(description = "楼栋编号", type = "String", example = "A")
    private String buildingNo;

    @Schema(description = "楼栋名称", type = "String", example = "A座")
    private String buildingName;

    @Schema(description = "单元名称", type = "String", example = "1单元")
    private String unitName;

    @Schema(description = "楼层名称 对应字典0004", type = "String", example = "2层")
    @Convert(type = ConvertType.DICT, targetFieldName = "floorName")
    private String floor;

    @Schema(description = "楼层名称", type = "String", example = "2层")
    private String floorName;

    @Schema(description = "等候厅名称", type = "String", example = "等候厅A")
    private String waitingHallName;

    @Schema(description = "等候厅类型，字典", type = "String", example = "0001")
    @Convert(type = ConvertType.DICT, targetFieldName = "waitingHallTypeName")
    private String waitingHallType;

    @Schema(description = "等候厅类型", type = "String", example = "等后厅1")
    private String waitingHallTypeName;

    @Schema(description = "设备尺寸", type = "String", example = "40寸")
    @Convert(type = ConvertType.DICT, targetFieldName = "equipmentSizeName")
    private String equipmentSize;

    @Schema(description = "设备尺寸", type = "String", example = "40寸")
    private String equipmentSizeName;

}

package com.coocaa.meht.module.web.service;

import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.module.web.dto.CustomerTransferIntoDTO;
import com.coocaa.meht.module.web.dto.CustomerTransferOutDTO;
import com.coocaa.meht.module.web.dto.HighSeaCustomerDTO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.vo.HighSeaCustomerVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
public interface HighSeaCustomerService {

    PageResult<HighSeaCustomerVO> list(HighSeaCustomerDTO param);

    void transferInto(CustomerTransferIntoDTO param);

    void transferOut(CustomerTransferOutDTO param);

    List<String> getCities();

    void sendMessage(String msg, List<BuildingRatingEntity> entities);

    void refreshEnterSeaTime(Collection<String> buildingNos);

    List<String> responsibilityCheck(String userWno, List<String> businessCodes);

    /**
     * 清空入公海时间
     * @param buildingNo
     */
    void emptyEnterSeaTime(String buildingNo);

}

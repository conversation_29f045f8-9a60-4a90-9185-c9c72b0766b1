package com.coocaa.meht.module.web.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 楼宇基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
public class BuildingMetaDetailDto {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 楼宇编码BC打头
     */
    private String buildingMetaNo;

    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中  3已认证
     */
    private Integer buildingStatus;

    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    private Integer buildingType;

    /**
     * ai楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    private Integer buildingTypeAi;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 地图楼宇名称-ai
     */
    private String buildingNameAi;

    /**
     * 地图楼宇编码
     */
    private String mapNo;

    /**
     * 省名称
     */
    private String mapProvince;

    /**
     * 市名称
     */
    private String mapCity;

    /**
     * 区名称
     */
    private String mapRegion;

    /**
     * 详细地址
     */
    private String mapAddress;

    /**
     * 纬度
     */
    private String mapLatitude;

    /**
     * 经度
     */
    private String mapLongitude;

    /**
     * 行政区域编码
     */
    private String mapAdCode;

    /**
     * 认证生效开始日期
     */
    private Date authenticationStart;

    /**
     * 认证生效结束日期
     */
    private Date authenticationEnd;

    /**
     * 认证有效期限
     */
    private Long authenticationPeriod;

    /**
     * 描述
     */
    private String buildingDesc;

    /**
     * 综合得分
     */
    private BigDecimal buildingScore;

    /**
     * 等级评价
     */
    private String projectLevel;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建方式字典,人工提交，系统爬取
     */
    private String createType;

    /**
     * 最后一个成功认证的no
     */
    private String buildingRatingNo;

    /**
     * 审核通过的时间
     */
    private Date successTime;

    /**
     * 目标点位数量
     */
    private Integer targetPointCount;

    /**
     * 竞媒点位数量
     */
    private Integer competitorPointCount;

    /**
     * 负责人
     */
    private String manager;
    /**
     * 负责人 (工号)
     */
    private String managerName;

    /**
     * ai的等级评价
     * */
    private String projectLevelAi;

    /**
     * 创世点位数量
     * */
    private Integer csPointCount;

    /**
     * ai 等级评分
     * */
    private BigDecimal buildingAiScore;

    /**
     * 最高楼层
     */
    @Schema(description = "最高楼层", type = "Int", example = "1")
    private Integer floorTotalNumber;

    /**
     * 总楼栋数量
     */
    @Schema(description = "总楼栋数量", type = "Int", example = "1")
    private Integer buildingTotalNumber;

    /**
     * 总单元数量
     */
    @Schema(description = "总单元数量", type = "Int", example = "1")
    private Integer unitsTotalNumber;

    /**
     * 总等候厅数
     */
    @Schema(description = "总等候厅数", type = "Int", example = "1")
    private Integer hallTotalNumber;

    /**
     * 禁忌行业
     */
    @Schema(description = "禁忌行业code", type = "String", example = "1")
    private List<String> forbiddenIndustry;

    @Schema(description = "禁忌行业名称", type = "String", example = "1")
    private String forbiddenIndustryName;

    /**
     * 物业信息
     */
    @Schema(description = "物业信息")
    List<BuildingPropertyCompanyVO> propertyCompanyVOS;


    /**
     * 外墙材料附件地址
     */
    private List<String> buildingExteriorPic;
    /**
     * 大堂高度及装饰附件地址
     */
    private List<String> buildingLobbyPic;
    /**
     * 楼梯厅装饰附件地址
     */
    private List<String> buildingHallPic;


    /**
     * 梯厅环境图附件地址
     */
    @Schema(description = "梯厅环境图附件地址")
    private List<String> buildingElevatorPic;

    /**
     * 闸口图附件地址
     */
    @Schema(description = "闸口图附件地址")
    private List<String> buildingGatePic;

    /**
     * 安装示意图附件地址
     */
    @Schema(description = "安装示意图附件地址")
    private List<String> buildingInstallationPic;


    @Schema(description = "楼宇基因信息")
    private BuildingGeneVO buildingGene;

    @Schema(description = "是否大屏")
    private Boolean largeScreen;

    @Schema(description = "楼宇评级id")
    private Long buildingRatingId;

    @Schema(description = "楼宇评级状态")
    private Integer status;

    @Schema(description = "是否可以完善评级标识：true-可以, false-不可以")
    private boolean improveRatingFlag;

    @Schema(description = "楼宇编号")
    private String buildingNo;

    @Schema(description = "完善编号")
    private String completeRatingNo;

    @Schema(description = "评级版本号")
    private String ratingVersion;

}

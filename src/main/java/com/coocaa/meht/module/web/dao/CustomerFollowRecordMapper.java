package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 客户跟进
 */
@Mapper
public interface CustomerFollowRecordMapper extends BaseMapper<CustomerFollowRecordEntity> {

    Page<FollowUpVO> getCustomerFollowRecord(Page page, @Param("buildingNo") String buildingNo, @Param("id") Integer id, @Param("businessCode") String businessCode);
}

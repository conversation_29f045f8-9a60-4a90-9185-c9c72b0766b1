package com.coocaa.meht.module.web.vo.proxy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @file CsBrandPointDataVO
 * @date 2025/1/21 10:13
 * @description 创视 - BrandPointDataVO包装对象
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CsBrandPointDataWrapperVO {
    /**
     * 本品点位列表
     */
    @Schema(description = "本品点位列表")
    private List<CsBrandPointDataVO> selfBrandPoints;

    /**
     * 竞品点位列表
     */
    @Schema(description = "竞品点位列表")
    private List<CsBrandPointDataVO> competitiveBrandPoints;
}

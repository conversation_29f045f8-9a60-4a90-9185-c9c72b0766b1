package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.building.vo.RatingVO;
import com.coocaa.meht.module.crm.dto.CmsBusinessDto;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.utils.RsaExample;
import org.mapstruct.*;
import org.mapstruct.control.DeepClone;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public abstract class BuildingRatingConvert {

    @Autowired
    protected RsaExample rsaExample;

    @Mapping(target = "province", source = "mapProvince")
    @Mapping(target = "city", source = "mapCity")
    @Mapping(target = "region", source = "mapRegion")
    @Mapping(target = "address", expression = "java(rsaExample.decryptByPrivate(buildingRatingEntity.getMapAddress()))")
    public abstract CmsBusinessDto convertToCmsBusinessDto(BuildingRatingEntity buildingRatingEntity);

    public abstract List<CmsBusinessDto> convertToCmsBusinessDto(List<BuildingRatingEntity> buildingRatingEntityList);

    public abstract RatingVO toRatingVO(BuildingRatingEntity entity);

}

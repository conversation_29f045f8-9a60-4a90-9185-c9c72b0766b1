package com.coocaa.meht.module.web.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by fengke on 2024/11/7.
 */
@Data
public class BuildingParameterDto {
    private Long id;

    private Long parentId;
    private Integer buildingType;

    private String parameterName;

    private String parameterCode;

    private BigDecimal parameterScore;

    private BigDecimal weightValue;

    private Integer sort;

    private List<BuildingParameterDto> child;
}

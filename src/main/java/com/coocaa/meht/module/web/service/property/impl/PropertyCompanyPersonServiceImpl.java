package com.coocaa.meht.module.web.service.property.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.bean.IReturnCode;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.dao.PropertyCompanyPersonMapper;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyPersonConvert;
import com.coocaa.meht.module.web.dto.property.PersonParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyPersonParam;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyPersonService;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyPersonVO;
import com.coocaa.meht.utils.AesUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PropertyCompanyPersonServiceImpl extends ServiceImpl<PropertyCompanyPersonMapper, PropertyCompanyPersonEntity> implements IPropertyCompanyPersonService {

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<PropertyCompanyPersonVO> savePerson(PersonParam param) {
        if (CollectionUtils.isEmpty(param.getPersonList())) {
            throw new ServerException("联系人为空");
        }
        // 手机号重复校验
        long count = param.getPersonList().stream().map(PropertyCompanyPersonParam::getPhone).distinct().count();
        if (count < param.getPersonList().size()) {
            throw new ServerException("手机号有重复");
        }
        // 邮箱校验
        param.getPersonList().forEach(person -> {
            if (StringUtils.isNotBlank(person.getEmail()) && person.getEmail().length() > 30) {
                throw new ServerException("电子邮箱长度必须在1-30之间");
            }
        });
        List<PropertyCompanyPersonEntity> personEntities = new ArrayList<>();
        List<Integer> personIds= new ArrayList<>();
        param.getPersonList().forEach(person -> {
            if (Objects.nonNull(person.getId())) {
                personIds.add(person.getId());
            }
            PropertyCompanyPersonEntity entity = PropertyCompanyPersonConvert.INSTANCE.toEntity(person);
            entity.setCompanyId(param.getPropertyId());
            personEntities.add(entity);
        });
        // 删除不存在的老数据
        this.remove(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                .eq(PropertyCompanyPersonEntity::getCompanyId, param.getPropertyId())
                .notIn(CollectionUtils.isNotEmpty(personIds), PropertyCompanyPersonEntity::getId, personIds));
        this.saveOrUpdateBatch(personEntities);
        return PropertyCompanyPersonConvert.INSTANCE.toVOs(personEntities);
    }

    @Override
    public synchronized Integer savePerson(@Validated PropertyCompanyPersonParam param) {
        // 数据插入前验证
        if (Objects.isNull(param.getCompanyId())) {
            throw new ServerException(Integer.parseInt(IReturnCode.Default.ERROR.getErrCode()), "物业公司id不能为空！");
        }
        // 验证数据是否存在
        boolean existed = this.exists(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                .eq(PropertyCompanyPersonEntity::getCompanyId, param.getCompanyId())
                .eq(PropertyCompanyPersonEntity::getName, param.getName())
                .eq(PropertyCompanyPersonEntity::getPhone, AesUtils.encryptHex(param.getPhone()))
        );
        if (existed) {
            log.warn("物业公司联系人已存在：{}", param);
            return 0;
        }
        // 数据持久化
        PropertyCompanyPersonEntity entity = PropertyCompanyPersonConvert.INSTANCE.toEntity(param);
        if (this.save(entity)) {
            return entity.getCompanyId();
        }

        throw new ServerException("新增物业公司联系人失败！");
    }
}

package com.coocaa.meht.module.web.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @description  
 * @since 2025-04-29
 */
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评论实体类
 */
@Data
@TableName("comment")
public class Comment {
    
    /**
     * 评论ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 业务类型（1楼宇、2价格申请）
     */
    private Integer businessType;
    
    /**
     * 业务ID
     */
    private String businessId;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 通知用户列表，JSON格式
     */
    private String notifiedUsers;
    
    /**
     * 附件列表
     */
    private String attachmentIds;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改人
     */
    private String updateBy;
    
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 是否删除: 0否,1是
     */
    @TableLogic
    private Integer deleteFlag;
} 
package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Created by fengke on 2024/11/21.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("personnel_approval")
public class PersonnelApprovalEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String threeOrg;     //三级组织
    private String fourOrg;     //三级组织
    private String empCode;     //员工编码
    private String empName;     //员工姓名
    private String empPost;   //员工岗位
    private String approvalCode;   //审批人工号
    private String approvalName;   //审批人姓名
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;
}

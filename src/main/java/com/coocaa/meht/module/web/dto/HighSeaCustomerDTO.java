package com.coocaa.meht.module.web.dto;

import com.coocaa.meht.common.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HighSeaCustomerDTO extends PageReq {

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "城市名称集合")
    private List<String> cities;

}

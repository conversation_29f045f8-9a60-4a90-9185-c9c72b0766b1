package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RatingApproveDto {

    /**
     * 楼宇编号
     */
    @Schema(description = "楼宇编号")
    @NotBlank(message = "楼宇编号不能为空")
    private String buildingNo;

    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过
     */
    @Schema(description = "楼宇状态：0待审核，1已审核 2已驳回 3审核不通过")
    @NotNull(message = "审批状态不能为空")
    private Integer status;


    @Schema(description = "审批意见")
    private String desc;

    /**
     * 复核系数
     */
    @Schema(description = "复核系数")
    private String finalCoefficient;

}

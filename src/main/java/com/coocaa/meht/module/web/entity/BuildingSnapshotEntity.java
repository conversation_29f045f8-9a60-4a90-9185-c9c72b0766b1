package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("building_snapshot")
public class BuildingSnapshotEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 楼盘编号
     */
    private String buildingRatingNo;

    /**
     * 楼盘meta快照
     */
    private String metaSnapshot;

    /**
     * 客户快照
     */
    private String ratingSnapshot;

    /**
     * 详情快照
     */
    private String detailsSnapshot;

    /**
     * 大屏快照
     */
    private String screenSnapshot;

    /**
     * 是否删除: 0否,1是
     */
    @TableLogic
    private Integer deleteFlag;

    /**
     * 快照类型：0-完善评级(老数据)，1-审核不通过快照，2-完善评级快照（1.7.8后新数据），3-评级编辑快照, 4-价格申请快照
     */
    private Integer type;

    /**
     * 评级版本
     */
    private String ratingVersion;

    @Getter
    @AllArgsConstructor
    public enum Type {
        OLD_COMPLETE(0, "完善评级快照(老数据)"),
        FAILED_AUDIT(1, "审核不通过或撤回快照"),
        COMPLETE(2, "完善评级快照(1.7.8后新数据)"),
        RATING_EDIT(3, "评级编辑快照"),
        PRICE_APPLY(4, "价格申请快照");

        private final Integer value;

        private final String name;
    }

}

package com.coocaa.meht.module.web.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/24
 * @description 楼宇清洗后的返回值
 */
@Data
public class BasicInfoAllVO {

    /**
     * 最后一个成功认证的no
     */
    private String buildingRatingNo;
    /**
     * 省名称
     */
    @Schema(description = "省名称")
    private String mapProvince;

    /**
     * 市名称
     */
    @Schema(description = "市名称")
    private String mapCity;

    /**
     * 区名称
     */
    @Schema(description = "区名称")
    private String mapRegion;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String mapAddress;

    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区")
    private Integer buildingType;

    @Schema(description = "楼宇评级ai")
    private String projectLevelAi;

    @Schema(description = "楼宇评级")
    private String projectLevel;
}

package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BuildingDetailsDao extends BaseMapper<BuildingDetailsEntity> {

    default List<BuildingDetailsEntity> listByBuildingNo(List<String> buildingNoList) {
        LambdaQueryWrapper<BuildingDetailsEntity> queryWrapper = new QueryWrapper<BuildingDetailsEntity>().lambda().eq(BuildingDetailsEntity::getBuildingNo, buildingNoList);
        return selectList(queryWrapper);

    }
}

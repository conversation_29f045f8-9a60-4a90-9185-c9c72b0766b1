package com.coocaa.meht.module.web.service.kanban;

import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dao.BuildingStatusChangeLogMapper;
import com.coocaa.meht.module.web.dto.BuildingStatusChangeLogWithRatingDto;
import com.coocaa.meht.module.web.dto.BusinessOpportunityWithRatingDto;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.vo.kanban.IndexVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import com.coocaa.meht.module.web.vo.kanban.SectionVO;
import com.coocaa.meht.module.web.vo.kanban.TabVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @file BuildingIncrementStatisticsHandler
 * @date 2025/1/3 16:02
 * @description 楼宇 - 新增统计
 */

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingIncrementStatisticsHandler {
    private final BuildingStatusChangeLogMapper buildingStatusChangeLogMapper;
    private final BusinessOpportunityService businessOpportunityService;
    private final BuildingRatingDao buildingRatingDao;

    public final static String CACHE_KEY_FORMAT = "kanban:city:%s:building:increment_statistics";
    public final static String KPI_FLAG = "新增统计";
    private final static String CERTIFIED_BUILDING = "已认证楼宇";
    private final static String RATING_APPLICATION = "评级申请";
    private final static String PROJECT_OPPORTUNITY = "项目商机";
    private final static String[] RECENT_DAYS = {"昨日", "最近7天", "最近30天"};
    private final static String INDEX_DEFAULT_PLACEHOLDER = "-";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private Map<String, LocalDateTime> minusDaysRange30;
    private Map<String, LocalDateTime> minusDaysRange7;
    private Map<String, LocalDateTime> minusDaysRange1;

    /**
     * 计算城市的指标数据
     */
    public KanbanVO getKanbanVOByCityResultList(List<String> cityResultList) {
        // 汇总城市数据
        Map<String, Map<String, Integer>> allCityMap = initCityCountBaseMap();
        cityResultList.forEach(cityResult -> {
            try {
                KanbanVO kanbanVO = OBJECT_MAPPER.readValue(cityResult, KanbanVO.class);
                if (kanbanVO == null) {
                    return;
                }
                // 更新总量数据
                kanbanVO.getTabs().forEach(tabVO -> {
                    Map<String, Integer> map = allCityMap.get(tabVO.getMainTitle());
                    tabVO.getSections().forEach(sectionVO -> {
                        sectionVO.getIndices().forEach(indexVO -> {
                            int kpiValue = Integer.parseInt(indexVO.getValue1());
                            if (kpiValue != 0) {
                                map.compute(indexVO.getName(), (k, v) -> v == null ? kpiValue : v + kpiValue);
                            }
                        });
                    });
                });
            } catch (JsonProcessingException e) {
                log.error("总量统计缓存数据转换失败！{}", e.getMessage());
                throw new RuntimeException(e);
            }
        });
        // 生成KanbanVO
        return generateKanbanVO(allCityMap);
    }


    /**
     * 获取指标数据类型的数据
     */
    public Map<String, KanbanVO> getKanbanVOByCityCodeMap() {
        initDateTimeRange();
        Map<String, Map<String, Map<String, HashSet<String>>>> incrementStatisticsByCityCodeMap = new HashMap<>();
        ratingApplicationHandle(incrementStatisticsByCityCodeMap);
        certifiedBuildingHandle(incrementStatisticsByCityCodeMap);
        destroyDateTimeRange();
        // 指标数据封装、适配
        Map<String, KanbanVO> kanbanVOByCityCodeMap = new HashMap<>(incrementStatisticsByCityCodeMap.size());
        incrementStatisticsByCityCodeMap.forEach((cityCode, incrementStatisticsMap) -> {
            kanbanVOByCityCodeMap.put(cityCode, generateKanbanVO(incrementStatisticsMap));
        });

        return kanbanVOByCityCodeMap;
    }

    /**
     * 初始化统计时间段
     */
    public void initDateTimeRange() {
        minusDaysRange30 = getTimeRangeMap(LocalDate.now(), -30);
        minusDaysRange7 = getTimeRangeMap(LocalDate.now(), -7);
        minusDaysRange1 = getTimeRangeMap(LocalDate.now(), -1);
    }

    /**
     * 销毁统计时间段
     */
    public void destroyDateTimeRange() {
        minusDaysRange30 = null;
        minusDaysRange7 = null;
        minusDaysRange1 = null;
    }

    /**
     * 生成初始的指标Map结构
     */
    private Map<String, Map<String, HashSet<String>>> initCityBaseMap() {
        Map<String, Map<String, HashSet<String>>> cityMap = new LinkedHashMap<>(RECENT_DAYS.length);
        for (String recentDay : RECENT_DAYS) {
            Map<String, HashSet<String>> recentDayMap = new LinkedHashMap<>(3);
            recentDayMap.put(RATING_APPLICATION, new HashSet<>());
            recentDayMap.put(CERTIFIED_BUILDING, new HashSet<>());
            cityMap.put(recentDay, recentDayMap);
        }

        return cityMap;
    }

    private Map<String, Map<String, Integer>> initCityCountBaseMap() {
        Map<String, Map<String, Integer>> cityMap = new LinkedHashMap<>(RECENT_DAYS.length);
        for (String recentDay : RECENT_DAYS) {
            Map<String, Integer> recentDayMap = new LinkedHashMap<>(3);
            recentDayMap.put(RATING_APPLICATION, 0);
            recentDayMap.put(CERTIFIED_BUILDING, 0);
            cityMap.put(recentDay, recentDayMap);
        }

        return cityMap;
    }

    /**
     * 生成指定的时间范围
     */
    public Map<String, LocalDateTime> getTimeRangeMap(LocalDate date, int offset) {
        final LocalDate d = date.plusDays(offset);
        final LocalDate yesterday = date.minusDays(1);
        return new HashMap<>() {{
            put("start", d.atStartOfDay());
            put("end", yesterday.atTime(LocalTime.MAX));
        }};
    }

    /**
     * 已认证楼宇
     */
    public void certifiedBuildingHandle(Map<String, Map<String, Map<String, HashSet<String>>>> incrementStatisticsByCityCodeMap) {
        List<String> projectLevelList = List.of("A", "AA", "AAA");
        // 数据库记录查询 - log记录
        Map<String, List<BuildingStatusChangeLogWithRatingDto>> buildingLogByCityCodeMap =
                buildingStatusChangeLogMapper.selectByStatus(BuildingStatusChangeLogEntity.BizType.RATING.getCode(),
                                BuildingStatusChangeLogEntity.RatingApplicationStatus.APPROVED.getCode(), minusDaysRange30, projectLevelList)
                        .stream().collect(Collectors.groupingBy(BuildingStatusChangeLogWithRatingDto::getMapCity));

        // 计算各个时间段内的数据
        rangeDataHandle(buildingLogByCityCodeMap, incrementStatisticsByCityCodeMap, CERTIFIED_BUILDING);
    }

    /**
     * 评级申请
     */
    public void ratingApplicationHandle(Map<String, Map<String, Map<String, HashSet<String>>>> incrementStatisticsByCityCodeMap) {
        // 数据库记录查询 - log记录
        Map<String, List<BuildingStatusChangeLogWithRatingDto>> buildingLogByCityCodeMap =
                buildingStatusChangeLogMapper.selectOfRatingApplicationRecords(BuildingStatusChangeLogEntity.BizType.RATING.getCode(),
                                BuildingStatusChangeLogEntity.RatingApplicationStatus.WAIT_APPROVED.getCode(), minusDaysRange30)
                        .stream().collect(Collectors.groupingBy(BuildingStatusChangeLogWithRatingDto::getMapCity));

        // 计算各个时间段内的数据
        rangeDataHandle(buildingLogByCityCodeMap, incrementStatisticsByCityCodeMap, RATING_APPLICATION);
    }

    /**
     * 项目商机
     */
    public void projectOpportunityHandle(Map<String, Map<String, Map<String, HashSet<String>>>> incrementStatisticsByCityCodeMap) {
        // 获取商机表的记录
        Map<String, List<BusinessOpportunityWithRatingDto>> businessOpportunityByCityCodeMap =
                businessOpportunityService.getBusinessOpportunityWithRatingDtoList(minusDaysRange30)
                        .stream().collect(Collectors.groupingBy(BusinessOpportunityWithRatingDto::getMapCity));

        // 数据按时间段进行分组统计
        Map<String, Map<String, List<BusinessOpportunityWithRatingDto>>> logByCityCodeTimeRangeMap =
                new HashMap<>(businessOpportunityByCityCodeMap.size());
        businessOpportunityByCityCodeMap.forEach((cityCode, buildingStatusChangeLogWithRatingDtoList) -> {
            logByCityCodeTimeRangeMap.put(cityCode, groupByTimeRange(buildingStatusChangeLogWithRatingDtoList, "getCreateTime"));
        });
        // 处理商机表的记录
        logByCityCodeTimeRangeMap.forEach((cityCode, logGroupByTimeRangeMap) -> {
            Map<String, Map<String, HashSet<String>>> incrementStatisticsMap = incrementStatisticsByCityCodeMap.computeIfAbsent(cityCode, k -> initCityBaseMap());
            for (Map.Entry<String, List<BusinessOpportunityWithRatingDto>> item : logGroupByTimeRangeMap.entrySet()) {
                String timeRange = item.getKey();
                List<BusinessOpportunityWithRatingDto> dtoList = item.getValue();
                //
                Map<String, HashSet<String>> allKpiMap = incrementStatisticsMap.get(timeRange);
                HashSet<String> buildingNoSet = allKpiMap.get(PROJECT_OPPORTUNITY);
                for (BusinessOpportunityWithRatingDto dto : dtoList) {
                    buildingNoSet.add(dto.getCode());
                }
            }
        });

    }

    /**
     * 数据按时间段进行分组统计
     */
    private <T> Map<String, List<T>> groupByTimeRange(List<T> entityList, String methodName) {
        Map<String, List<T>> groupByTimeRangeMap = new HashMap<>(3);
        for (String recentDay : RECENT_DAYS) {
            groupByTimeRangeMap.put(recentDay, new ArrayList<>());
        }
        //
        for (T entity : entityList) {
            try {
                Class<?> clazz = entity.getClass();
                Method targetMethod = clazz.getMethod(methodName);
                LocalDateTime targetTime = (LocalDateTime) targetMethod.invoke(entity);
                if (targetTime == null) continue;
                groupByTimeRangeMap.get(RECENT_DAYS[2]).add(entity);
                if (targetTime.isAfter(minusDaysRange1.get("start"))) {
                    groupByTimeRangeMap.get(RECENT_DAYS[0]).add(entity);
                }
                if (targetTime.isAfter(minusDaysRange7.get("start"))) {
                    groupByTimeRangeMap.get(RECENT_DAYS[1]).add(entity);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return groupByTimeRangeMap;
    }

    /**
     * 计算各个时间段内的数据
     */
    private void rangeDataHandle(Map<String, List<BuildingStatusChangeLogWithRatingDto>> buildingLogByCityCodeMap,
                                 Map<String, Map<String, Map<String, HashSet<String>>>> incrementStatisticsByCityCodeMap,
                                 String kpiKey) {
        // 数据按时间段进行分组统计
        Map<String, Map<String, List<BuildingStatusChangeLogWithRatingDto>>> logByCityCodeTimeRangeMap =
                new HashMap<>(buildingLogByCityCodeMap.size());
        buildingLogByCityCodeMap.forEach((cityCode, buildingStatusChangeLogWithRatingDtoList) -> {
            logByCityCodeTimeRangeMap.put(cityCode, groupByTimeRange(buildingStatusChangeLogWithRatingDtoList, "getChangeTime"));
        });
        // 处理log表的记录
        logByCityCodeTimeRangeMap.forEach((cityCode, logGroupByTimeRangeMap) -> {
            Map<String, Map<String, HashSet<String>>> incrementStatisticsMap = incrementStatisticsByCityCodeMap.computeIfAbsent(cityCode, k -> initCityBaseMap());
            for (Map.Entry<String, List<BuildingStatusChangeLogWithRatingDto>> item : logGroupByTimeRangeMap.entrySet()) {
                String timeRange = item.getKey();
                List<BuildingStatusChangeLogWithRatingDto> logList = item.getValue();
                //
                Map<String, HashSet<String>> allKpiMap = incrementStatisticsMap.get(timeRange);
                HashSet<String> buildingNoSet = allKpiMap.get(kpiKey);
                for (BuildingStatusChangeLogWithRatingDto dto : logList) {
                    buildingNoSet.add(dto.getBizCode());
                }
            }
        });
    }

    /**
     * 指标数据封装、适配
     */
    private <T> KanbanVO generateKanbanVO(Map<String, Map<String, T>> incrementStatisticsMap) {
        return generateKanbanVO(incrementStatisticsMap, null);
    }

    private <T> KanbanVO generateKanbanVO(Map<String, Map<String, T>> incrementStatisticsMap, String defaultValue) {
        // 按城市对数据进行处理
        List<TabVO> tabs = new ArrayList<>(RECENT_DAYS.length);
        KanbanVO kanbanVO = new KanbanVO().setMainTitle(KPI_FLAG).setSubTitle(INDEX_DEFAULT_PLACEHOLDER).setShowType(1)
                .setTips("展示鼠标移动上去提示").setShow(true).setTab(true)
                .setTabs(tabs);
        //
        incrementStatisticsMap.forEach((timeRange, allKpiMap) -> {
            List<SectionVO> sections = new ArrayList<>(1);
            TabVO tab = new TabVO().setMainTitle(timeRange).setSubTitle(INDEX_DEFAULT_PLACEHOLDER)
                    .setShow(true).setSections(sections);
            tabs.add(tab);

            // 固定给一层
            List<IndexVO> indices = new ArrayList<>(allKpiMap.size());
            SectionVO sectionVO = new SectionVO().setMainTitle(INDEX_DEFAULT_PLACEHOLDER).setSubTitle(INDEX_DEFAULT_PLACEHOLDER)
                    .setShowCount(allKpiMap.size()).setDataType("index").setShow(true).setIndices(indices);
            sections.add(sectionVO);

            // 指标层数据
            allKpiMap.forEach((k, valSet) -> {
                int intVal = valSet instanceof HashSet ? ((HashSet<?>) valSet).size() : Integer.parseInt(valSet.toString());
                String val = defaultValue != null && intVal == 0 ? defaultValue : String.valueOf(intVal);
                indices.add(new IndexVO().setKey(INDEX_DEFAULT_PLACEHOLDER).setName(k).setValue1(val)
                        .setValue2(INDEX_DEFAULT_PLACEHOLDER).setUnitType("number").setShow(true).setMain(false));
            });
        });

        return kanbanVO;
    }


    /**
     * @Author：TanJie
     * @Date：2025-01-13 21:25
     * @Description：生成统计结果的默认值
     */
    public KanbanVO getDefaultKanbanVO() {
        return generateKanbanVO(initCityCountBaseMap(), INDEX_DEFAULT_PLACEHOLDER);
    }
}

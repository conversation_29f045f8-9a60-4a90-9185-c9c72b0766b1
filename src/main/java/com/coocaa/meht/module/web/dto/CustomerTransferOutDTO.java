package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Data
public class CustomerTransferOutDTO {

    @Schema(description = "客户id集合")
    @NotEmpty(message = "客户不能为空")
    private List<Long> ids;

    /**
     * web客户公海批量客户分配功能使用
     */
    @Schema(description = "负责人id")
    @NotNull(message = "负责人id不能为空")
    private Integer userId;

    /**
     * web客户公海批量客户分配功能使用
     */
    @Schema(description = "批量操作标识", hidden = true)
    private Boolean bachOperateFlag = false;

}

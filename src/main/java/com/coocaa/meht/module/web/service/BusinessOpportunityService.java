package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BusinessOpportunityWithRatingDto;
import com.coocaa.meht.module.web.dto.BusinessProjectDto;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机
 */
public interface BusinessOpportunityService extends IService<BusinessOpportunityEntity> {
    /**
     * 价格申请查询商机列表
     */
    List<BusinessProjectDto> getPriceApplyBusiness(String name, Boolean allUser, List<String> status, String userCode);

    /**
     * @Author：TanJie
     * @Date：2025-01-16 17:19
     * @Description：获取指定时间范围的商机列表
     */
    List<BusinessOpportunityWithRatingDto> getBusinessOpportunityWithRatingDtoList(Map<String, LocalDateTime> timeRangeMap);


    /**
     * @Description：修改跟进状态
     */
    void updateFollowRecord(String buildingNo);

    /**
     * 获取楼宇下商机变更到指定状态的的最早时间
     */
    LocalDateTime getEarliestChangeTime(String buildingNo, String status);


    /**
     * 查询是否曾经有到达过合同流程的商机
     */
    boolean isContractedBefore(Collection<Integer> businessIds);

    /**
     * 恢复商机
     */
    void recover(List<BusinessOpportunityEntity> businessEntities);

}

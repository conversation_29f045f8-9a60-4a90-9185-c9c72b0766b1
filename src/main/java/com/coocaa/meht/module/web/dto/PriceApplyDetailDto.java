package com.coocaa.meht.module.web.dto;


import com.coocaa.meht.common.bean.feishu.FlowNode;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.utils.BigDecimalUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class PriceApplyDetailDto {

    /**
     * 申请id
     */
    private Integer priceApplyId;

    /**
     * 楼宇编码
     */
    private String buildingNo;
    /**
     * 商机编码
     */
    private String businessCode;

    /**
     * 商机名称
     */
    private String businessName;
    /**
     * 楼宇编码
     */
    private String bu;

    /**
     * 申请名称(楼盘名称)
     */
    private String projectName;

    /**
     * 楼盘类型
     */
    private String projectType;

    /**
     * 价格申请编码： JGSQ+年月日+流水号（4位，从0001开始）
     */
    private String applyCode;

    /**
     * 等级评价
     */
    private String score;

    /**
     * 是否是大屏 [1:小屏, 2:大屏, 3:大小屏]
     */
    private Integer largeScreenFlag;

    /**
     * 复核系数，审核后确定的系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 省市区
     */
    private String mapAddress;


    /**
     * 合同年限
     */
    private BigDecimal contractDuration;

    /**
     * 合同总金额
     */
    private BigDecimal totalAmount;

    /**
     * 付款方式
     */
    private String paymentType;

    /**
     * 是否有押金
     */
    private Boolean isDeposit;

    /**
     * 押金金额
     */
    private BigDecimal depositAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建人
     */
    private String  createBy;

    /**
     * 附件列表
     */
    private List<Long> fileIds;

    /**
     * 设备列表
     */
    private List<DeviceDetailDto> devices;

    /**
     * 附件详情
     */
    private List<FilesDetailDto> files;

    /**
     * true 属于自己的申请
     */
    private Boolean isSelfApply;

    /**
     * 当前登陆人是否为审批人
     */
    @Schema(description = "当前登陆人是否为审批人")
    private Boolean isSelfApprove;

    /**
     * 审批人
     */
    private String approveBy;

    /**
     * 审批人名
     */
    private String approveName;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 审批备注
     */
    private String approveRemark;

    /**
     * 申请人名
     */
    private String  createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * top值
     */
    private String topLevel;

    private List<FlowNode> flowNodes;

    /**
     * 实例编码
     */
    @Schema(description = "实例编码")
    private String instanceCode;

    /**
     * 审核记录
     */
    @Schema(description = "审核记录")
    private List<ScreenApproveRecordDTO> approveRecords;

    /**
     * 经纬度度
     */
    private String longitude;
    private String latitude;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    @Schema(description = "去掉省市区的详细地址")
    private String simplifyAddress;


    /**
     * 点位数量
     */
    private Integer pointCount;

    /**
     * 评级版本
     */
    private String ratingVersion;

    /**
     * 省
     */
    private String mapProvince;

    /**
     * 区名称
     */
    private String mapRegion;

    /**
     * 数据版本号
     */
    private String version;


    @Data
    @Accessors(chain = true)
    public static class FilesDetailDto {

        /**
         * 附件id
         */
        private Long id;

        /**
         * 附件名称
         */
        private String name;

        /**
         * 附件地址
         */
        private String url;
    }


    @Data
    @Accessors(chain = true)
    public static class DeviceDetailDto {
        /**
         * 价格申请设备id
         */
        private Integer applyDeviceId;
        /**
         * 设备类型
         */
        private String type;

        /**
         * 设备尺寸
         */
        private String size;

        /**
         * 设备数量
         */
        private Integer quantity;

        /**
         * 签约单价(元/台/年)
         */
        private BigDecimal signPrice;

        /**
         * 签约总价(元/年)
         */
        private BigDecimal totalPrice;

        /**
         * 安装位置
         */
        @Deprecated
        private String location;

        /**
         * 安装位置 JSON
         */
        @Deprecated
        private String locationDetail;

        @Schema(description = "终端点位", type = "List", example = "{}")
        private List<DevicePointDto> points;


        /**
         * 激励单价(元/台/月)
         */
        private BigDecimal incentivePrice;

        /**
         * 是否是大屏 [1:小屏, 2:大屏, 3:大小屏]
         */
        private Integer largeScreenFlag;

        /**
         * 是否核心区域 [0:非, 1:是]
         */
        private Integer coreAreaFlag;

        /**
         * 总单价
         */
        @Deprecated
        private BigDecimal totalUnitPrice = BigDecimalUtils.add(incentivePrice, signPrice);

        /**
         * 小屏水位价
         */
        private String waterMarkPriceForSmall;

        /**
         * 大屏水位价
         */
        private String waterMarkPriceForBig;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DevicePointDto {
        @Schema(description = "点位编码", type = "String", example = "FZ000402")
        private String code;

        @Schema(description = "点位名称", type = "String", example = "2栋_1单元_1层_大堂_1号电梯厅_点位2")
        private String name;

        @Schema(description = "是否选中", type = "Boolean", example = "FZ000402")
        private Boolean checked = false;
    }
}

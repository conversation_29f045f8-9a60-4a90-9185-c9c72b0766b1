package com.coocaa.meht.module.web.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 楼宇基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
public class BuildingRatingDTO {


    @ExcelProperty(value = "buildingNo")
    private String buildingNo;



    /**
     * ai的等级评价
     * */
    @ExcelProperty(value = "projectAiLevel")
    private String projectLevelAi;



    /**
     * ai 等级评分
     * */
    @ExcelProperty(value = "buildingAiScore")
    private BigDecimal buildingAiScore;
}

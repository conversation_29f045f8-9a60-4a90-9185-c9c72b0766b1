package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("building_parameter")
public class BuildingParameterEntity  extends BaseEntity {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    private Integer buildingType;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 参数名称
     */
    private String parameterName;

    /**
     * 参数编码
     */
    private String parameterCode;

    /**
     * 参数规则
     */
    private String parameterRule;
    /**
     * 参数值
     */
    private BigDecimal parameterScore;

    /**
     * 权重值
     */
    private BigDecimal weightValue;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否删除: 0否,1是
     */
    private Integer deleted;

    /**
     * 数据标识版本
     */
    private Integer dataFlag;

    /**
     * 评分和权重
     */
    public record ScoreAndWeight<BigDecimal>(BigDecimal Score, BigDecimal weight) {}

}
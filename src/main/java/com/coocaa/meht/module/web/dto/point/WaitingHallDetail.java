package com.coocaa.meht.module.web.dto.point;

import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import lombok.Data;

@Data
public class WaitingHallDetail {
    private Integer id;
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    private String floor;
    @Convert(type = ConvertType.DICT)
    private String floorName;
    private String waitingHallName;

    private String waitingHallType;

    @Convert(type = ConvertType.DICT)
    private String waitingHallTypeName;

    private Integer pointPlanId;
} 
package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.handler.EncryptHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.logging.Handler;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@TableName(value = "property_company", autoResultMap = true)
public class PropertyCompanyEntity implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 物业公司类型：1企业、2个人
     */
    private Integer type;

    /**
     * 物业公司名称
     */
    private String name;

    /**
     * 统一社会信用代码：企业类型必填
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String unifiedSocialCreditCode;

    /**
     * 身份证号
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String idCard;

    /**
     * 手机号码:个人必填
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String phone;

    /**
     * 物业地址
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String address;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String  createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String  updateBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}

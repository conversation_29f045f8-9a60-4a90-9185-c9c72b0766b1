package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同申请表单自定义id
 */
@Getter
@AllArgsConstructor
public enum FeiShuApprovalFormIdEnum implements IEnumType<String> {
    PRICE_APPLY_NO("price_apply_no", "申请单号"),
    ITEM_NAME("item_name", "项目名称"),
    BUILDING_TYPE("building_type", "物业类型"),
    CITY("city", "项目城市"),
    AMOUNT("amount", "总金额"),
    APPLY_USER("apply_user", "申请人姓名"),
    APPLY_TIME("apply_time", "申请时间"),
    DETAILS("details", "价格申请详情");


    private final String code;
    private final String desc;

    private final static Map<String, FeiShuApprovalFormIdEnum> BY_CODE_MAP =
            Arrays.stream(FeiShuApprovalFormIdEnum.values())
                    .collect(Collectors.toMap(FeiShuApprovalFormIdEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static FeiShuApprovalFormIdEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static FeiShuApprovalFormIdEnum parse(String code, FeiShuApprovalFormIdEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(FeiShuApprovalFormIdEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}

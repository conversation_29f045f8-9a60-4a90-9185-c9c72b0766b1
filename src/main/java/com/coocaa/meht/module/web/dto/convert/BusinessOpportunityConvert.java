package com.coocaa.meht.module.web.dto.convert;

import com.coocaa.meht.module.web.dto.BusinessOpportunityDto;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description 商机
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface BusinessOpportunityConvert {
    BusinessOpportunityConvert INSTANCE = Mappers.getMapper(BusinessOpportunityConvert.class);

    /**
     * Entity数组转Dto数组
     */
    List<BusinessOpportunityDto> toDtoList(List<BusinessOpportunityEntity> entities);
}

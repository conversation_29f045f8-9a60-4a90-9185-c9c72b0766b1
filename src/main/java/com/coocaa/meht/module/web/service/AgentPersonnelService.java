package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.AgentPersonnelDto;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;

import javax.naming.CommunicationException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-11-08 15:19
 */
public interface AgentPersonnelService extends IService<AgentPersonnelEntity> {

    /**
     * 根据手机号查询
     *
     * @param phone
     * @return
     */
    AgentPersonnelEntity getByPhone(String phone);

    AgentPersonnelEntity getByEmpCode(String empCode);

    List<AgentPersonnelEntity> getByEmpCodes(List<String> empCodes);


    /**
     * 创建外部代理商
     * @param dto
     * @return
     */
    boolean addAgentPersonnel(AgentPersonnelDto dto) throws CommunicationException;

    /**
     * 修改代理商状态
     * @param dto
     * @return
     * @throws CommunicationException
     */
    boolean updateStatus(AgentPersonnelDto dto);
}

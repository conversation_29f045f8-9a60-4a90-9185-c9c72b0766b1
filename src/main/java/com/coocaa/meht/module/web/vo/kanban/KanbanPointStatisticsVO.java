package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 城市维度点位状态统计
 */
@Data
@Accessors(chain = true)
public class KanbanPointStatisticsVO {

    /**
     * 城市名称
     */
    @Schema(description = "城市名称", type = "String", example = "成都市")
    private ContractPointStatisticsVO currentData;
    /**
     * 点位状态转化率
     */
    @Schema(description = "点位状态转化率", type = "list")
    private List<DataItemVO> ratioData;
    /**
     * 点位状态每日统计数据
     */
    @Schema(description = "点位状态每日统计数据", type = "list")
    private List<ContractPointStatisticsVO> trendData;


}

package com.coocaa.meht.module.web.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RatingCalculateDto {

    @Schema(description = "楼宇名称")
    @NotBlank(message = "楼宇名称不能为空")
    private String buildingName;

    @Schema(description = "百度地图楼宇编码")
    @NotBlank(message = "百度地图楼宇编码不能为空")
    private String mapNo;

    @Schema(description = "百度地图行政编码")
    @NotBlank(message = "百度地图行政编码不能为空")
    private String mapAdCode;

    @Schema(description = "百度地图城市名称")
    @NotBlank(message = "百度地图城市名称不能为空")
    private String mapCity;

    @Schema(description = "楼宇类型")
    @NotNull(message = "楼宇类型不能为空")
    private Integer buildingType;

    /**
     * 楼宇等级
     */
    @Schema(description = "楼宇等级")
    private Long buildingGrade;
    private String buildingGradeName;

    /**
     * 地理位置
     */
    @Schema(description = "地理位置")
    private Long buildingLocation;
    private String buildingLocationName;

    /**
     * 楼层数
     */
    @Schema(description = "楼层数")
    private Long buildingNumberInput;

    /**
     * 楼龄
     */
    @Schema(description = "楼龄")
    private Long buildingAgeInput;

    /**
     * 月租金
     */
    @Schema(description = "月租金")
    private Long buildingPriceInput;

    /**
     * 外立面
     */
    @Schema(description = "外立面")
    private Long buildingExterior;
    private String buildingExteriorName;

    /**
     * 大堂
     */
    @Schema(description = "大堂")
    private Long buildingLobby;
    private String buildingLobbyName;

    /**
     * 车库
     */
    @Schema(description = "车库")
    private Long buildingGarage;
    private String buildingGarageName;

    /**
     * 等候厅
     */
    @Schema(description = "等候厅")
    private Long buildingHall;
    private String buildingHallName;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private Long buildingBrand;
    private String buildingBrandName;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private Long buildingRating;
    private String buildingRatingName;

    /**
     * 入驻率
     */
    @Schema(description = "入驻率")
    private Long buildingSettled;
    private String buildingSettledName;

}

package com.coocaa.meht.module.web.service.impl;

import com.coocaa.meht.module.web.service.UserGuideService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导服务实现类
 * @since 2025-06-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserGuideServiceImpl implements UserGuideService {

    private final StringRedisTemplate redisTemplate;

    private static final String USER_GUIDE_GROUP = "user:guide:read";

    private String buildSubKey(String userCode, String featureCode) {
        return userCode + ":" + featureCode;
    }

    @Override
    public boolean checkUserRead(String featureCode, String userCode) {
        log.info("检查用户是否已读功能引导: featureCode={}, userCode={}", featureCode, userCode);
        String key = buildSubKey(userCode, featureCode);
        Boolean hasRead = redisTemplate.opsForHash().hasKey(USER_GUIDE_GROUP, key);
        log.info("检查用户是否已读功能引导结果: featureCode={}, userCode={}, hasRead={}", featureCode, userCode, hasRead);
        return Boolean.TRUE.equals(hasRead);
    }

    @Override
    public boolean markRead(String userCode, String featureCode) {
        log.info("标记用户已读功能引导: userCode={}, featureCode={}", userCode, featureCode);
        try {
            String key = buildSubKey(userCode, featureCode);
            redisTemplate.opsForHash().put(USER_GUIDE_GROUP, key, "1");
            log.info("标记用户已读功能引导成功: userCode={}, featureCode={}", userCode, featureCode);
            return true;
        } catch (Exception e) {
            log.error("标记用户已读功能引导异常: userCode={}, featureCode={}, error={}", userCode, featureCode, e.getMessage(), e);
            return false;
        }
    }
} 
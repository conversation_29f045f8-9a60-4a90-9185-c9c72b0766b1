package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class DataItemVO<T> {
    @Schema(description = "指标名称", type = "string", example = "xxx指标")
    private String name;

    @Schema(description = "指标值", type = "number", example = "0")
    private T value;

    @Schema(description = "转化率", type = "number", example = "12.7")
    public BigDecimal ratio;
}

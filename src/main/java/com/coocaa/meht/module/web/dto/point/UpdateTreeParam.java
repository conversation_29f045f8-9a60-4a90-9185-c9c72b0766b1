package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class UpdateTreeParam {

    @Schema(description = "楼宇编码")
    @NotBlank(message = "楼宇编码不能为空")
    private String buildingRatingNo;

    @Schema(description = "商机编码")
    @NotBlank(message = "商机编码不能为空")
    private String businessCode;

    @Schema(description = "楼栋名称，修改单元，楼层的时候传")
    private String buildingName;
    @Schema(description = "单元名称,修改楼层的时候传")
    private String unitName;

    @Schema(description = "楼层传字典")
    private String floorName;

    @Schema(description = "原来的值")
    private String originVale;

    @Schema(description = "节点类型: building/unit/floor")
    private String type;


}

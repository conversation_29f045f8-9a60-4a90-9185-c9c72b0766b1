package com.coocaa.meht.module.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 楼宇基因VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode
@Schema(description = "楼宇基因VO")
public class BuildingGeneVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "楼宇编号")
    private String buildingRatingNo;

    @Schema(description = "日租金")
    private BigDecimal dailyPrice;

    @Schema(description = "房价")
    private BigDecimal housePrice;

    @Schema(description = "竞媒信息")
    private List<String> competitiveMediaInfos;

    @Schema(description = "竞媒信息")
    private String competitiveMediaInfo;

    @Schema(description = "竞媒信息")
    private String competitiveMediaInfoName;

    @Schema(description = "目标点位数量")
    private Integer targetPointCount;

    @Schema(description = "最高层数")
    private Integer maxFloorCount;

    @Schema(description = "楼龄")
    private Integer buildingAge;

    @Schema(description = "总楼栋数量")
    private Integer totalBuildingCount;

    @Schema(description = "总单元数量")
    private Integer totalUnitCount;

    @Schema(description = "总等候厅数量")
    private Integer totalWaitingCount;

    @Schema(description = "电梯数量")
    private Integer elevatorCount;

    @Schema(description = "车位数量")
    private Integer parkingCount;

    @Schema(description = "禁忌行业")
    private String forbiddenIndustry;

    @Schema(description = "禁忌行业")
    private String forbiddenIndustryName;


    @Schema(description = "入驻企业数量")
    private Integer companyCount;

    @Schema(description = "入住率")
    private BigDecimal occupancyRate;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 楼间距，表示楼宇之间的间距（单位：米）
     */
    @Schema(description = "楼间距")
    private BigDecimal buildingSpacing;

    /**
     * 挑高，表示楼宇的层高（单位：米）
     */
    @Schema(description = "挑高")
    private BigDecimal buildingCeilingHeight;


    @Schema(description = "物业费")
    private BigDecimal propertyFee;

    @Schema(description = "最低楼层数")
    private Integer minFloorCount;

    @Schema(description = "交付时间")
    private LocalDate deliveryDate;

    @Schema(description = "覆盖人数")
    private Integer coverageCount;
} 
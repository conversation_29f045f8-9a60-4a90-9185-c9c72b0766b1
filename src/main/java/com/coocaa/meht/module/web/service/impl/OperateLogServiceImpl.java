package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.module.web.dao.OperateLogMapper;
import com.coocaa.meht.module.web.entity.OperateLogEntity;
import com.coocaa.meht.module.web.service.OperateLogService;
import org.springframework.stereotype.Service;

/**
 * 操作日志服务类实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLogEntity> implements OperateLogService {


    @Override
    public void saveOperateLog(Integer businessType, String businessNo, String description, String OperateType, String changeDesc) {
        OperateLogEntity operateLogEntity = new OperateLogEntity();
        operateLogEntity.setType(businessType);
        operateLogEntity.setBizId(businessNo);
        operateLogEntity.setOperateType(OperateType);
        operateLogEntity.setDescription(description);
        operateLogEntity.setOperateType(OperateType);
        operateLogEntity.setContent(changeDesc);
        operateLogEntity.setCreator(UserThreadLocal.getUser().getId());
        this.save(operateLogEntity);
    }
}

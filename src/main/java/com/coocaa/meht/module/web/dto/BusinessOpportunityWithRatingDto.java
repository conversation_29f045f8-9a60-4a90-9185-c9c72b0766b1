package com.coocaa.meht.module.web.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/01/16
 * @description 商机
 */
@Data
public class BusinessOpportunityWithRatingDto {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 商机名称
     */
    @Schema(description = "商机名称")
    private String name;
    /**
     * 商机编号
     */
    @Schema(description = "商机编号")
    private String code;
    /**
     * 商机状态
     */
    @Schema(description = "商机状态")
    private String status;
    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * building_rating表的building_no
     */
    @Schema(description = "building_rating表的building_no")
    private String buildingNo;
    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String submitUser;
    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private String owner;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // ====================================
    /**
     * 所在区域编码
     */
    private String mapCity = "";
}

package com.coocaa.meht.module.web.dto.req;

import com.coocaa.meht.common.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PriceApplyQueryCmsReq extends PageReq {
    /**
     * 查询关键字
     */
    private String search;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 工号
     */
    private String wno;

    /**
     * 楼盘编号
     */
    private String buildingNo;

    /**
     * 商机编码
     */
    private String businessCode;
}

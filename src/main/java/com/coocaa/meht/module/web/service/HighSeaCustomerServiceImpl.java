package com.coocaa.meht.module.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.OperateLogDTO;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.constants.DictCodeConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.job.EnterSeaJob;
import com.coocaa.meht.module.building.param.TransferAndDropParam;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dto.CustomerTransferIntoDTO;
import com.coocaa.meht.module.web.dto.CustomerTransferOutDTO;
import com.coocaa.meht.module.web.dto.HighSeaCustomerDTO;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.vo.HighSeaCustomerVO;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignMehtWebRpc;
import com.coocaa.meht.rpc.dto.CityVO;
import com.coocaa.meht.rpc.dto.UserBatchMessageParam;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import com.coocaa.meht.rpc.dto.UserMessageContentParam;
import com.coocaa.meht.utils.MessageSendUtil;
import com.coocaa.meht.utils.OperateLogUtils;
import com.coocaa.meht.utils.RsaExample;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Slf4j
@Service
public class HighSeaCustomerServiceImpl implements HighSeaCustomerService {

    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private BuildingRatingDao buildingRatingDao;

    @Autowired
    private ConverterFactory converterFactory;

    @Autowired
    private CodeNameHelper codeNameHelper;

    @Autowired
    private PermissionHandler permissionHandler;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private HighSeaRecordService highSeaRecordService;

    @Autowired
    private IBuildingMetaService buildingMetaService;

    @Autowired
    private RsaExample rsaExample;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private FeignMehtWebRpc feignMehtWebRpc;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private MessageSendUtil messageSendUtil;

    @Autowired
    private EnterSeaJob enterSeaJob;

    @Resource
    private UserCacheHelper userCacheHelper;

    @Resource
    private CompleteRatingService completeRatingService;

    /**
     * 认领客户消息模板
     */
    private static final String CLAIM_MSG = "%s的%s被%s认领了";

    @Override
    public PageResult<HighSeaCustomerVO> list(HighSeaCustomerDTO param) {
        // 数据权限
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(null, param.getCities());
        if (Objects.isNull(cmsPermission.getCities())) {
            // 没有符合权限的条件，直接返回空数据
            return new PageResult<>(Collections.emptyList(), 0);
        }

        // 设置有权限的城市
        param.setCities(cmsPermission.getCities());

        Page<HighSeaCustomerVO> page = buildingRatingDao.listHighSeaCustomer(
                new Page<>(param.getPage(), param.getLimit()), param);

        page.getRecords().forEach(item -> {
            item.setMapAddress(rsaExample.decryptByPrivate(item.getMapAddress()));
        });
        converterFactory.convert(page.getRecords());

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferInto(CustomerTransferIntoDTO param) {
        Map<String, String> dictMapping = codeNameHelper.getDictMapping(Collections.singletonList(param.getEnterSeaReason()));
        if (CollUtil.isEmpty(dictMapping)) {
            throw new ServerException("放入公海原因字典数据不存在");
        }

        // web端操作人
        CachedUser user = UserThreadLocal.getUser();

        // 楼宇信息
        List<BuildingRatingEntity> buildingRatings = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getSubmitUser,
                        BuildingRatingEntity::getUpdateTime)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
                        BuildingRatingEntity.Status.AUDITED.getValue())
                .in(BuildingRatingEntity::getId, param.getIds())
                .list();
        if (CollUtil.isEmpty(buildingRatings)) {
            throw new ServerException("无有效客户");
        }

        List<String> buildingNos = new ArrayList<>(buildingRatings.size());
        StringJoiner nameJoiner = new StringJoiner("，");
        for (BuildingRatingEntity buildingRating : buildingRatings) {
            nameJoiner.add(buildingRating.getBuildingName());
            buildingNos.add(buildingRating.getBuildingNo());
        }

        // 履约中合同校验
        contractingCheck(param, buildingNos, buildingRatings);

        LocalDateTime enterSeaTime = LocalDateTime.now();

        // 客户入公海
        doTransfer(enterSeaTime, param.getEnterSeaReason(), "", buildingRatings, BuildingRatingEntity.HighSeaFlagEnum.YES);

        // 入公海记录
        highSeaRecordService.record(enterSeaTime, user.getWno(), param.getEnterSeaReason(), BuildingRatingEntity.HighSeaFlagEnum.YES, buildingRatings);

        if (param.getBachOperateFlag()) {
            String reason = dictMapping.get(param.getEnterSeaReason());
            Integer operatorId = param.getBachOperateFlag() ? user.getId() : UserThreadLocal.getUserId();
            // 操作日志
            logOperate(operatorId, buildingRatings.get(0).getId().intValue(), "批量放入公海",
                    String.format("%s放入公海，理由是%s", nameJoiner, reason));

            // 消息通知
            sendMessage(user.getName() + "（" + user.getWno() + "）" + "已经将客户%s放入公海", buildingRatings);
        }
    }

    private void contractingCheck(CustomerTransferIntoDTO param, List<String> buildingNos, List<BuildingRatingEntity> buildingRatings) {
        List<BusinessOpportunityEntity> businessOpportunities = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getBuildingNo, buildingNos)
                .list();
        if (CollUtil.isNotEmpty(businessOpportunities)) {
            List<String> codes = RpcUtils.unBox(feignCmsRpc.isContractSigning(
                    businessOpportunities.stream()
                            .map(BusinessOpportunityEntity::getCode)
                            .collect(Collectors.toSet())));
            if (CollUtil.isNotEmpty(codes)) {
                log.info("客户已签订合同，不能放入公海！商机编号{}", codes);
                if (param.getBachOperateFlag()) {
                    // web端批量操作
                    // 合同履约中客户编号
                    Set<String> contractingBuildingNos = businessOpportunities.stream().filter(e -> codes.contains(e.getCode()))
                            .map(BusinessOpportunityEntity::getBuildingNo).collect(Collectors.toSet());
                    List<String> buildingNames = buildingRatings.stream().filter(e -> contractingBuildingNos.contains(e.getBuildingNo()))
                            .map(BuildingRatingEntity::getBuildingName).toList();
                    StringJoiner joiner = new StringJoiner("，");
                    for (int i = 0; i < buildingNames.size(); i++) {
                        if (i == 5) {
                            joiner.add("...");
                            break;
                        }
                        joiner.add(buildingNames.get(i));
                    }
                    throw new ServerException(String.format("客户%s已签订合同，不能放入公海！", joiner));
                } else {
                    // H5单个操作
                    throw new ServerException("该客户已签订合同，不能放入公海！");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferOut(CustomerTransferOutDTO param) {
        // 负责人
        String managerName;
        String managerCode;
        if (param.getBachOperateFlag()) {
            // web批量操作负责人校验
            if (Objects.isNull(param.getUserId())) {
                throw new ServerException("负责人不能为空");
            }

            CachedUser user = userCacheHelper.getUser(param.getUserId());
            if (Objects.isNull(user)) {
                throw new ServerException("负责人不存在");
            }
            managerName = user.getName();
            managerCode = user.getWno();
        } else {
            // H5认领客户操作负责人为登录用户
            CachedUser user = UserThreadLocal.getUser();
            managerName = user.getName();
            managerCode = user.getWno();
        }

        // web端操作人
        CachedUser user = UserThreadLocal.getUser();

        // 客户数校验
        Long count = RpcUtils.unBox(feignMehtWebRpc.getAvailableUserCount(managerCode));
        if (count < param.getIds().size()) {
            throw new ServerException(String.format("客户分配失败，%s剩余客户数量为%d，本次分配数量大于%d", managerName, count, count));
        }

        // 客户信息
        List<BuildingRatingEntity> buildingRatings = buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getMapCity,
                        BuildingRatingEntity::getUpdateTime)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.YES.getCode())
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
                        BuildingRatingEntity.Status.AUDITED.getValue())
                .in(BuildingRatingEntity::getId, param.getIds())
                .list();
        if (CollUtil.isEmpty(buildingRatings)) {
            throw new ServerException("无有效公海客户");
        }

        List<String> buildingNos = new ArrayList<>(buildingRatings.size());
        StringJoiner nameJoiner = new StringJoiner("，");
        for (BuildingRatingEntity buildingRating : buildingRatings) {
            nameJoiner.add(buildingRating.getBuildingName());
            buildingNos.add(buildingRating.getBuildingNo());
            // 设置负责人，发送消息用到
            buildingRating.setSubmitUser(managerCode);
        }

        // 客户出公海
        doTransfer(null,"", managerCode, buildingRatings, BuildingRatingEntity.HighSeaFlagEnum.NO);

        // 刷新入公海时间
        refreshEnterSeaTime(buildingNos);

        // 出公海记录
        highSeaRecordService.record(LocalDateTime.now(), user.getWno(), "", BuildingRatingEntity.HighSeaFlagEnum.NO, buildingRatings);

        if (param.getBachOperateFlag()) {
            // 批量操作记录日志
            logOperate(user.getId(), buildingRatings.get(0).getId().intValue(), "批量客户分配",
                    String.format("%s分配给%s", nameJoiner, managerName));

            // 消息通知
            sendMessage(user.getName() + "（" + user.getWno() + "）" + "将客户%s分配给你了，快去看看吧！", buildingRatings);
        } else {
            // H5认领客户操作给客户所属城市负责人发消息提醒
            // 20250801产品要求去掉该消息发送
            // sendMessageToCityBusinessHead(buildingRatings);
        }
    }

    @Override
    public void refreshEnterSeaTime(Collection<String> buildingNos) {
        log.info("刷新入公海时间，{}", buildingNos);

        if (CollUtil.isEmpty(buildingNos)) {
            log.info("buildingNos为空");
            return;
        }

        List<BuildingRatingEntity> entities = buildingRatingService.lambdaQuery().select(
                        BuildingRatingEntity::getId,
                        BuildingRatingEntity::getStatus,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getSubmitTime,
                        BuildingRatingEntity::getUpdateTime,
                        BuildingRatingEntity::getApproveTime,
                        BuildingRatingEntity::getEnterSeaTime,
                        BuildingRatingEntity::getEnterSeaCalculateTime)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .in(BuildingRatingEntity::getBuildingNo, buildingNos)
                .list();

        if (CollUtil.isEmpty(entities)) {
            log.info("entities为空");
            return;
        }

        List<BuildingRatingEntity> refreshEntities = new ArrayList<>(entities.size());
        // 不会自动掉公海数据id集合
        List<BuildingRatingEntity> neverEnterEntities = new ArrayList<>(entities.size());
        for (BuildingRatingEntity buildingRating : entities) {
            if (BuildingRatingEntity.Status.AUDITED.getValue() != buildingRating.getStatus()) {
                // 非审核通过数据，不会自动掉入公海
                neverEnterEntities.add(buildingRating);
                log.info("客户({} {})入公海时间将置空", buildingRating.getBuildingNo(), buildingRating.getBuildingName());
                continue;
            }

            Map<Integer, LocalDateTime> thresholdMapping = enterSeaJob.getThreshold(buildingRating);
            if (Objects.isNull(thresholdMapping)) {
                // 不会掉入自动公海数据
                neverEnterEntities.add(buildingRating);
                log.info("客户({} {})入公海时间将置空", buildingRating.getBuildingNo(), buildingRating.getBuildingName());
                continue;
            }

            Map.Entry<Integer, LocalDateTime> entry = thresholdMapping.entrySet().iterator().next();
            // 入公海阈值
            Integer threshold = entry.getKey();
            // 入公海计算起始时间
            LocalDateTime calculateTime = entry.getValue();
            // 入公海时间
            LocalDateTime enterSeaTime = calculateTime.plusDays(threshold);

            if (enterSeaTime.equals(buildingRating.getEnterSeaTime())) {
                // 入公海时间未改变，忽略
                continue;
            }

            BuildingRatingEntity entity = new BuildingRatingEntity();
            entity.setId(buildingRating.getId());
            entity.setEnterSeaTime(enterSeaTime);
            entity.setUpdateTime(buildingRating.getUpdateTime());
            refreshEntities.add(entity);
            log.info("客户({} {})入公海时间需更新为{}", buildingRating.getBuildingNo(), buildingRating.getBuildingName(), enterSeaTime);
        }

        if (CollUtil.isNotEmpty(refreshEntities)) {
            buildingRatingService.updateBatchById(refreshEntities);
        }

        // 不会掉入公海数据，置空入公海时间
        if (CollUtil.isNotEmpty(neverEnterEntities)) {
            for (BuildingRatingEntity neverEnterEntity : neverEnterEntities) {
                LambdaUpdateWrapper<BuildingRatingEntity> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(BuildingRatingEntity::getId, neverEnterEntity.getId());
                wrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
                wrapper.set(BuildingRatingEntity::getUpdateTime, neverEnterEntity.getUpdateTime());
                buildingRatingService.update(wrapper);
                log.info("客户({} {})入公海时间已置空", neverEnterEntity.getBuildingNo(), neverEnterEntity.getBuildingName());
            }
        }
    }

    private UserVO getOperator(String operateUserCode) {
        UserVO operator;
        operator = RpcUtils.unBox(feignAuthorityRpc.getUserInfo(operateUserCode));
        if (operator == null) {
            throw new ServerException(String.format("操作人%s不存在", operateUserCode));
        }
        return operator;
    }

    @Override
    public List<String> getCities() {
        ResultTemplate<UserDataAccessV2DTO> result = feignCmsRpc.getUserDataAccessV2();
        UserDataAccessV2DTO accessData = result.getData();
        if (Objects.isNull(accessData)) {
            return Collections.emptyList();
        }

        List<Integer> cityIds = accessData.getCityIds();
        if (CollUtil.isEmpty(cityIds)) {
            return Collections.emptyList();
        }

        Map<Integer, String> cityMapping = codeNameHelper.getCityMapping(cityIds);
        List<String> cities = cityMapping.values().stream().sorted(Collator.getInstance(Locale.CHINA)).toList();

        // 公海数据城市列表
        List<String> highSeaCities = buildingRatingService.getHighSeaCities();
        if (CollUtil.isEmpty(highSeaCities)) {
            return cities;
        }

        // 按公海数据城市列表排序
        return sortByReferenceStream(cities, highSeaCities);
    }

    private <T> List<T> sortByReferenceStream(Collection<T> toSort, List<T> reference) {
        // 创建顺序映射
        Map<T, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < reference.size(); i++) {
            orderMap.put(reference.get(i), i);
        }

        return toSort.stream()
                .sorted(Comparator.comparingInt(e -> orderMap.getOrDefault(e, Integer.MAX_VALUE)))
                .toList();
    }

    @Override
    public void sendMessage(String msg, List<BuildingRatingEntity> entities) {
        try {
            // 消息接收人及对应客户名称映射
            Map<String, List<String>> codeNameMapping = Maps.newHashMapWithExpectedSize(entities.size());
            entities.forEach(entity -> {
                if (StrUtil.isBlank(entity.getSubmitUser())) {
                    log.warn("客户{} {}负责人为空，无法发送消息", entity.getBuildingNo(), entity.getBuildingName());
                    return;
                }
                List<String> buildingNames = codeNameMapping.computeIfAbsent(entity.getSubmitUser(), k -> new ArrayList<>());
                buildingNames.add(entity.getBuildingName());
            });

            if (CollUtil.isEmpty(codeNameMapping)) {
                log.warn("客户负责人为空，无法发送消息");
                return;
            }

            List<UserVO> users = RpcUtils.unBox(feignAuthorityRpc.listUserByWnos(codeNameMapping.keySet()));
            if (CollUtil.isEmpty(users)) {
                log.warn("查询客户负责人信息为空，无法发送消息");
                return;
            }

            // 10个用户一组发送消息
            List<List<UserVO>> userPartitions = Lists.partition(users, 10);
            for (List<UserVO> subUsers : userPartitions) {
                UserBatchMessageParam message = new UserBatchMessageParam();
                List<UserMessageContentParam> contents = new ArrayList<>();
                message.setContentParams(contents);
                message.setAppCode(DictCodeConstants.MSG_APP_MEHT);
                message.setModuleCode(DictCodeConstants.MSG_MODULE_HIGH_SEA);
                message.setSendUserId(Optional.ofNullable(UserThreadLocal.getUserId()).orElse(0));

                subUsers.forEach(user -> Optional.ofNullable(codeNameMapping.get(user.getWno()))
                        .ifPresent(buildingNames -> {
                            // 10个客户为一条消息
                            List<List<String>> partitions = Lists.partition(buildingNames, 10);
                            partitions.forEach(subs -> {
                                UserMessageContentParam content = new UserMessageContentParam();
                                content.setContent(String.format(msg, StrUtil.join("，", subs)));
                                content.setReceiveUserId(user.getId());
                                if (user.isInner()) {
                                    // 内部用户需要发送飞书机器人消息
                                    content.setFeiShuFlag(true);
                                }
                                contents.add(content);
                            });
                        }));

                messageSendUtil.sendMessage(message);
            }
        } catch (Exception e) {
            log.error("消息发送失败", e);
        }
    }

    private void sendMessageToCityBusinessHead(List<BuildingRatingEntity> buildingRatings) {
        try {
            // 获取城市负责人
            List<CityVO> cityVos = RpcUtils.unBox(feignCmsRpc.getBusinessHead(buildingRatings.stream()
                    .map(BuildingRatingEntity::getMapCity)
                    .collect(Collectors.toSet())));
            if (CollUtil.isEmpty(cityVos)) {
                log.warn("城市负责人为空，无法发送消息");
                return;
            }

            Map<String, Integer> cityMapping = cityVos.stream().collect(Collectors.toMap(CityVO::getName, CityVO::getBusinessHead));

            // 城市负责人id集合
            Collection<Integer> businessHeads = cityMapping.values();
            List<CodeNameVO> users = RpcUtils.unBox(feignAuthorityRpc.listUserByIds(businessHeads));
            Map<Integer, CodeNameVO> userMapping = users.stream().collect(Collectors.toMap(CodeNameVO::getId, Function.identity()));

            // 组装消息
            UserBatchMessageParam message = new UserBatchMessageParam();
            List<UserMessageContentParam> contents = new ArrayList<>();
            message.setContentParams(contents);
            message.setAppCode(DictCodeConstants.MSG_APP_MEHT);
            message.setModuleCode(DictCodeConstants.MSG_MODULE_HIGH_SEA);
            message.setSendUserId(UserThreadLocal.getUserId());
            buildingRatings.forEach(buildingRating -> {
                // 城市负责人id
                Integer businessHead = cityMapping.get(buildingRating.getMapCity());
                if (Objects.isNull(businessHead)) {
                    log.warn("客户{} {}的城市{}负责人为空，无法发送消息",
                            buildingRating.getBuildingNo(), buildingRating.getBuildingName(), buildingRating.getMapCity());
                    return;
                }

                CodeNameVO user = userMapping.get(businessHead);
                if (Objects.isNull(user)) {
                    log.warn("客户{} {}城市负责人{}信息为空，无法发送消息",
                            buildingRating.getBuildingNo(), buildingRating.getBuildingName(), businessHead);
                    return;
                }

                CachedUser cachedUser = UserThreadLocal.getUser();
                UserMessageContentParam content = new UserMessageContentParam();
                content.setContent(String.format(CLAIM_MSG, buildingRating.getMapCity(), buildingRating.getBuildingName(),
                        cachedUser.getName() + "（" + cachedUser.getWno() + "）"));
                content.setReceiveUserId(user.getId());
                if (user.isInner()) {
                    // 内部用户需要发送飞书机器人消息
                    content.setFeiShuFlag(true);
                }
                contents.add(content);
            });

            messageSendUtil.sendMessage(message);
        } catch (Exception e) {
            log.error("消息发送失败", e);
        }
    }

    private void doTransfer(LocalDateTime enterSeaTime, String enterSeaReason, String userCode, List<BuildingRatingEntity> buildingRatings,
                            BuildingRatingEntity.HighSeaFlagEnum highSeaFlag) {
        if (Objects.isNull(userCode)) {
            return;
        }

        if (CollUtil.isEmpty(buildingRatings)) {
            return;
        }

        boolean isTransferInto = BuildingRatingEntity.HighSeaFlagEnum.YES == highSeaFlag;

        List<String> buildingNos = new ArrayList<>(buildingRatings.size());
        List<BuildingRatingEntity> updateEntities = new ArrayList<>(buildingRatings.size());

        for (BuildingRatingEntity buildingRating : buildingRatings) {
            BuildingRatingEntity updateEntity = new BuildingRatingEntity();
            updateEntity.setId(buildingRating.getId());
            updateEntity.setUpdateTime(buildingRating.getUpdateTime());
            updateEntity.setSubmitUser(userCode);
            updateEntity.setEnterSeaReason(enterSeaReason);
            updateEntity.setHighSeaFlag(highSeaFlag.getCode());
            if (isTransferInto) {
                // 转入公海，设置入公海时间
                updateEntity.setEnterSeaTime(enterSeaTime);
            } else {
                // 转出公海，重置计算时间
                updateEntity.setEnterSeaCalculateTime(LocalDateTime.now());
            }

            updateEntities.add(updateEntity);
            buildingNos.add(buildingRating.getBuildingNo());
        }

        log.info("客户{}{}公海，刷新楼宇，楼宇元数据，商机负责人为{}",
                JSON.toJSONString(buildingNos), isTransferInto ? "入" : "出", StrUtil.isBlank(userCode) ? "空" : userCode);

        buildingRatingService.updateBatchById(updateEntities);

        buildingMetaService.lambdaUpdate()
                .set(BuildingMetaEntity::getManager, userCode)
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNos)
                .update();

        businessOpportunityService.lambdaUpdate()
                .set(BusinessOpportunityEntity::getSubmitUser, userCode)
                .set(BusinessOpportunityEntity::getOwner, userCode)
                .in(BusinessOpportunityEntity::getBuildingNo, buildingNos)
                .update();

        // 完善评级转移
        completeRatingService.transferAndDrop(new TransferAndDropParam(buildingNos, userCode));
    }

    private void logOperate(Integer operatorId, Integer entityId, String functionName, String context) {
        OperateLogDTO log = new OperateLogDTO();
        log.setEntityId(entityId);
        log.setEntityCode(DictCodeConstants.HIGH_SEA_CUSTOMER);
        log.setOperator(operatorId);
        log.setFunctionName(functionName);
        log.setContent(context);
        OperateLogUtils.staticLog(log);
    }

    @Override
    public List<String> responsibilityCheck(String userWno, List<String> businessCodes) {
        log.info("查询商机是否还被被指定用户负责，userWno：{}，businessCodes：{}", userWno, JSON.toJSONString(businessCodes));
        if (CollUtil.isEmpty(businessCodes)) {
            return Collections.emptyList();
        }

        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getCode, businessCodes)
                .eq(BusinessOpportunityEntity::getSubmitUser, userWno)
                .list();

        if (CollUtil.isEmpty(businessOpportunityEntities)) {
            log.info("负责的商机为空");
            // 负责商机为空，表示所有商机都不属于他，直接返回入参
            return businessCodes;
        }

        List<String> codes = businessOpportunityEntities.stream().map(BusinessOpportunityEntity::getCode).toList();
        log.info("负责的商机，codes：{}", JSON.toJSONString(codes));
        // 过滤掉负责的商机
        businessCodes.removeAll(codes);

        // 返回不属于的商机
        return businessCodes;
    }

    @Override
    public void emptyEnterSeaTime(String buildingNo) {
        if (StrUtil.isBlank(buildingNo)) {
            return;
        }

        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery().select(
                        BuildingRatingEntity::getId,
                        BuildingRatingEntity::getUpdateTime,
                        BuildingRatingEntity::getEnterSeaTime)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .one();

        if (Objects.nonNull(ratingEntity) && Objects.nonNull(ratingEntity.getEnterSeaTime())) {
            LambdaUpdateWrapper<BuildingRatingEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(BuildingRatingEntity::getId, ratingEntity.getId());
            wrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
            wrapper.set(BuildingRatingEntity::getUpdateTime, ratingEntity.getUpdateTime());
            buildingRatingService.update(wrapper);
            log.info("客户({} {})入公海时间已置空", ratingEntity.getBuildingNo(), ratingEntity.getBuildingName());
        }
    }

}

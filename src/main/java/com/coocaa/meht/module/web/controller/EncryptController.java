package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.utils.AesUtils;
import com.coocaa.meht.utils.RsaExample;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证加解密
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-31
 */
@Slf4j
@RestController
@RequestMapping("/encrypt")
@Tag(name = "验证加解密", description = "验证加解密")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class EncryptController {
    private final RsaExample rsaExample;

    @Anonymous
    @GetMapping("/rsa")
    public Result<String> rsa(@RequestParam(name = "text") String text) {
        return StringUtils.isBlank(text)
                ? Result.ok()
                : Result.ok(rsaExample.encryptByPublic(text));
    }

    @Anonymous
    @GetMapping("/aes")
    public Result<String> aes(@RequestParam(name = "text") String text) {
        return StringUtils.isBlank(text)
                ? Result.ok()
                : Result.ok(AesUtils.encryptHex(text));
    }
}

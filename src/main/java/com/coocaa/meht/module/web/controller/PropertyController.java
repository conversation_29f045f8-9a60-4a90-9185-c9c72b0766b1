package com.coocaa.meht.module.web.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.dto.property.PersonParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyParam;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyPersonParam;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingPropertyCompanyService;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyPersonService;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyService;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.module.web.vo.CompanyInfoVO;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyPersonVO;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@RestController
@RequestMapping("/property")
@Tag(name = "物业公司", description = "物业公司")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PropertyController {

    private final IPropertyCompanyService companyService;
    private final IPropertyCompanyPersonService companyPersonService;
    private final IBuildingPropertyCompanyService buildingPropertyCompanyService;
    private final IBuildingMetaService buildingMetaService;

    /**
     * 新增物业公司
     */
    @Operation(summary = "新增物业公司")
    @PostMapping("/save")
    public ResultTemplate<Integer> saveCompany(@RequestBody @Validated PropertyCompanyParam param) {
        return ResultTemplate.success(companyService.saveCompany(param));
    }

    @Operation(summary = "新增物业公司下的联系人")
    @PostMapping("/person/save")
    public ResultTemplate<List<PropertyCompanyPersonVO>> saveCompanyPerson(@RequestBody @Validated PersonParam param) {
        return ResultTemplate.success(companyPersonService.savePerson(param));
    }

    @Operation(summary = "物业列表")
    @GetMapping("/company/list")
    public ResultTemplate<List<PropertyCompanyVO>> getCompany(@RequestParam(name = "name", required = false) String name) {
        return ResultTemplate.success(companyService.getCompany(name));
    }

    @Operation(summary = "物业联系人信息")
    @GetMapping("/person/list")
    public ResultTemplate<PropertyCompanyVO> getPerson(@RequestParam(name = "projectCode") String projectCode) {
        return ResultTemplate.success(companyService.getPerson(projectCode));
    }

    @Operation(summary = "根据项目code获取物业信息")
    @GetMapping("/company/project/detail")
    public ResultTemplate<BuildingPropertyCompanyVO> getProjectDetail(@RequestParam(name = "code") String code) {
        List<BuildingPropertyCompanyVO> companyVOS = buildingPropertyCompanyService.getDetail(code, null);
        return ResultTemplate.success(CollectionUtils.isEmpty(companyVOS) ? null : companyVOS.get(0));
    }

    @Operation(summary = "根据楼宇编码获取物业信息")
    @GetMapping("/company/customer/detail")
    public ResultTemplate<List<BuildingPropertyCompanyVO>> getCustomerDetail(@RequestParam(name = "buildingNo") String buildingNo,
                                                                             @RequestParam(name = "code", required = false) String code) {
        return ResultTemplate.success(buildingPropertyCompanyService.getDetail(code, buildingNo));
    }

    @Operation(summary = "根据楼宇编码获取物业信息-根据楼宇去重")
    @GetMapping("/company/customer/distinct/detail")
    public ResultTemplate<List<BuildingPropertyCompanyVO>> getCustomerDetailDistinct(@RequestParam(name = "buildingNo") String buildingNo,
                                                                                     @RequestParam(name = "code", required = false) String code) {
        List<BuildingPropertyCompanyVO> detail = buildingPropertyCompanyService.getDetail(code, buildingMetaService.bc2brr(buildingNo));
        if (CollUtil.isNotEmpty(detail)) {
            detail.forEach(item -> {
                item.setProjectName(null);
                item.setProjectCode(null);
            });
            detail = CollUtil.distinct(detail);
        }
        return ResultTemplate.success(detail);
    }

    @Operation(summary = "物业公司关联商机")
    @PostMapping("/project/save")
    public ResultTemplate<Integer> saveBuildingProperty(@RequestBody @Validated BuildingPropertyCompanyParam param) {
        return ResultTemplate.success(buildingPropertyCompanyService.saveBuildingProperty(param));
    }

    @Operation(summary = "天眼查查询公司信息")
    @GetMapping("/search/list")
    public ResultTemplate<PageResult<CompanyInfoVO>> getCompanyInfoList(@RequestParam(name = "word", required = false) String word,
                                                                        @RequestParam(name = "pageSize", required = false, defaultValue = "20") Integer pageSize,
                                                                        @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum) {
        return ResultTemplate.success(companyService.getCompanyInfoList(word, pageNum, pageSize));
    }

    @Operation(summary = "获取行业末级列表")
    @GetMapping("/industry/second/list")
    public ResultTemplate<List<CodeNameVO>> getSecondIndustry() {
        return ResultTemplate.success(companyService.getSecondIndustry());
    }

    @Operation(summary = "新增物业公司联系人")
    @PostMapping("/contact")
    public ResultTemplate<Integer> saveCompanyContact(@RequestBody @Validated PropertyCompanyPersonParam param) {
        return ResultTemplate.success(companyPersonService.savePerson(param));
    }
}

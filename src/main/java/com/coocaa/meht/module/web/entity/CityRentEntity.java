package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Created by fengke on 2024/11/11.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("city_rent")
public class CityRentEntity extends BaseEntity {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;

    /**
     * 写字楼租金
     */
    private BigDecimal officeRent;

    /**
     * 写字楼日租金
     */
    private BigDecimal officeRentDaily;

    /**
     * 商住楼租金
     */
    private BigDecimal residRent;

    /**
     * 商住楼日租金
     */
    private BigDecimal residRentDaily;

    /**
     * 行政区域编码
     */
    private Integer adCode;
}

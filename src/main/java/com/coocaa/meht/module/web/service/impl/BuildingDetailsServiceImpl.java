package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.dao.BuildingDetailsDao;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BuildingDetailsServiceImpl extends ServiceImpl<BuildingDetailsDao, BuildingDetailsEntity> implements BuildingDetailsService {

    @Override
    public BuildingDetailsEntity getDetailsByBuildingNo(String buildingNo) {
        LambdaQueryWrapper<BuildingDetailsEntity> queryWrapper = new QueryWrapper<BuildingDetailsEntity>().lambda().eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                .last("limit 1");
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BuildingDetailsEntity> listDetailsByBuildingNo(List<String> buildingNoList) {
        LambdaQueryWrapper<BuildingDetailsEntity> queryWrapper = new QueryWrapper<BuildingDetailsEntity>().lambda().in(BuildingDetailsEntity::getBuildingNo, buildingNoList);
        return this.list(queryWrapper);
    }

    @Override
    public BuildingDetailsEntity createDetailsEntity(RatingApplyDto param) {
        BuildingDetailsEntity entity = BuildingDetailsEntity.getEmptyEntity();

        // 各类型楼宇设置对应的评级字段值
        if (Objects.isNull(param.getBuildingType())) {
            // 草稿可能不传buildingType
            entity.setBuildingNumberInput(param.getBuildingNumberInput())
                    .setDailyPriceInput(param.getDailyPriceInput())
                    .setDeliveryDate(param.getDeliveryDate())
                    .setGradeName(param.getGradeName())
                    .setLocationName(param.getLocationName())
                    .setExteriorName(param.getExteriorName())
                    .setLobbyName(param.getLobbyName())
                    .setGarageName(param.getGarageName())
                    .setHallName(param.getHallName())
                    .setBrandName(param.getBrandName())
                    .setRatingName(param.getRatingName())
                    .setSettledName(param.getSettledName())
                    .setTopBrandId(param.getTopBrandId())
                    .setTopBrandName(param.getTopBrandName());
        } else if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() == param.getBuildingType()) {
            entity.setGradeName(param.getGradeName())
                    .setLocationName(param.getLocationName())
                    .setBuildingNumberInput(param.getBuildingNumberInput())
                    .setDailyPriceInput(param.getDailyPriceInput())
                    .setDeliveryDate(param.getDeliveryDate())
                    .setExteriorName(param.getExteriorName())
                    .setLobbyName(param.getLobbyName())
                    .setGarageName(param.getGarageName());
        } else if (BuildingRatingEntity.BuildingType.COMMERCIAL_RESIDENTIAL_BUILDING.getValue() == param.getBuildingType()) {
            entity.setDeliveryDate(param.getDeliveryDate())
                    .setLocationName(param.getLocationName())
                    .setBuildingNumberInput(param.getBuildingNumberInput())
                    .setDailyPriceInput(param.getDailyPriceInput())
                    .setExteriorName(param.getExteriorName())
                    .setHallName(param.getHallName())
                    .setGarageName(param.getGarageName());
        } else if (BuildingRatingEntity.BuildingType.COMPLEX.getValue() == param.getBuildingType()) {
            entity.setBrandName(param.getBrandName())
                    .setLocationName(param.getLocationName())
                    .setRatingName(param.getRatingName())
                    .setTopBrandId(param.getTopBrandId())
                    .setTopBrandName(param.getTopBrandName());
        } else if (BuildingRatingEntity.BuildingType.INDUSTRIAL_PARK.getValue() == param.getBuildingType()) {
            entity.setDeliveryDate(param.getDeliveryDate())
                    .setLocationName(param.getLocationName())
                    .setBuildingNumberInput(param.getBuildingNumberInput())
                    .setSettledName(param.getSettledName())
                    .setExteriorName(param.getExteriorName())
                    .setHallName(param.getHallName());
        } else {
            log.error("暂不支持的楼宇类型：{}", param.getBuildingType());
            throw new ServerException("暂不支持的楼宇类型");
        }

        // 通过交付时间计算到当前时间的楼龄
        if (Objects.nonNull(entity.getDeliveryDate())) {
            int years = getYears(entity.getDeliveryDate());
            entity.setBuildingAgeInput(String.valueOf(years));
            param.setBuildingAgeInput(String.valueOf(years));
        }

        return entity;
    }

    @Override
    public int getYears(LocalDate deliveryDate) {
        // 当前月份第一天
        LocalDate endDay = LocalDate.now().withDayOfMonth(1);
        // 交付日期月份第一天
        LocalDate deliveryDay = deliveryDate.withDayOfMonth(1);

        Period period = Period.between(deliveryDay, endDay);
        // 年份差
        int years = period.getYears();
        if (years < 0) {
            years = 0;
        } else if (period.getMonths() > 0 || period.getDays() > 0) {
            // 有多余的时间也算作一年
            years++;
        }
        return years;
    }

}

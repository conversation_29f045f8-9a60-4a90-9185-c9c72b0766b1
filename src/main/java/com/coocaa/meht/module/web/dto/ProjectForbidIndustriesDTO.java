package com.coocaa.meht.module.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: cheese-meht
 * @ClassName ProjectForbidIndustriesDTO
 * @description:  同步修改项目的禁忌行业
 * @author: zhangbinxian
 * @create: 2025-02-08 14:46
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProjectForbidIndustriesDTO {

    private String buildingNo;

    private String forbidIndustry;

}

package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.service.MessageRecordService;
import com.coocaa.meht.utils.AesUtils;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 飞书事件
 *
 * <AUTHOR>
 * @Date 2024-11-12 10:10
 */
@RestController
@RequiredArgsConstructor
public class FsCallbackController {

    @Value("${fsVerificationToken}")
    private String token;

    private final MessageRecordService messageRecordService;

    @PostMapping("/fs/event")
    @Reqlog(value = "飞书事件", type = Reqlog.LogType.UPDATE)
    public Object event(@RequestBody Map<String, Object> map) {
        if ("url_verification".equals(Converts.toStr(map.get("type")))) {
            if (!Objects.equals(token, Converts.toStr(map.get("token")))) {
                return null;
            }
            return Maps.of("challenge", Converts.toStr(map.get("challenge")));
        }
        Map<?, ?> header = (Map<?, ?>) map.get("header");
        String eventType = Converts.toStr(header.get("event_type"));
        if ("im.message.message_read_v1".equals(eventType)) {
            Map<?, ?> event = (Map<?, ?>) map.get("event");
            List<String> messageIdList = Converts.toList(event.get("message_id_list"), Converts::toStr);
            Map<?, ?> reader = (Map<?, ?>) event.get("reader");
            LocalDateTime readTime = DateUtils.toDateTime(Converts.toLong(reader.get("read_time")));
            if (!CollectionUtils.isEmpty(messageIdList)) {
                messageRecordService.setFsRead(messageIdList, readTime);
            }
        }
        return Result.ok();
    }


    @Anonymous
    @GetMapping("/fs/send/price-apply/notice")
    public Result<String> sendPriceApplyMessage(@RequestBody Map<String, String> map) {
        messageRecordService.sendPriceApplyForMsg(map.get("targetUser"), map.get("applyUserName"), map.get("buildingName"), map.get("buildingArea"), map.get("applyId"));
        return Result.ok();
    }

}

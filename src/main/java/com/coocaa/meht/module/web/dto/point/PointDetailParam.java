package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/14
 */
@Data
public class PointDetailParam {
    @Schema(description = "点位方案id")
    private Integer pointPlanId;
    @Schema(description = "楼栋编码")
    private String buildingNo;

    @Schema(description = "商机编码")
    private String businessCode;

    @Schema
    private List<String> points;
}

package com.coocaa.meht.module.web.dto.crm;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Accessors(chain = true)
public class CrmOwenerUserIdUpdateParamDto {

    private String ownerUserId;

    private Integer transferType = 1;

    private List<String> changeType = Arrays.asList("2");

    private List<String> ids = new ArrayList<>();
}

package com.coocaa.meht.module.web.controller;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.config.aspect.RedisLock;
import com.coocaa.meht.module.web.dto.CustomerTransferIntoDTO;
import com.coocaa.meht.module.web.dto.CustomerTransferOutDTO;
import com.coocaa.meht.module.web.dto.HighSeaCustomerDTO;
import com.coocaa.meht.module.web.service.HighSeaCustomerService;
import com.coocaa.meht.module.web.vo.HighSeaCustomerVO;
import com.coocaa.meht.rpc.FeignMehtWebRpc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Slf4j
@Tag(name = "公海客户管理")
@RestController
@RequestMapping("/high-sea-customer")
public class HighSeaCustomerController {

    @Autowired
    private HighSeaCustomerService highSeaCustomerService;

    @Autowired
    private FeignMehtWebRpc feignMehtWebRpc;

    @PostMapping("/list")
    @Operation(summary = "分页查询公海客户列表")
    public ResultTemplate<PageResult<HighSeaCustomerVO>> list(@RequestBody HighSeaCustomerDTO param) {
        return ResultTemplate.success(highSeaCustomerService.list(param));
    }

    @PostMapping("/transfer-into")
    @Operation(summary = "放入公海")
    public ResultTemplate<Void> transferInto(@RequestBody CustomerTransferIntoDTO param) {
        highSeaCustomerService.transferInto(param);
        return ResultTemplate.success();
    }

    @PostMapping("/transfer-out")
    @Operation(summary = "认领客户")
    @RedisLock(key = "userId")
    public ResultTemplate<Void> transferOut(@RequestBody CustomerTransferOutDTO param) {
        highSeaCustomerService.transferOut(param);
        return ResultTemplate.success();
    }

    @GetMapping("/available-user/count")
    @Operation(summary = "查询剩余客户数")
    public ResultTemplate<Long> getAvailableUserCount() {
        return ResultTemplate.success(RpcUtils.unBox(feignMehtWebRpc.getAvailableUserCount(UserThreadLocal.getUser().getWno())));
    }

    @GetMapping("/cities")
    @Operation(summary = "查询用户有权限的城市，按公海数据量倒叙排列")
    public ResultTemplate<List<String>> getCities() {
        return ResultTemplate.success(highSeaCustomerService.getCities());
    }

    @PostMapping("/refresh/enter-sea-time")
    @Operation(summary = "根据楼宇编号刷新入公海时间")
    public ResultTemplate<Void> refreshEnterSeaTime(@RequestBody List<String> buildingNos) {
        highSeaCustomerService.refreshEnterSeaTime(buildingNos);
        return ResultTemplate.success();
    }

    @PostMapping("/responsibility/check/{userWno}")
    @Operation(summary = "查询商机是否被指定用户负责")
    public ResultTemplate<List<String>> responsibilityCheck(@PathVariable String userWno,
                                                            @RequestBody List<String> businessCodes) {
        return ResultTemplate.success(highSeaCustomerService.responsibilityCheck(userWno, businessCodes));
    }

}

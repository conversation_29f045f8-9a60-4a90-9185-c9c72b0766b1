package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.cos.CosVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.dto.AuditTaskBatchQueryParam;
import com.coocaa.meht.rpc.dto.AuditTaskBatchVO;
import com.coocaa.meht.rpc.dto.AuditTaskQueryParam;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * COS读写
 */
@RestController
@RequestMapping("/cos")
@AllArgsConstructor
@Tag(name = "cos相关接口", description = "cos相关接口")
public class CosController {

    @Autowired
    private final FeignAuthorityRpc feignAuthorityRpc;

    @Anonymous
    @Operation(summary = "获取私有读写桶临时密钥", description = "获取私有读写桶临时密钥")
    @GetMapping("/temp/key/private")
    public Result<CosVO> getPrivateTempKey() {
        ResultTemplate<CosVO> cosVO = feignAuthorityRpc.getPrivateTempKey();
        return Result.ok(cosVO.getData());
    }

    @Anonymous
    @Operation(summary = "获取审核任务信息", description = "获取审核任务信息")
    @PostMapping("/audit/query")
    public Result<Object> logAuditJobId(@RequestBody AuditTaskQueryParam param) {
        ResultTemplate<Object> objectResultTemplate = feignAuthorityRpc.logAuditJobId(param);
        return Result.ok(objectResultTemplate.getData());
    }

    @Anonymous
    @Operation(summary = "批量获取审核任务信息", description = "批量获取审核任务信息")
    @PostMapping("/audit/batch")
    public Result<List<AuditTaskBatchVO>> getAuditBatch(@RequestBody AuditTaskBatchQueryParam param) {
        ResultTemplate<List<AuditTaskBatchVO>> listResultTemplate = feignAuthorityRpc.getAuditBatch(param);
        return Result.ok(listResultTemplate.getData());
    }
}

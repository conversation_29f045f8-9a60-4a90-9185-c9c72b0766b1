package com.coocaa.meht.module.web.dto.convert;

import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2024年12月26 17:11
 */
@Mapper
public interface AgentPersonnelConvert {

//    AgentPersonnelConvert INSTANCE = Mappers.getMapper(AgentPersonnelConvert.class);
//
//
//    /**
//     * Entity 转 DTO
//     */
//    AgentPersonnelDto toDto(AgentPersonnelEntity entity);
//
//    /**
//     *  AddDTO转 Entity
//     */
//    AgentPersonnelEntity addDtoToEntity(AgentPersonnelDto entity);
}

package com.coocaa.meht.module.web.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.handler.EncryptHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 跟进记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "customer_follow_record", autoResultMap = true)
public class CustomerFollowRecordEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 跟进时间
     */
    private LocalDateTime visitTime;
    /**
     * 跟进方式
     */
    private String visitType;
    /**
     * 跟进对象
     */
    private String visitObjects;
    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 跟进目的
     */
    private String visitPurpose;
    /**
     * 跟进结果
     */
    private String visitResult;
    /**
     * 与crm关联id
     */
    private String batchId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 拜访角色
     */
    private String role;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 电话
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String phone;

    /**
     * 是否有效
     */
    private Integer valid;
}

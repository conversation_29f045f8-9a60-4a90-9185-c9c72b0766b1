package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.service.ttc.ITtcService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Ttc
 */
@RestController
@RequestMapping("/ttc")
@AllArgsConstructor
public class TtcController {

    @Resource
    private ITtcService ttcService;

    @GetMapping("/sync")
    @Operation(summary = "同步ttc数据")
    public Result<Boolean> syncTtcData(@RequestParam(name = "startTime", required = false) String startTime,
                                       @RequestParam(name = "endTime", required = false) String endTime) {
        ttcService.syncTtcData(startTime, endTime);
        return Result.ok(true);
    }
}

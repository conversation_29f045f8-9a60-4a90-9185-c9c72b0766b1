package com.coocaa.meht.module.web.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 客户跟进记录-图片关联表
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("follow_record_pic")
public class FollowRecordPicEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 跟进记录id
     */
    private Integer followId;

    /**
     * 图片
     */
    private String picUrl;


}

package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.vo.kanban.StatusChangeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @file IBuildingStatusChangeLog
 * @date 2025/1/2 14:01
 * @description 楼宇/商机状态变更记录
 */

public interface IBuildingStatusChangeLogService extends IService<BuildingStatusChangeLogEntity> {
    List<StatusChangeVO> getBuildingRatingStatusChangeList(StatusChangeVO param);

    List<StatusChangeVO> getProjectStatusChangeList(StatusChangeVO param);

    void deleteLatestLog(Long id, BuildingStatusChangeLogEntity.BizType bizType, BuildingStatusChangeLogEntity.RatingApplicationStatus ratingApplicationStatus);
}

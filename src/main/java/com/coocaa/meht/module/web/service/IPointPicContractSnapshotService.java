package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.PointPicContractSnapshotEntity;

import java.util.List;

/**
 * <p>
 * 点位合同图片快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IPointPicContractSnapshotService extends IService<PointPicContractSnapshotEntity> {

    List<PointPicContractSnapshotEntity> listByPointIds(List<Integer> pointIds);
}
package com.coocaa.meht.module.web.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024年12月18 16:25
 */
@Getter
//@AllArgsConstructor
public enum BuildingMetaImgTypeEnum {

    EXTERIOR_PIC(1, "外墙材料附件地址"),
    LOBBY_PIC(2, "大堂高度及装饰附件地址"),
    HALL_PIC(3, "楼梯厅装饰附件地址"),
    ELEVATOR_PIC(5, "梯厅环境图附件地址"),
    GATE_PIC(6, "闸口图附件地址"),
    INSTALL_PIC(7, "安装示意图附件地址"),
    OTHER_ATTACHMENTS(8, "其他附件地址");
    @Getter
    private final Integer type;
    private final String desc;

    BuildingMetaImgTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static BuildingMetaImgTypeEnum getByType(Integer type) {
        for (BuildingMetaImgTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}

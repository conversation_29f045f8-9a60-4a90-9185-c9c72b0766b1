package com.coocaa.meht.module.web.controller;

import com.alibaba.nacos.common.utils.NumberUtils;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.feishu.FlowNode;
import com.coocaa.meht.module.web.service.approval.impl.PriceApprovalEventService;
import com.coocaa.meht.module.web.service.approval.impl.PriceApprovalService;
import com.coocaa.meht.utils.AesUtils;
import com.lark.oapi.service.approval.v4.model.ApprovalNodeInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 物业合同审批
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/price/approval")
@Tag(name = "物业合同审批", description = "物业合同审批")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PriceApprovalController {


    private final PriceApprovalService priceApprovalService;
    private final PriceApprovalEventService priceApprovalEventService;


    /**
     * 新增审批
     */
    @Operation(summary = "新增审批")
    @GetMapping("/createApproval")
    public ResultTemplate<Integer> createApproval(@RequestParam(name = "contractId", required = true) Integer contractId) {
        priceApprovalService.createPriceApprovalInstance(contractId);
        return ResultTemplate.success();
    }

    /**
     * 查看审批
     */
    @Operation(summary = "查看审批")
    @GetMapping("/getInstance")
    public ResultTemplate<List<FlowNode>> getInstance(@RequestParam(name = "priceApplyId", required = true) String priceApplyId) {
        // 解密
        String plainText = AesUtils.decryptStr(priceApplyId);
        Integer id = StringUtils.isBlank(plainText) ? null : NumberUtils.toInt(plainText);
        List<FlowNode> approvalInstance = priceApprovalService.getApprovalInstance(id);
        return ResultTemplate.success(approvalInstance);
    }

    /**
     * 查看审批定义
     */
    @Operation(summary = "查看审批定义")
    @GetMapping("/getApproveDefinition")
    public ResultTemplate<List<ApprovalNodeInfo>> getApproveDefinition() {
        List<ApprovalNodeInfo> approveDefinition = priceApprovalService.getApproveDefinition();
        return ResultTemplate.success(approveDefinition);
    }

    /**
     * 价格审批通过发送消息
     */
    @Operation(summary = "价格审批通过发送消息")
    @GetMapping("/sendMessage")
    public ResultTemplate<List<ApprovalNodeInfo>> getApproveDefinition(@RequestParam(name = "priceApplyId", required = true) Integer priceApplyId) {
        priceApprovalEventService.sendPointMessage(priceApplyId);
        return ResultTemplate.success();
    }

    /**
     * 模拟飞书回调
     */
    @Operation(summary = "模拟飞书回调")
    @GetMapping("/fs/Manually")
    public ResultTemplate<Integer> fsApprovalManually(@RequestParam(name = "contractId", required = true) Integer contractId, @RequestParam(name = "result", required = true) Integer result) {
        priceApprovalEventService.fsApprovalManually(contractId, result);
        return ResultTemplate.success();
    }

}

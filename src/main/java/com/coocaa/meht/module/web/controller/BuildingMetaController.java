package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.dto.BuildingMetaDetailDto;
import com.coocaa.meht.module.web.dto.BuildingMetaDto;
import com.coocaa.meht.module.web.dto.BuildingMetaUpdateDto;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 楼宇评级
 */
@RestController
@Tag(name = "楼宇主数据相关功能")
@RequestMapping("/buildMeta")
public class BuildingMetaController {

    @Resource
    private IBuildingMetaService buildingMetaService;

    /**
     * 智能获取楼宇信息
     *
     * @param buildingNo
     * @return
     */
    @GetMapping("/getDetail/{buildingNo}")
    @Operation(summary = "楼宇主数据详情")
    public Result<BuildingMetaDetailDto> getDetail(@PathVariable(name = "buildingNo") String buildingNo) {
        return Result.ok(buildingMetaService.getDetail(buildingNo));
    }

    @GetMapping("/basic-info/{buildingNo}")
    @Operation(summary = "楼宇基本数据详情")
    public Result<BuildingMetaDto> basicInfo(@PathVariable(name = "buildingNo") String buildingNo) {
        BuildingMetaDto result = buildingMetaService.getBasicInfo(buildingNo);
        return Result.ok(result);
    }

    /**
     * 智能获取楼宇信息
     *
     * @param dto
     * @return
     */
    @PutMapping("/updateBuildMeta")
    @Operation(summary = "楼宇主数据编辑")
    public Result updateBuildMeta(@Validated @RequestBody BuildingMetaUpdateDto dto) {
        return buildingMetaService.updateBuildMeta(dto) ? Result.ok() : Result.error("编辑楼宇主数据失败");
    }

    /**
     * todo已迁移待删除
     */
    @GetMapping("/no")
    @Operation(summary = "获取楼宇主数据no", hidden = true)
    @Anonymous
    public Result<String> getBuildMeta() {
        return Result.ok(buildingMetaService.getBuildingMetaNo());
    }

    @GetMapping("/meta-pic/{mapNo}")
    @Anonymous
    @Operation(summary = "根据mapNo查询楼宇图片  [buildingExteriorPic外墙材料附件地址 buildingLobbyPic大堂高度及装饰附件地址 buildingHallPic楼梯厅装饰附件地址]")
    public Result<Map<String, List<BuildingMetaImgRelationEntity>>> metePic(@PathVariable(name = "mapNo") String mapNo) {
        return Result.ok(buildingMetaService.metaPic(mapNo, List.of(1, 2, 5)));
    }

}

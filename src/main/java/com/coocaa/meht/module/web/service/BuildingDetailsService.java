package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;

import java.time.LocalDate;
import java.util.List;

public interface BuildingDetailsService extends IService<BuildingDetailsEntity> {

    BuildingDetailsEntity getDetailsByBuildingNo(String buildingNo);

    List<BuildingDetailsEntity> listDetailsByBuildingNo(List<String> buildingNoList);

    BuildingDetailsEntity createDetailsEntity(RatingApplyDto param);

    int getYears(LocalDate deliveryDate);

}

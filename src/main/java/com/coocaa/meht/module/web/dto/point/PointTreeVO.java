package com.coocaa.meht.module.web.dto.point;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "点位树形结构")
public class PointTreeVO {

    @Schema(description = "等候厅id")
    private Integer waitingHallId;

    private Integer pointId;

    @Schema(description = "点位图片列表")
    private List<String> images;

    private String deviceSize;
    @Schema(description = "设备尺寸")
    private String deviceSizeName;

    @Schema(description = "方案描述")
    private String description;

    @Schema(description = "节点名称")
    private String name;
    
    @Schema(description = "节点类型: building/unit/floor/waitingHall/point")
    private String type;
    
    @Schema(description = "子节点")
    private List<PointTreeVO> children;
    
    @Schema(description = "点位数量")
    private int count;



} 
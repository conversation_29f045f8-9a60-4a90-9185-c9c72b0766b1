package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 看板下标签
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-27
 */
@Data
@Accessors(chain = true)
public class TabVO {
    /**
     * 主标题
     */
    @Schema(description = "主标题", type = "String", example = "已认证楼宇")
    private String mainTitle;

    /**
     * 子标题
     */
    @Schema(description = "子标题", type = "String", example = "-")
    private String subTitle;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示", type = "Boolean", example = "true")
    private boolean show;

    /**
     * 指标分组
     */
    @Schema(description = "指标分组", type = "List<SectionVO>")
    private List<SectionVO> sections;
}

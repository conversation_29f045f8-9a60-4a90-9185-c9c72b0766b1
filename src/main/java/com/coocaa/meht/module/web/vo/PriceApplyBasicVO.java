package com.coocaa.meht.module.web.vo;

import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.vo.common.ConfigVO;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@Data
public class PriceApplyBasicVO {

    /**
     * 校验商机是否还属于用户
     */
    private BusinessOpportunityEntity businessOpportunityEntity;

    /**
     * 城市水位价
     */
    private ConfigVO cityWaterMarkPrice;

    /**
     * 价格申请对象
     */
    private PriceApplyEntity priceApplyEntity;

    /**
     * 价格申请设备对象
     */
    private List<PriceApplyDeviceEntity> priceApplyDeviceList;

    /**
     * 价格申请设备点对象
     */
    private List<PriceApplyDevicePointEntity> priceApplyDevicePointList;

}

package com.coocaa.meht.module.web.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024-11-08 15:19
 */
@Data
public class AgentPersonnelDto {

    private Long id;

    // 员工编码
    @NotBlank(message = "员工编码不能为空")
    private String empCode;

    // 员工姓名
    @NotBlank(message = "员工姓名不能为空")
    private String empName;

    // 员工手机号码
    @NotBlank(message = "员工手机号码不能为空")
    private String empMobile;

    // 代理商账号
    @NotBlank(message = "代理商账号不能为空")
    private String agentCode;

    // 代理商名称
    @NotBlank(message = "代理商名称不能为空")
    private String agentName;

    @TableLogic(value = "0", delval = "1")
    private Integer status;
}

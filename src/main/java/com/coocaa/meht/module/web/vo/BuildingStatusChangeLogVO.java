package com.coocaa.meht.module.web.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-07
 */

@Data
public class BuildingStatusChangeLogVO {
    /**
     * ID
     */
    private Long id;

    /**
     * 数据类型
     */
    private String type;

    /**
     * 数据类型子类型
     */
    private Integer subType;

    /**
     * 业务ID (楼宇,客户,商机,...)
     */
    private Long bizId;

    /**
     * 业务编码 (楼宇,客户,商机,...)
     */
    private String bizCode;

    /**
     * 业务状态(字典xx)
     */
    private String status;

    /**
     * 状态变更时间
     */
    private String changeTime;

    /**
     * 状态变更操作人
     */
    private Long operator;

    /**
     * 状态变更操作人工号
     */
    private String operatorWno;

    /**
     * 状态变更操作人姓名
     */
    private String operatorName;

    /**
     * 补充内容
     */
    private String content;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Byte deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}

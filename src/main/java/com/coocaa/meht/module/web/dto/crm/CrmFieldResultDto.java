package com.coocaa.meht.module.web.dto.crm;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CrmFieldResultDto  extends CrmResultDto{

    private List<DataDto> data;


    @Data
    @Accessors(chain = true)
    public static class DataDto {

        private Integer fieldType;

        private String fieldName;

        private String name;

        private Integer type;

        private String fieldId;

        private Object value;
    }
}

package com.coocaa.meht.module.web.dao;

import com.coocaa.meht.module.dataimport.pojo.PriceApplyPointDTO;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.vo.PriceApplyDevicePointJoinVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 价格申请 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Mapper
public interface PriceApplyDao extends BaseMapper<PriceApplyEntity> {
    /**
     * 根据点位code获取设备激励金数据
     */
    List<PriceApplyDevicePointJoinVO> selectIncentivePrice(@Param("pointCodes") Collection<String> pointCodes);

    List<PriceApplyPointDTO> selectIncentivePriceByApplyCode();


}

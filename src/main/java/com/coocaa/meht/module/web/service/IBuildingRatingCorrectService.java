package com.coocaa.meht.module.web.service;

import java.io.File;
import java.util.List;

/**
 * 楼宇评级修正服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 13:45
 */
public interface IBuildingRatingCorrectService {
    /**
     * 重新生成AI评分过程数据
     */
    Boolean aiScoreDetail(List<String> buildingNos);

    /**
     * 根据已有AI评分项，重新计算并更新AI评分
     */
    Boolean aiScoreUpdate(List<String> buildingNos);

    /**
     * 按城市导出AI评分详情信息
     */
    String aiScoreExport(List<String> cityNames);

    /**
     * 获取AI评分上传文件
     */
    File getAiScoreUploadFile(List<String> cityNames);

    /**
     * 根据已有的过程详情数据再次计算AI单项分
     */
    Boolean aiScoreDetailRecalculate(List<String> buildingNos, Integer batch);
}

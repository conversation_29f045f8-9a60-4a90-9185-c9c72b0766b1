/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 楼宇基因控制器
 * 提供楼宇基因相关的RESTful API接口
 * 处理楼宇基因信息的增删改查请求
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Tag(name = "楼宇基因管理", description = "楼宇基因相关接口")
@RestController
@RequestMapping("/building-gene")
@RequiredArgsConstructor
public class BuildingGeneController {

    private final BuildingGeneService buildingGeneService;

    private final BuildingScreenService buildingScreenService;



    /**
     * 新增楼宇基因信息
     * 接收楼宇基因信息并保存到数据库
     *
     * @param buildingGeneEntity 楼宇基因实体对象
     */
    @Operation(summary = "新增楼宇基因", description = "创建新的楼宇基因信息")
    @PostMapping
    public Result<Boolean> save(@RequestBody BuildingGeneEntity buildingGeneEntity) {
        return Result.ok(buildingGeneService.save(buildingGeneEntity));
    }

    /**
     * 更新楼宇基因信息
     * 根据ID更新楼宇基因信息
     *
     * @param buildingGeneEntity 楼宇基因实体对象
     */
    @Operation(summary = "修改楼宇基因", description = "更新已存在的楼宇基因信息")
    @PutMapping
    public Result<Boolean> update(@RequestBody BuildingGeneEntity buildingGeneEntity) {
        if (CollectionUtils.isNotEmpty(buildingGeneEntity.getCompetitiveMediaInfos())) {
            String mediaInfo = String.join(",", buildingGeneEntity.getCompetitiveMediaInfos());
            buildingGeneEntity.setCompetitiveMediaInfo(mediaInfo);
        }
        return Result.ok(buildingGeneService.updateById(buildingGeneEntity));
    }

    /**
     * 删除楼宇基因信息
     * 根据ID删除指定的楼宇基因信息
     *
     * @param id 楼宇基因ID
     */
    @Operation(summary = "删除楼宇基因", description = "删除指定的楼宇基因信息")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Integer id) {
        return Result.ok(buildingGeneService.removeById(id));
    }

    /**
     * 获取楼宇基因详情
     * 根据ID查询楼宇基因的详细信息
     *
     * @param id 楼宇基因ID
     * @return 楼宇基因实体对象
     */
    @Operation(summary = "获取楼宇基因详情", description = "获取指定ID的楼宇基因详细信息")
    @GetMapping("/{id}")
    public Result<BuildingGeneVO> getById(@PathVariable Integer id) {
        return Result.ok(buildingGeneService.detail(id));
    }

    /**
     * 根据楼宇编码查询基因信息
     */
    @Operation(summary = "根据楼宇编码查询基因信息", description = "根据楼宇编码查询基因信息")
    @PostMapping("/by-no")
    public Result<List<BuildingGeneEntity>> getByNo(@RequestBody @NotEmpty(message = "楼宇编码列表不能为空") List<String> buildingRatingNos) {
        List<BuildingScreenEntity> screenEntities = buildingScreenService.lambdaQuery()
                .select(BuildingScreenEntity::getId,
                        BuildingScreenEntity::getBuildingRatingNo,
                        BuildingScreenEntity::getSubmitCoefficient,
                        BuildingScreenEntity::getFinalCoefficient)
                .in(BuildingScreenEntity::getBuildingRatingNo, buildingRatingNos)
                .list();

        return Result.ok(screenEntities.stream().map(screenEntity -> {
            BuildingGeneEntity geneEntity = new BuildingGeneEntity();
            geneEntity.setId(screenEntity.getId());
            geneEntity.setBuildingRatingNo(screenEntity.getBuildingRatingNo());
            geneEntity.setSubmitCoefficient(screenEntity.getSubmitCoefficient());
            geneEntity.setFinalCoefficient(screenEntity.getFinalCoefficient());
            return geneEntity;
        }).toList());
    }

}
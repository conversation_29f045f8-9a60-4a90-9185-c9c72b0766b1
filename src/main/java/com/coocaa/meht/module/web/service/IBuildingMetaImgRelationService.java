package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingMetaImgListDto;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;

/**
 * <p>
 * 楼宇主数据图片管理信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IBuildingMetaImgRelationService extends IService<BuildingMetaImgRelationEntity> {
    /**
     * 根据楼宇编码获取楼宇图片信息
     * @param buildingNo
     * @return
     */
    BuildingMetaImgListDto getImgByBuildingNo(String buildingNo);
}

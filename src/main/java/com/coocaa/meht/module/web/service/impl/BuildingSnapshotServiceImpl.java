package com.coocaa.meht.module.web.service.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.web.dao.BuildingSnapshotMapper;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingSnapshotServiceImpl extends ServiceImpl<BuildingSnapshotMapper, BuildingSnapshotEntity> implements BuildingSnapshotService {

    private final BuildingRatingService buildingRatingService;

    private final BuildingDetailsService buildingDetailsService;

    private final BuildingScreenService buildingScreenService;


    @Override
    public void save(String buildingNo, BuildingSnapshotEntity.Type type) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .one();

        BuildingDetailsEntity detailsEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                .one();

        BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingNo)
                .one();

        BuildingSnapshotEntity snapshotEntity = new BuildingSnapshotEntity();
        snapshotEntity.setBuildingRatingNo(buildingNo);
        snapshotEntity.setType(type.getValue());
        snapshotEntity.setRatingVersion(ratingEntity.getRatingVersion());
        snapshotEntity.setRatingSnapshot(JSON.toJSONString(ratingEntity, JSONWriter.Feature.WriteMapNullValue));
        snapshotEntity.setDetailsSnapshot(Objects.nonNull(detailsEntity) ? JSON.toJSONString(detailsEntity, JSONWriter.Feature.WriteMapNullValue) : "");
        snapshotEntity.setScreenSnapshot(Objects.nonNull(screenEntity) ? JSON.toJSONString(screenEntity, JSONWriter.Feature.WriteMapNullValue) : "");
        snapshotEntity.setMetaSnapshot("");

        BuildingSnapshotEntity existSnapshotEntity = lambdaQuery().eq(BuildingSnapshotEntity::getBuildingRatingNo, buildingNo)
                .eq(BuildingSnapshotEntity::getType, type.getValue())
                .eq(BuildingSnapshotEntity::getBuildingRatingNo, ratingEntity.getBuildingNo())
                .eq(BuildingSnapshotEntity::getRatingVersion, ratingEntity.getRatingVersion())
                .one();

        if (Objects.nonNull(existSnapshotEntity)) {
            snapshotEntity.setId(existSnapshotEntity.getId());
            updateById(snapshotEntity);
        } else {
            save(snapshotEntity);
        }
    }

}

package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.entity.CityRentEntity;
import com.coocaa.meht.module.web.service.CityRentService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租金
 */
@RestController
@RequestMapping("/rent")
@AllArgsConstructor
public class CityRentController {

    @Resource
    private CityRentService rentService;

    @GetMapping("/getRent")
    @Reqlog(value = "城市租金", type = Reqlog.LogType.SELECT)
    public Result<CityRentEntity> list(String adCode) {
        CityRentEntity cityRentEntity = rentService.getRent(adCode);
        return Result.ok(cityRentEntity);
    }
}

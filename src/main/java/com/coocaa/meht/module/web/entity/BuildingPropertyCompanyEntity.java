package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import org.apache.kafka.common.protocol.types.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@TableName(value = "building_property_company", autoResultMap = true)
public class BuildingPropertyCompanyEntity implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 物业公司id
     */
    private Integer propertyId;

    /**
     * 物业公司名称
     */
    private String propertyName;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String  createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String  updateBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

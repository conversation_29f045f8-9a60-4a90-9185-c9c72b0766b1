package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.WaitingHallBusinessEntity;

import java.util.List;

/**
 * @program: cheese-meht
 * @ClassName WaitingHallBusinessService
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-15 15:28
 * @Version 1.0
 **/
public interface WaitingHallBusinessService extends IService<WaitingHallBusinessEntity> {

    void deleteBusinessCode(String businessCode);

    List<Integer> findByBusinessCode(String businessCode);

    void deleteByWaitingIds(List<Integer> waitingIds);
}

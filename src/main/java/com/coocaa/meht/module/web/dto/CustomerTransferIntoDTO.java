package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Data
public class CustomerTransferIntoDTO {

    @Schema(description = "客户id集合")
    @NotEmpty(message = "客户不能为空")
    private List<Long> ids;

    @Schema(description = "放入公海理由")
    @NotBlank(message = "放入公海理由不能为空")
    private String enterSeaReason;

    /**
     * web客户管理批量放入公海功能使用
     */
    @Schema(description = "批量操作标识", hidden = true)
    private Boolean bachOperateFlag = false;

}

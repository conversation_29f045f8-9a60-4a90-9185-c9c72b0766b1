package com.coocaa.meht.module.web.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/28
 * @description 楼宇状态
 */
@Data
public class BuildingStatusVO {

    @Schema(description = "楼宇id")
    private Long id;

    @Schema(description = "商机状态")
    private String businessStatus = "";

    @Schema(description = "合同状态")
    private String contractStatus = "";

    @Schema(description = "公海数据标识：0-否，1-是")
    private Integer highSeaFlag;

}

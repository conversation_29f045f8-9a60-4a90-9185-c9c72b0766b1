package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 指标
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
@Data
@Accessors(chain = true)
public class IndexVO {
    @Schema(description = "指标key", type = "String", example = "approving")
    private String key;

    @Schema(description = "指标名称", type = "String", example = "审批中")
    private String name;

    @Schema(description = "指标值1", type = "String", example = "30")
    private String value1;

    @Schema(description = "指标值2", type = "String", example = "30")
    private String value2;

    @Schema(description = "是否显示（支持权限控制）", type = "Boolean", example = "true")
    private boolean show;

    @Schema(description = "主要指标", type = "Boolean", example = "true")
    private boolean main;

    @Schema(description = "提示信息", type = "String", example = "截止到统计时间，审批中的合同申请单个数")
    private String tips;

    @Schema(description = "单位类型 [number:数值, percent:百分比]", type = "String", example = "number")
    private String unitType;
}

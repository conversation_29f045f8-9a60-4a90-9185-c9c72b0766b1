package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.dto.AgentPersonnelDto;
import com.coocaa.meht.module.web.entity.PersonnelApprovalEntity;
import com.coocaa.meht.module.web.service.AgentPersonnelService;
import com.coocaa.meht.module.web.service.PersonnelApprovalService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.naming.CommunicationException;

/**
 * 代理商审批
 */
@RestController
@RequestMapping("/approval")
@AllArgsConstructor
public class AgentPersonnelController {

    @Resource
    private PersonnelApprovalService personnelApprovalService;

    @Resource
    private AgentPersonnelService agentPersonnelService;


    @Anonymous
    @GetMapping("/getApproval")
    @Reqlog(value = "审批名单", type = Reqlog.LogType.SELECT)
    public Result<PersonnelApprovalEntity> list(String empCode) {
        PersonnelApprovalEntity personnelApprovalEntity = personnelApprovalService.getApprovalByCode(empCode);
        return Result.ok(personnelApprovalEntity);
    }

    @Anonymous
    @PostMapping("/addAgentPersonnel")
    @Reqlog(value = "添加代理商", type = Reqlog.LogType.INSERT)
    public Result<PersonnelApprovalEntity> addAgentPersonnel(@RequestBody AgentPersonnelDto dto) throws CommunicationException {
        boolean status = agentPersonnelService.addAgentPersonnel(dto);
        return status ? Result.ok() : Result.error();
    }

    @Anonymous
    @PostMapping("/updateStatus")
    @Reqlog(value = "修改代理商状态", type = Reqlog.LogType.UPDATE)
    public Result<PersonnelApprovalEntity> updateStatus(@RequestBody AgentPersonnelDto dto) throws CommunicationException {
        boolean status = agentPersonnelService.updateStatus(dto);
        return status ? Result.ok() : Result.error();
    }
}

package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 评论创建请求
 * @since 2025-04-29
 */
@Data
public class CommentCreateDTO implements Serializable{

    /**
     * 业务类型（1楼宇、2价格申请）
     */
    @NotNull(message = "业务类型不能为空")
    @Schema(description = "业务类型（1楼宇、2价格申请）")
    private Integer businessType;

    /**
     * 业务ID
     */

    @NotBlank(message = "业务ID不能为空")
    @Schema(description = "业务ID")
    private String businessId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Schema(description = "评论内容")
    private String content;

    /**
     * 通知用户列表
     */
    @Schema(description = "通知用户列表")
    private List<String> notifiedUsers;

    /**
     * 附件url列表
     */
    @Schema(description = "附件url列表")
    private List<String> attachmentIds;

    private String sender;

    @Schema(description = "版本号")
    private String version;
} 
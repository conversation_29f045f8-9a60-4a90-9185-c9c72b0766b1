package com.coocaa.meht.module.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-07-21
 */
@Data
@Builder
public class BuildingRating2WorkOrderVO {

    @Schema(description = "楼宇编号-BC")
    private String buildingNo;

    @Schema(description = "楼宇编号-BRR")
    private String buildingRatingNo;

    @Schema(description = "楼宇名称")
    private String buildingName;
    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型")
    private Integer buildingType;

    @Schema(description = "楼宇类型名称")
    private String buildingTypeName;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "跟进人")
    private String followerName;

    @Schema(description = "跟进人电话")
    private String followerPhone;

    private Boolean installBig = false;


}

package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PointPicVo;
import com.coocaa.meht.module.web.dto.point.*;
import com.coocaa.meht.module.web.entity.PointEntity;

import java.util.List;

public interface PointService extends IService<PointEntity> {
    void addPoint(PointDTO pointAddDTO);

    void updatePoint(PointDTO pointAddDTO);

    void handlePoint(PointDTO pointAddDTO);

    ProjectPointVO listBuildingPoint(PointDetailParam param);

    ProjectPointVO listPriceApplyPoint(List<String> pointCodes,String buildingNo);

    void deleteById(Integer pointId);

    void deleteByWaitingHallId(Integer waitingHallId);

    void deleteBusinessCode(String businessCode);

    void batchDeleteByWaitingHallIds(List<Integer> waitingHallIds);

    List<String> getWaitingHallPointInformation(List<Integer> priceApplyDeviceIds);

    List<CodeNameVO> listPointToContract(String buildingNo,String businessCode);

    String pptPoint(PointDetailParam param);

    String workOrderPpt(List<WorkOrderPointQueryDTO> param);


    void cleaningWaitingHallBusiness(String buildingRatingNo, String businessCode);

    void deleteByPointIds(List<String> pointCodes);

    WaitingHallPointVO findWaitingHallAndPoint();

    List<PointPicVo> findPointPicList();

    List<String> getBui();

    ProjectPointVO listPointByBuildingNo(String buildingNo);
}
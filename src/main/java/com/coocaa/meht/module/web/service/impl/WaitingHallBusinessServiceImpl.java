package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.WaitingHallBusinessMapper;
import com.coocaa.meht.module.web.entity.WaitingHallBusinessEntity;
import com.coocaa.meht.module.web.service.WaitingHallBusinessService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: cheese-meht
 * @ClassName WaitingHallBusinessServiceImpl
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-15 15:30
 * @Version 1.0
 **/
@Service
public class WaitingHallBusinessServiceImpl extends ServiceImpl<WaitingHallBusinessMapper, WaitingHallBusinessEntity> implements WaitingHallBusinessService {

    @Override
    public void deleteBusinessCode(String businessCode) {
        LambdaQueryWrapper<WaitingHallBusinessEntity> queryWrapper = Wrappers.<WaitingHallBusinessEntity>lambdaQuery().eq(WaitingHallBusinessEntity::getBusinessCode, businessCode);
        remove(queryWrapper);
    }

    @Override
    public List<Integer> findByBusinessCode(String businessCode) {
        LambdaQueryWrapper<WaitingHallBusinessEntity> queryWrapper = Wrappers.<WaitingHallBusinessEntity>lambdaQuery()
                .eq(WaitingHallBusinessEntity::getBusinessCode, businessCode)
                .select(WaitingHallBusinessEntity::getWaitingHallId);;
        return list(queryWrapper).stream().map(WaitingHallBusinessEntity::getWaitingHallId).collect(Collectors.toList());
    }

    @Override
    public void deleteByWaitingIds(List<Integer> waitingIds) {
        LambdaQueryWrapper<WaitingHallBusinessEntity> queryWrapper = Wrappers.<WaitingHallBusinessEntity>lambdaQuery().in(WaitingHallBusinessEntity::getWaitingHallId, waitingIds);
        remove(queryWrapper);
    }
}

package com.coocaa.meht.module.web.vo.kanban;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @file KanbanDeviceStatisticsVO
 * @date 2025/1/14 14:30
 * @description 设备统计数据
 */

@Data
@Accessors(chain = true)
public class KanbanDeviceStatisticsVO<T> {

    @Schema(description = "数据更新时间", type = "LocalDate", example = "2025-01-14")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate latestUpdateDate;

    @Schema(description = "设备数据", type = "list")
    private List<BaseDataItemVO<T>> deviceDataList;

    @Schema(description = "转化率", type = "list")
    private List<DataItemVO<T>> ratioData;

}

package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("point_pic")
public class PointPicEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer pointId;
    private String pic;
    private String createBy;
    private LocalDateTime createTime;
} 
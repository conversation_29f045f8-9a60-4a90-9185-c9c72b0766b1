package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.api.map.MapApiService;
import com.coocaa.meht.module.web.dto.CityAddressDto;
import com.coocaa.meht.module.web.dto.MapAddressDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * 百度地图
 */
@RestController
@RequestMapping("/map")
@AllArgsConstructor
public class MapController {

    private final MapApiService mapApiService;

    @GetMapping("/list")
    @Reqlog(value = "地图列表", type = Reqlog.LogType.SELECT)
    public Result<Map<String, Object>> list(String query, String region, String city_limit, String output,String tag) throws UnsupportedEncodingException {
        Map<String, Object> mapJson = mapApiService.getJsonMap(query,region,city_limit,output,tag);
        return Result.ok(mapJson);
    }


    @GetMapping("/getAccurate")
    @Reqlog(value = "获取准信地址", type = Reqlog.LogType.SELECT)
    public Result<Map<String, Object>> getAccurate(String latitude,String longitude,String extensions_poi,String poi_types) {
        Map<String, Object> mapJson = mapApiService.getAccurate(latitude,longitude,extensions_poi,poi_types);
        return Result.ok(mapJson);
    }

    @Anonymous
    @GetMapping("/getConvertAddress")
    @Reqlog(value = "地址转换", type = Reqlog.LogType.SELECT)
    public Result<MapAddressDto> getConvertAddress(String latitude, String longitude) throws JsonProcessingException {
        MapAddressDto mapJson = mapApiService.getConvertAddress(latitude,longitude);
        return Result.ok(mapJson);
    }


    @Anonymous
    @GetMapping("/getCity")
    @Reqlog(value = "地址转换", type = Reqlog.LogType.SELECT)
    public Result<CityAddressDto> getCity(String query, String region) throws JsonProcessingException {
        CityAddressDto mapJson = mapApiService.getCityId(query,region);
        return Result.ok(mapJson);
    }
}

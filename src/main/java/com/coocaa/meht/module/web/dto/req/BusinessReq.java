package com.coocaa.meht.module.web.dto.req;


import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/7
 */
@Data
public class BusinessReq {
    /**
     * 客户id
     */
    @Schema(description = "客户id", type = "String", example = "1xxxxx")
    private String customerId;
    /**
     * 客户名称
     */
    @Schema(description = "客户名称", type = "String", example = "xx客户")
    private String customerName;
    /**
     * 楼宇编号
     */
    @Schema(description = "楼宇编号", type = "String", example = "1")
    private String buildingNo;
    /**
     * 商机名称
     */
    @Schema(description = "商机名称", type = "String", example = "xx商机")
    private String name;
    /**
     * 商机code
     */
    @Schema(description = "商机code", type = "String", example = "BCxxxx01")
    private String code;

    /**
     * 商机id
     */
    @Schema(description = "crm商机id", type = "String", example = "13343xxxxxx")
    private String businessId;

    /**
     * 商机随机编码
     */
    @Schema(description = "商机随机编码", type = "String", example = "xxxxxxxxxx")
    private String batchId;

    /**
     * 商机组类型ID
     */
    @Schema(description = "商机组类型ID", type = "String", example = "1")
    private String typeId;


    /**
     * 物业信息
     */
    @Schema(description = "物业信息id", type = "Integer", example = "1")
    private Integer propertyId;
}

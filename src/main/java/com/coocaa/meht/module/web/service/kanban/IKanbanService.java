package com.coocaa.meht.module.web.service.kanban;

import com.coocaa.meht.module.web.vo.kanban.CityStatisticsItemVO;
import com.coocaa.meht.module.web.vo.kanban.DataAccessVO;

import com.coocaa.meht.module.web.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanPointStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @file IKanbanService
 * @date 2025/1/2 15:25
 * @description 媒资平台 - 数据看板
 */
public interface IKanbanService {
    /**
     * 获取合同统计信息
     */
    KanbanVO getContractStatusInfo(Integer cityId);

    /**
     * 楼宇 - 总量统计
     */
    void buildingTotalStatistics();

    /**
     * 获取楼宇总量统计信息
     */
    KanbanVO getBuildingTotalStatisticsInfo(Integer cityId);

    /**
     * 楼宇 - 新增统计
     */
    void buildingIncrementStatistics();

    /**
     * 获取楼宇新增统计信息
     */
    KanbanVO getBuildingIncrementStatisticsInfo(Integer cityId);

    void executeRatioStatistics(LocalDateTime date);

    /**
     * 获取用户权限信息
     */
    DataAccessVO getUserDataAccess();

    /**
     * 获取合同点位统计信息
     *
     * @param date
     * @param cityId
     * @return
     */
    KanbanPointStatisticsVO getPointStatusInfo(LocalDate date, Integer days, Integer cityId);

    /**
     * 获取楼宇和项目（商机）转化率信息
     * @param cityId
     */
    CityStatisticsItemVO getBuildingStatisticsRatioInfo(Integer cityId);

    /**
     * @Author：TanJie
     * @Date：2025-01-17 15:37
     * @Description：获取设备统计数据
     */
    KanbanDeviceStatisticsVO getDeviceStatisticsInfo(Integer cityId, LocalDate date);
}

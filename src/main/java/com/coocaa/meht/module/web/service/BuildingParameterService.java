package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.service.impl.BuildingParameterServiceImpl;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface BuildingParameterService extends IService<BuildingParameterEntity> {

    List<BuildingTypesDto> getBuildingType(String buildingNo);

    List<BuildingTypesDto> getParameterBuildingType(Integer  dataVersion);

    Map<String,BuildingParameterEntity> getByMapIds(List<Long> ids);

    /**
     * 获取参数得分和权重
     * @param dataFlag  版本号
     * @param buildingType  楼宇类型
     * @param ruleId    规则id
     * @param parameterCode 参数编码
     * @return
     */
    BuildingParameterEntity.ScoreAndWeight<BigDecimal> getScoreAndWeight(Integer dataFlag, Integer buildingType, Long ruleId, String parameterCode);

    /**
     * 刷新规则缓存
     */
    Boolean refreshCache();
}

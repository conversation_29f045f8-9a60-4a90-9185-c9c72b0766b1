package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞书单选控件值
 */
@Getter
@AllArgsConstructor
public enum FeiShuFormRatioValueEnum implements IEnumType<String> {
    YES("m4mha171-i4wqwrp3iu-0", "是"),
    NO("m4mha171-vv9ekgslw8m-0", "否");

    private final String code;
    private final String desc;

    private final static Map<String, FeiShuFormRatioValueEnum> BY_CODE_MAP =
            Arrays.stream(FeiShuFormRatioValueEnum.values())
                    .collect(Collectors.toMap(FeiShuFormRatioValueEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static FeiShuFormRatioValueEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static FeiShuFormRatioValueEnum parse(String code, FeiShuFormRatioValueEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(FeiShuFormRatioValueEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}

package com.coocaa.meht.module.web.vo.kanban;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 看板下分组
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-27
 */
@Data
@Accessors(chain = true)
public class SectionVO {
    /**
     * 主标题
     */
    @Schema(description = "主标题", type = "String", example = "已认证楼宇")
    private String mainTitle;

    /**
     * 子标题
     */
    @Schema(description = "子标题", type = "String", example = "-")
    private String subTitle;

    /**
     * 每行显示指标数量
     */
    @Schema(description = "每行显示指标数量", type = "Integer", example = "4")
    private Integer showCount;


    /**
     * 数据类型: 指标(INDEX)、曲线(CURVE)、转化率(CONVERT_RATE)
     */
    @Schema(description = "数据类型: 指标(INDEX)、曲线(CURVE)、转化率(CONVERT_RATE)", type = "String", example = "INDEX")
    private String dataType;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息", type = "String", example = "这是提示信息")
    private String tips;

    /**
     * 是否显示
     */
    @Schema(description = "是否显示", type = "Boolean", example = "true")
    private boolean show;

    /**
     * 指标列表
     */
    @Schema(description = "指标列表", type = "List<IndexVO>")
    private List<IndexVO> indices;
}

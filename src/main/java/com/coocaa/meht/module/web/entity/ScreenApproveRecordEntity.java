package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "screen_approve_record", autoResultMap = true)
public class ScreenApproveRecordEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务主键
     */
    private String naturalKey;

    /**
     * 审批人
     */
    private String approveUser;
    /**
     * 审批人
     */
    private Integer approveUserId;

    /**
     * 审批意见
     */
    private String remark;

    /**
     * 审批状态
     */
    private Integer status;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    private Boolean deleteFlag;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审批级别0提交1审核2复审
     */
    private Integer approveLevel;

    /**
     * 审批场景类型 BUILDING(1, "楼宇"),
     * PRICE_APPLY(2, "价格申请");
     */
    private Integer sceneType;

    /**
     * 操作类型：1-新建数据审核，2-完善评级审核
     */
    private Integer operateType;

    /**
     * 复核系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 规则编码
     */
    private Integer ruleCode;

    /**
     * 审批实例编码
     */
    private String instanceCode;

    /**
     * 版本号
     */
    private String version;

    /**
     * 节点ID
     */
    private Integer nodeId;

    /**
     * 是否为审批节点，0：提交人节点，1：审批节点，2：结束节点
     */
    private Integer approvalFlag;
    /**
     * 审批结果，字典0138
     */
    private String approvalResult;
    /**
     * 任务状态（字典0139）
     */
    private String nodeStatus;
    /**
     * 取消原因（字典0140）
     */
    private String cancelReason;
    /**
     * 业务类型字典0163
     */
    private String approveType;


    @Getter
    @AllArgsConstructor
    public enum OperateType {
        NEW_DATA(1, "新建数据审核"),
        IMPROVE_RATING(2, "完善评级审核");

        private final Integer code;
        private final String name;
    }

}

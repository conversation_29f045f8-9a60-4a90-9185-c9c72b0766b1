package com.coocaa.meht.module.web.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class PriceApplyListCmsDto {
    /**
     * 申请id(主键)
     */
    private Integer applyId;

    /**
     * 价格申请编码： JGSQ+年月日+流水号（4位，从0001开始）
     */
    private String applyCode;

    /**
     * 楼宇编码
     */
    private String buildingNo;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 设备数量
     */
    private Integer quantity;

    /**
     * 申请时间
     */
    private LocalDateTime createTime;

    /**
     * 合同年限
     */
    private BigDecimal contractDuration;

    /**
     * 合同总金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 付款方式 [一次性、一年付、半年付、季度付、其他]
     */
    private String paymentType;

    /**
     * 是否有押金 [0:否, 1:是]
     */
    private Integer isDeposit;

    /**
     * 押金金额(元)
     */
    private BigDecimal depositAmount;

    /**
     * 设备列表
     */
    private List<PriceApplyDetailDto.DeviceDetailDto > devices;

}

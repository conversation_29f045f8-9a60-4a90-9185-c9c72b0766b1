package com.coocaa.meht.module.web.vo;

import com.coocaa.meht.module.web.vo.property.PropertyCompanyPersonVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode
public class BuildingPropertyCompanyVO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码", type = "String", example = "1")
    private String buildingNo;

    /**
     * 项目编号
     */
    @Schema(description = "项目编号", type = "String", example = "1")
    private String projectCode;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", type = "String", example = "xx项目")
    private String projectName;

    /**
     * 物业公司id
     */
    @Schema(description = "物业公司id", type = "Int", example = "1")
    private Integer propertyId;

    /**
     * 物业公司名称
     */
    @Schema(description = "物业公司名称", type = "String", example = "xx公司")
    private String propertyName;

    /**
     * 物业公司类型：1企业、2个人
     */
    @Schema(description = "物业公司类型：1企业、2个人", type = "Int", example = "1")
    private Integer type;

    /**
     * 物业公司名称
     */
    @Schema(description = "物业公司名称", type = "String", example = "xx公司")
    private String name;

    /**
     * 统一社会信用代码：企业类型必填
     */
    @Schema(description = "统一社会信用代码", type = "String", example = "6529****281X")
    private String unifiedSocialCreditCode;

    /**
     * 身份证号
     */
    @Schema(description = "身份证", type = "String", example = "6529****281X")
    private String idCard;

    /**
     * 手机号码:个人必填
     */
    @Schema(description = "手机号码", type = "String", example = "183****4368")
    private String phone;

    /**
     * 物业地址
     */
    @Schema(description = "物业地址", type = "String", example = "xx省xx市")
    private String address;

    @Schema(description = "物业人员信息")
    private List<PropertyCompanyPersonVO> personVOS;


}

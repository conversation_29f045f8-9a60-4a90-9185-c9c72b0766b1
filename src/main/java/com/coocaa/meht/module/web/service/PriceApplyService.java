package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.web.dto.PriceApplyApproveDto;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.dto.PriceApplyDto;
import com.coocaa.meht.module.web.dto.PriceApplyListCmsDto;
import com.coocaa.meht.module.web.dto.PriceApplyListDto;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.dto.req.PointByApplyNumberReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryCmsReq;
import com.coocaa.meht.module.web.dto.req.PriceApplyQueryReq;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.vo.PriceApplyDevicePointJoinVO;
import com.coocaa.meht.module.web.vo.common.ConfigVO;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 价格申请 服务类
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface PriceApplyService extends IService<PriceApplyEntity> {
    /**
     * 查询价格申请列表
     *
     * @param req 查询参数
     * @return 分页结果
     */
    PageResult<PriceApplyListDto> listPriceApplyList(PriceApplyQueryReq req);

    /**
     * 获取申请详情信息
     *
     * @param id 价格申请ID
     * @param version 版本号
     * @return 价格申请详情
     */
    PriceApplyDetailDto getPriceApplyDetail(Integer id, String version);

    /**
     * 价格申请
     *
     * @param applyDto 申请信息
     * @return 是否成功
     */
    boolean apply(PriceApplyDto applyDto);

    /**
     * 价格申请审批
     *
     * @param approveDto 审批信息
     * @return 是否成功
     */
    boolean approve(PriceApplyApproveDto approveDto);

    /**
     * 查询价格申请列表
     *
     * @param req 查询参数
     * @return 分页结果
     */
    PageResult<PriceApplyListCmsDto> listPriceApplyCms(PriceApplyQueryCmsReq req);

    /**
     * 计算价格申请的总价
     *
     * @param applyCodes 价格申请编码
     * @return 总价
     */
    BigDecimal calculateTotalAmount(Collection<String> applyCodes);

    /**
     * 申请清单
     */
    List<CodeNameVO> getPriceApplyDevicePointList(String buildingNo, String businessCode);

    /**
     * 安装信息
     */
    ProjectPointVO getInstallationInformation(Integer priceApplyDeviceId, String version, String applyCode);

    /**
     * 申请下的点位信息
     */
    ProjectPointVO pointByApplyNumber(PointByApplyNumberReq req);

    /**
     * 商机编码编码
     */
    List<PointDetail> pointByBusinessCode(String code);

    /**
     * 商机ssp编码编码
     */
    List<PointDetail> sspPointByBusinessCode(String code);

    /**
     * 检查楼宇状态
     */
    boolean checkBuildingStatus(String buildingNo);

    /**
     * 检查楼宇状态
     */
    List<String> checkBuildingGene(String buildingNo);

    /**
     * 更新价格申请分组的大屏标记
     */
    boolean updateDeviceLargeScreenFlag(Integer deviceId, Integer largeScreen, Integer coreArea);

    /**
     * 批量更新大屏标记
     */
    boolean batchUpdateLargeScreenFlag();

    /**
     * 批量更新核心区域标记
     */
    boolean batchUpdateCoreAreaFlag();

    /**
     * 根据点位code获取设备激励金数据
     */
    List<PriceApplyDevicePointJoinVO> getIncentivePrice(Collection<String> pointCodes);

    /**
     * 价格申请草稿
     * @param applyDto 申请信息
     * @return 是否成功
     */
    boolean priceApplyDraft(PriceApplyDto applyDto);

    /**
     * 删除价格申请草稿
     * @param id 价格申请草稿ID
     * @return 是否成功
     */
    boolean priceApplyDraftDelete(Integer id);

    /**
     * 根据商机编码获取个人的草稿数据
     * @param businessCode 商机编码
     * @return 草稿信息
     */
    PriceApplyDetailDto getPriceApplyDraft(String businessCode);

    /**
     * 获取城市水位价格
     * @param buildingRatingEntity 楼栋信息
     * @return 水位价格
     */
    ConfigVO getCityWaterMarkPrice(BuildingRatingEntity buildingRatingEntity);

    /**
     * 判断是否核心区域
     * @param buildingRatingEntity 楼栋信息
     * @return 是否核心区域
     */
    boolean isCoreArea(BuildingRatingEntity buildingRatingEntity);

}

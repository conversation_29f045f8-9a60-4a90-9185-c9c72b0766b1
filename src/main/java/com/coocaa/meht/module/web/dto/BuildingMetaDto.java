package com.coocaa.meht.module.web.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 楼宇基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
public class BuildingMetaDto {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 楼宇编码BC打头
     */
    private String buildingNo;

    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中  3已认证
     */
    private Integer buildingStatus;

    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    private Integer buildingType;

    /**
     * ai楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    private Integer buildingTypeAi;

    /**
     * 楼宇名称
     */
    private String buildingName;

    /**
     * 地图楼宇名称-ai
     */
    private String buildingNameAi;

    /**
     * 地图楼宇编码
     */
    private String mapNo;

    /**
     * 省名称
     */
    private String mapProvince;

    /**
     * 市名称
     */
    private String mapCity;

    /**
     * 区名称
     */
    private String mapRegion;

    /**
     * 详细地址
     */
    private String mapAddress;

    /**
     * 纬度
     */
    private String mapLatitude;

    /**
     * 经度
     */
    private String mapLongitude;

    /**
     * 行政区域编码
     */
    private String mapAdCode;

    /**
     * 认证生效开始日期
     */
    private Date authenticationStart;

    /**
     * 认证生效结束日期
     */
    private Date authenticationEnd;

    /**
     * 认证有效期限
     */
    private Long authenticationPeriod;

    /**
     * 描述
     */
    private String buildingDesc;

    /**
     * 综合得分
     */
    private BigDecimal buildingScore;

    /**
     * 等级评价
     */
    private String projectLevel;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建方式字典,人工提交，系统爬取
     */
    private String createType;

    /**
     * 最后一个成功认证的no
     */
    private String buildingRatingNo;

    /**
     * 审核通过的时间
     */
    private Date successTime;

    /**
     * 目标点位数量
     */
    private Integer targetPointCount;

    /**
     * 竞媒点位数量
     */
    private Integer competitorPointCount;

    /**
     * 负责人
     */
    private String manager;

    /**
     * ai的等级评价
     * */
    private String projectLevelAi;

    /**
     * 创世点位数量
     * */
    private Integer csPointCount;

    /**
     * ai 等级评分
     * */
    private BigDecimal buildingAiScore;
}

package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CityNameParam;
import com.coocaa.meht.common.bean.DictCodeVO;
import com.coocaa.meht.common.bean.ProjectAddParam;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.common.exception.CommonException;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.web.dao.WaitingHallMapper;
import com.coocaa.meht.module.web.dto.BuildingMetaDetailDto;
import com.coocaa.meht.module.web.dto.convert.WaitingHallConvert;
import com.coocaa.meht.module.web.dto.point.AddWaitingHallVO;
import com.coocaa.meht.module.web.dto.point.DeleteParam;
import com.coocaa.meht.module.web.dto.point.UpdateTreeParam;
import com.coocaa.meht.module.web.dto.point.WaitingHallDTO;
import com.coocaa.meht.module.web.dto.point.WaitingHallDetail;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.entity.WaitingHallBusinessEntity;
import com.coocaa.meht.module.web.entity.WaitingHallEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.PointNodeTypeEnum;
import com.coocaa.meht.module.web.enums.PointPlanStatusEnum;
import com.coocaa.meht.module.web.enums.PropertyTypeEnum;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CustomerFollowRecordService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.WaitingHallBusinessService;
import com.coocaa.meht.module.web.service.WaitingHallService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.rpc.dto.DeletePointParam;
import com.coocaa.meht.rpc.dto.PointTreeUpdate;
import com.coocaa.meht.rpc.dto.WaitingHallAddParam;
import com.coocaa.meht.utils.RsaExample;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WaitingHallServiceImpl extends ServiceImpl<WaitingHallMapper, WaitingHallEntity> implements WaitingHallService {

    @Autowired
    private FeignSspRpc feignSspRpc;
    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;
    @Autowired
    private RsaExample rsaExample;
    @Autowired
    private IBuildingMetaService buildingMetaService;
    @Autowired
    private PointPlanService pointPlanService;
    @Autowired
    private WaitingHallBusinessService waitingHallBusinessService;
    @Autowired
    private WaitingHallConvert waitingHallConvert;
    @Autowired
    private PointService pointService;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;



    @Transactional
    @Override
    public AddWaitingHallVO add(WaitingHallDTO waitingHallDTO) {
        //初始点位方案id
        Integer pointStartPlanId = waitingHallDTO.getPointPlanId();
        //校验点位方案状态
        Integer pointPlanId = waitingHallDTO.getPointPlanId();
        checkPointPlanStatus(pointPlanId);
        LoginUser user = SecurityUser.getUser();
        String userCode = user.getUserCode();
        String buildingNo = waitingHallDTO.getBuildingRatingNo();
        if (pointPlanId == null) {
            //需要先新增点位方案
            pointPlanId = savePointPlan(userCode, buildingNo, waitingHallDTO.getBusinessCode());
        }
        ProjectAddParam projectAddParam = getProjectAddParam(buildingNo);
        WaitingHallAddParam addParam = waitingHallConvert.toWaitingHallAddParam(waitingHallDTO);
        log.info("addParam:{}", addParam);
        //改从ssp调用新增
        addParam.setProjectAddParam(projectAddParam);
        Integer waitingHallId = RpcUtils.unBox(feignSspRpc.addWaitingHall(addParam));
        //增加等候厅与商机之间的方案
        WaitingHallBusinessEntity entity = WaitingHallBusinessEntity
                .builder().waitingHallId(waitingHallId)
                .businessCode(waitingHallDTO.getBusinessCode())
                .creator(user.getId().intValue()).build();
        waitingHallBusinessService.save(entity);
        //发现消息
        if (pointStartPlanId == null) {
            sendBusinessStatusChange(waitingHallDTO.getBusinessCode());
        }
        return AddWaitingHallVO.builder().pointPlanId(pointPlanId)
                .waitingHallId(waitingHallId).build();

    }

    private void sendBusinessStatusChange(String businessCode){
        //发商机状态变更消息
        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, businessCode)
                .one();

        CachedUser user = UserThreadLocal.getUser();
        BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
        businessStatusChangeVO.setBusinessCode(businessCode);
        businessStatusChangeVO.setOperatorId(user.getId());
        businessStatusChangeVO.setOperatorWno(user.getWno());
        businessStatusChangeVO.setOperatorName(user.getName());

        // 补初步洽谈状态变化
        if (BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode().equals(businessOpportunity.getStatus())) {
            businessStatusChangeVO.setStatus(BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());
            kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSONObject.toJSONString(businessStatusChangeVO));
        }

        businessStatusChangeVO.setStatus(BusinessChangeStatusEnum.REACHING_INTENTION.getCode());
        kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSONObject.toJSONString(businessStatusChangeVO));
    }

    /**
     * 查询楼宇信息用于生成项目
     * @param buildingNo
     * @return
     */
    public ProjectAddParam getProjectAddParam(String buildingNo) {
        BuildingMetaDetailDto metaDetail = buildingMetaService.findByBuildRatingNo(buildingNo);
        if (ObjectUtil.isEmpty(metaDetail)) {
            throw new ServerException("楼宇信息不存在");
        }
        LoginUser loginUser = SecurityUser.getUserAnonymity();
        if (StringUtils.isNotBlank(metaDetail.getManager())) {
            UserVO userVO = RpcUtils.unBox(feignAuthorityRpc.getUserInfo(metaDetail.getManager()));
            String manager = "%s(%s)";
            if (ObjectUtils.isNotEmpty(userVO)) {
                manager = String.format(manager, userVO.getName(), userVO.getWno());
                metaDetail.setManager(manager);
            }
        }
        ProjectAddParam projectAddParam = ProjectAddParam.builder()
                .name(metaDetail.getBuildingName())
                .code("")
                .addressDetail(rsaExample.decryptByPrivate(metaDetail.getMapAddress()))
                .longitude(rsaExample.decryptByPrivate(metaDetail.getMapLongitude()))
                .latitude(rsaExample.decryptByPrivate(metaDetail.getMapLatitude()))
                .buildingRatingNo(metaDetail.getBuildingRatingNo())
                .forbiddenIndustry(metaDetail.getForbiddenIndustry())
                .businessOwner(metaDetail.getManager()).creator(loginUser == null ? null : loginUser.getId().intValue())
                .createTime(new Date())
                .buildingMetaNo(metaDetail.getBuildingMetaNo())
                .build();
        //楼宇类型转物业类型
        switch (metaDetail.getBuildingType()) {
            case 0:
                projectAddParam.setPropertyType(PropertyTypeEnum.OFFICE.getCode());
                break;
            case 1:
                projectAddParam.setPropertyType(PropertyTypeEnum.GLOBE.getCode());
                break;
            case 2:
                projectAddParam.setPropertyType(PropertyTypeEnum.MIXED.getCode());
                break;
            case 3:
                projectAddParam.setPropertyType(PropertyTypeEnum.PARK.getCode());
                break;
            default:
                projectAddParam.setPropertyType(PropertyTypeEnum.RESIDENCE.getCode());
        }
        if (StringUtils.isBlank(metaDetail.getMapCity()) || StringUtils.isBlank(metaDetail.getMapRegion())) {
            throw new CommonException("楼宇区划信息缺失");
        }
        ResultTemplate<DictCodeVO> county = feignAuthorityRpc.getCounty(CityNameParam.builder().cityName(metaDetail.getMapCity()).countyName(metaDetail.getMapRegion()).build());
        DictCodeVO dictCodeVO = RpcUtils.unBox(county);
        projectAddParam.setCityId(dictCodeVO.getParentId());
        projectAddParam.setDistrictId(dictCodeVO.getId());
        return projectAddParam;
    }

    private void checkPointPlanStatus(Integer pointPlanId) {
        if(pointPlanId !=null){
            PointPlanEntity pointPlan = pointPlanService.getById(pointPlanId);
            checkPointPlanStatus(pointPlan);
        }
    }

    private static void checkPointPlanStatus(PointPlanEntity pointPlan) {
        String status = pointPlan.getStatus();
        if (PointPlanStatusEnum.REVIEW.getCode().equals(status)) {
            throw new ServerException("当前点位状态下,不允许改动点位数据.");
        }
    }

    private static void checkDeletePointPlanStatus(PointPlanEntity pointPlan) {
        String status = pointPlan.getStatus();
        if (!PointPlanStatusEnum.WAITING_SIGN.getCode().equals(status)) {
            throw new ServerException("当前点位状态下,不允许改动点位数据.");
        }
    }

    private WaitingHallEntity getSameNameWaitingHall(WaitingHallDTO waitingHallDTO) {
        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, waitingHallDTO.getBuildingRatingNo())
                .eq(WaitingHallEntity::getBuildingName, waitingHallDTO.getBuildingName())
                .eq(WaitingHallEntity::getUnitName, waitingHallDTO.getUnitName())
                .eq(WaitingHallEntity::getFloor, waitingHallDTO.getFloor())
                .eq(WaitingHallEntity::getWaitingHallName, waitingHallDTO.getWaitingHall());
        return getOne(queryWrapper);
    }

    private Integer savePointPlan(String userCode, String buildingNo,String businessCode) {

        //点位方案校验
        List<PointPlanEntity> list = pointPlanService.list(Wrappers.<PointPlanEntity>lambdaQuery()
                .eq(PointPlanEntity::getBusinessCode, businessCode));
        if (CollectionUtil.isNotEmpty(list)){
            throw new ServerException("该商机已有方案，不能再添加点位方案");
        }

        Integer pointPlanId;
        PointPlanEntity pointPlanEntity = new PointPlanEntity();
        pointPlanEntity.setCreateBy(userCode);
        pointPlanEntity.setUpdateBy(userCode);
        pointPlanEntity.setBuildingRatingNo(buildingNo);
        pointPlanEntity.setStatus(PointPlanStatusEnum.WAITING_SIGN.getCode());
        pointPlanEntity.setBusinessCode(businessCode);
        pointPlanService.save(pointPlanEntity);
        pointPlanId = pointPlanEntity.getId();
        return pointPlanId;
    }

    @Override
    public void updateWaitingHall(WaitingHallDTO waitingHallDTO) {
        checkPointPlanStatus(waitingHallDTO.getPointPlanId());
        handleWaitingHall(waitingHallDTO);
    }

    @Override
    public void handleWaitingHall(WaitingHallDTO waitingHallDTO) {
        //调用ssp进行等候厅编辑
        PointTreeUpdate update = new PointTreeUpdate();
        BeanUtils.copyProperties(waitingHallDTO, update);
        update.setName(waitingHallDTO.getWaitingHall());
        update.setPointNodeTypeEnum(PointNodeTypeEnum.WAITING_HALL);
        update.setWaitingHallType(waitingHallDTO.getWaitingHallType());
        RpcUtils.unBox(feignSspRpc.updateTreeNode(update));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(DeleteParam param) {
        String businessCode = param.getBusinessCode();
        checkDeletePointStatusByBusinessCode(businessCode);
        if (!param.getType().equals(PointNodeTypeEnum.POINT.getCode())) {
            //查询当前商机下的等候厅id
            List<Integer> waitingHallIds = waitingHallBusinessService.findByBusinessCode(businessCode);
            param.setWaitingHallIds(waitingHallIds);
        }
        //调用ssp删除接口
        DeletePointParam deletePointParam = RpcUtils.unBox(feignSspRpc.deletePoint(param));
        //删除当前商机下的楼宇及等候厅信息
        if (ObjectUtil.isNotEmpty(deletePointParam.getWaitingHallIds())) {
            waitingHallBusinessService.deleteByWaitingIds(deletePointParam.getWaitingHallIds());
        }
        //删除H5中的点位信息
        if (ObjectUtil.isNotEmpty(deletePointParam.getPointCodes())) {
            pointService.deleteByPointIds(deletePointParam.getPointCodes());
        }
    }

    private void checkPointStatusByBusinessCode(String businessCode) {
        PointPlanEntity pointPlanEntity =pointPlanService.getByBusinessCode(businessCode);
        checkPointPlanStatus(pointPlanEntity);
    }
    private void checkDeletePointStatusByBusinessCode(String businessCode) {
        PointPlanEntity pointPlanEntity =pointPlanService.getByBusinessCode(businessCode);
        checkDeletePointPlanStatus(pointPlanEntity);
    }



    private void deleteByBuildingName(String buildingRatingNo, String buildingName) {
        // 1. 先查询要删除的等候厅IDs
        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .select(WaitingHallEntity::getId);
        
        List<Integer> waitingHallIds = getWaitingHallIds(queryWrapper);
        
        if (!CollectionUtils.isEmpty(waitingHallIds)) {
            // 2. 批量删除等候厅下的所有点位及其图片
            pointService.batchDeleteByWaitingHallIds(waitingHallIds);
            // 3. 删除等候厅
            remove(queryWrapper);
        }
    }

    private void deleteByUnitName(String buildingRatingNo, String buildingName, String unitName) {
        // 1. 先查询要删除的等候厅IDs
        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, unitName)
                .select(WaitingHallEntity::getId);
        
        List<Integer> waitingHallIds = getWaitingHallIds(queryWrapper);
        
        if (!CollectionUtils.isEmpty(waitingHallIds)) {
            // 2. 批量删除等候厅下的所有点位及其图片
            pointService.batchDeleteByWaitingHallIds(waitingHallIds);
            // 3. 删除等候厅
            remove(queryWrapper);
        }
    }

    private void deleteByFloorName(String buildingRatingNo, String buildingName, String unitName, String floorName) {
        // 1. 先查询要删除的等候厅IDs
        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, unitName)
                .eq(WaitingHallEntity::getFloor, floorName)
                .select(WaitingHallEntity::getId);
        
        List<Integer> waitingHallIds = getWaitingHallIds(queryWrapper);
        
        if (!CollectionUtils.isEmpty(waitingHallIds)) {
            // 2. 批量删除等候厅下的所有点位及其图片
            pointService.batchDeleteByWaitingHallIds(waitingHallIds);
            // 3. 删除等候厅
            remove(queryWrapper);
        }
    }

    private List<Integer> getWaitingHallIds(LambdaQueryWrapper<WaitingHallEntity> queryWrapper) {
        return list(queryWrapper)
                .stream()
                .map(WaitingHallEntity::getId)
                .collect(Collectors.toList());
    }

    private void deleteById(Integer waitingHallId) {
        removeById(waitingHallId);
        pointService.deleteByWaitingHallId(waitingHallId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTree(UpdateTreeParam param) {
        checkPointStatusByBusinessCode(param.getBusinessCode());

        updateTreeNode(param);
    }

    @Override
    public void updateTreeNode(UpdateTreeParam  param) {
        //调用ssp的编辑接口
        PointTreeUpdate update = new PointTreeUpdate();
        BeanUtils.copyProperties(param, update);
        // 获取节点类型
        PointNodeTypeEnum nodeType = PointNodeTypeEnum.getByCode(param.getType());
        if (nodeType == null) {
            throw new ServerException("无效的节点类型");
        }

        switch (nodeType) {
            case BUILDING:
                update.setName(param.getBuildingName());
                break;
            case UNIT:
                update.setName(param.getUnitName());
                break;
            case FLOOR:
                update.setName(param.getFloorName());
                break;
            default:
                throw new ServerException("不支持的节点类型");
        }
        update.setOriginalValue(param.getOriginVale());
        update.setPointNodeTypeEnum(nodeType);
        RpcUtils.unBox(feignSspRpc.updateTreeNode(update));
    }

    private void updateBuildingName(String buildingRatingNo, String originValue, String newName) {
        LambdaQueryWrapper<WaitingHallEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, newName);
        List<WaitingHallEntity> list = this.list(wrapper);
        if(ObjectUtil.isNotEmpty(list)){
            throw new ServerException("同楼层下存在相同名称的等候厅");
        }

        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, originValue);

        WaitingHallEntity entity = new WaitingHallEntity();
        entity.setBuildingName(newName);
        entity.setUpdateBy(SecurityUser.getUserCode());
        update(entity, queryWrapper);
    }

    private void updateUnitName(String buildingRatingNo, String buildingName, 
            String originValue, String newName) {
        LambdaQueryWrapper<WaitingHallEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, newName);
        List<WaitingHallEntity> list = this.list(wrapper);
        if(ObjectUtil.isNotEmpty(list)){
            throw new ServerException("同楼层下存在相同名称的等候厅");
        }

        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, originValue);
        WaitingHallEntity entity = new WaitingHallEntity();
        entity.setUnitName(newName);
        entity.setUpdateBy(SecurityUser.getUserCode());
        update(entity, queryWrapper);
    }

    private void updateFloorName(String buildingRatingNo, String buildingName,
            String unitName, String originValue, String newName) {
        LambdaQueryWrapper<WaitingHallEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, unitName)
                .eq(WaitingHallEntity::getFloor, newName);
        List<WaitingHallEntity> list = this.list(wrapper);
        if(ObjectUtil.isNotEmpty(list)){
            throw new ServerException("同楼层下存在相同名称的等候厅");
        }
        LambdaQueryWrapper<WaitingHallEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaitingHallEntity::getBuildingRatingNo, buildingRatingNo)
                .eq(WaitingHallEntity::getBuildingName, buildingName)
                .eq(WaitingHallEntity::getUnitName, unitName)
                .eq(WaitingHallEntity::getFloor, originValue);
        
        WaitingHallEntity entity = new WaitingHallEntity();
        entity.setFloor(newName);
        entity.setUpdateBy(SecurityUser.getUserCode());
        update(entity, queryWrapper);
    }

    @Override
    public WaitingHallDetail getWaitingHallById(Integer id,String businessCode) {
        WaitingHallDetail data = RpcUtils.unBox(feignSspRpc.getWaitingHallById(id));
        //查询点位方案信息
        PointPlanEntity pointPlan = pointPlanService.getByBusinessCode(businessCode);
        if(ObjectUtil.isEmpty(pointPlan)){
            throw new ServerException("点位方案不存在");
        }
        data.setPointPlanId(pointPlan.getId());
        return data;
    }
}
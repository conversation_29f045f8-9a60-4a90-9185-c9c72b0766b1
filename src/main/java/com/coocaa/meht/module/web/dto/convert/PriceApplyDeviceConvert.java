package com.coocaa.meht.module.web.dto.convert;


import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PriceApplyDeviceConvert {

    PriceApplyDeviceConvert INSTANCE = Mappers.getMapper(PriceApplyDeviceConvert.class);

    @Mapping(target = "applyDeviceId",source = "id")
    PriceApplyDetailDto.DeviceDetailDto toDeviceDetailDto(PriceApplyDeviceEntity entity);

    List<PriceApplyDetailDto.DeviceDetailDto> toDeviceDetailDto(List<PriceApplyDeviceEntity> entityList);
}

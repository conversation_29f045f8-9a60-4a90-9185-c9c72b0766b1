package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.annotation.RollBack;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("building_rating")
public class BuildingRatingEntity extends BaseEntity {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 楼宇编码
     */
    private String buildingNo;
    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中 3已认证
     */
    private Integer buildingStatus;
    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过 4 已放弃
     */
    private Integer status;
    /**
     * 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
     */
    private Integer buildingType;
    /**
     * 楼宇名称
     */
    private String buildingName;
    /**
     * 地图楼宇编码
     */
    private String mapNo;
    /**
     * 省名称
     */
    private String mapProvince;
    /**
     * 市名称
     */
    private String mapCity;
    /**
     * 区名称
     */
    private String mapRegion;
    /**
     * 详细地址
     */
    private String mapAddress;
    /**
     * 纬度
     */
    private String mapLatitude;
    /**
     * 经度
     */
    private String mapLongitude;

    /**
     * 区域行政编码
     */
    private String mapAdCode;
    /**
     * 认证生效开始日期
     */
    private LocalDateTime authenticationStart;
    /**
     * 认证生效结束日期
     */
    private LocalDateTime authenticationEnd;
    /**
     * 认证有效期限
     */
    private Long authenticationPeriod;
    /**
     * 冻结开始日期
     */
    private LocalDateTime freezeStart;
    /**
     * 冻结结束日期
     */
    private LocalDateTime freezeEnd;
    /**
     * 冻结有效期限
     */
    private Long freezePeriod;
    /**
     * 描述
     */
    private String buildingDesc;
    /**
     * 提交人工号
     */
    private String submitUser;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 综合得分
     */
    private BigDecimal buildingScore;
    /**
     * 等级评价
     */
    private String projectLevel;

    /** 一楼独享 */
    private BigDecimal firstFloorExclusive;

    /** 一楼共享 */
    private BigDecimal firstFloorShare;

    /** 负一楼 */
    private BigDecimal negativeFirstFloor;

    /** 负二楼 */
    private BigDecimal negativeTwoFloor;

    /** 二楼及以上 */
    private BigDecimal twoFloorAbove;

    /** 负三楼及以下 */
    private BigDecimal thirdFloorBelow;



    /**
     * 审批人工号
     */
    private String approveUser;
    /**
     * 审批时间
     */
    private LocalDateTime approveTime;
    /**
     * 审批描述
     */
    private String approveDesc;
    /**
     * 驳回人工号
     */
    private String rejectUser;
    /**
     * 驳回时间
     */
    private LocalDateTime rejectTime;
    /**
     * 驳回描述
     */
    private String rejectDesc;

    /**
     * 外墙材料附件地址
     */
    private String buildingExteriorPic;

    /**
     * 楼盘大堂附件地址
     */
    private String buildingLobbyPic;

    /**
     * 侯梯厅附件地址
     */
    private String buildingHallPic;

    /**
     * 大堂环境图附件地址
     */
    private String buildingLobbyEnvPic;

    /**
     * 梯厅环境图附件地址
     */
    private String buildingElevatorPic;

    /**
     * 闸口图附件地址
     */
    private String buildingGatePic;

    /**
     * 安装示意图附件地址
     */
    private String buildingInstallationPic;

    /**
     是否删除: 0否,1是
     */
    private Integer deleted;

    /**
     *  crm数据推送状态  0:未推送  1:已推送 2:推送失败
     */
    private Integer  crmPushStatus;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * CRM流程ID
     */
    private String crmFlowId;

    /**
     * 流程名称CRM
     */
    private String crmFlowName;

    /**
     * AI评级
     */
    private String projectAiLevel;
    /**
     * AI得分
     */
    private BigDecimal buildingAiScore;

    /**
     * 签约状态 (字典0028)
     */
    private String signStatus;

    /**
     * 目标点位数
     */
    private Integer targetPointCount;

    /**
     * 楼宇等级top等级
     */
    private String topLevel;

    /**
     * 公海数据标识：0-否，1-是
     */
    private Integer highSeaFlag;

    /**
     * 掉入公海时间
     */
    private LocalDateTime enterSeaTime;

    /**
     * 掉入公海计算起始时间
     */
    private LocalDateTime enterSeaCalculateTime;

    /**
     * 小屏评级完成标识：0-否，1-是
     */
    private Integer smallScreenRatingFlag;

    /**
     * 大屏评级完成标识：0-否，1-是
     */
    private Integer largeScreenRatingFlag;

    /**
     * 掉入公海原因
     */
    private String enterSeaReason;

    /**
     * 评级版本
     */
    private String ratingVersion;

    /**
     * 复核等级
     */
    private String projectReviewLevel;


    /**
     * 数据版本标识
     */
    private Integer dataFlag;

    /**
     * 草稿(RatingDraftDTO对象JSON格式)
     */
    private String draft;


    public enum BuildingStatus {
        UN_CONFIRM(0, "未认证"),
        CONFIRMING(1, "认证中"),
        FREEZING(2, "冻结中"),
        CONFIRMED(3,"已认证");
        public final int value;
        private final String name;

        BuildingStatus(int value, String name) {
            this.value = value;
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        // 静态方法，通过value获取name
        public static String getNameByValue(int value) {
            for (BuildingStatus type : BuildingStatus.values()) {
                if (type.value == value) {
                    return type.name;
                }
            }
            throw new IllegalArgumentException("No enum constant with value " + value);
        }
    }


    public enum Status {
        WAIT_AUDIT(0, "审核中"),
        AUDITED(1, "审核通过"),
        REJECTED(2, "已驳回"),
        FAILED_AUDIT(3, "审核不通过"),
        ABANDONED(4, "已放弃"),
        DRAFT(5, "草稿");

        public final int value;
        private final String name;

        Status(int value, String name) {
            this.value = value;
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        public static String getNameByValue(int value) {
            for (Status type : Status.values()) {
                if (type.value == value) {
                    return type.name;
                }
            }
            throw new IllegalArgumentException("No enum constant with value " + value);
        }
    }

    public enum BuildingType {
        OFFICE_BUILDING(0, "写字楼"),
        COMMERCIAL_RESIDENTIAL_BUILDING(1, "商住楼"),
        COMPLEX(2, "综合体"),
        INDUSTRIAL_PARK(3,"产业园区");
        public final int value;
        private final String name;

        BuildingType(int value, String name) {
            this.value = value;
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public String getName() {
            return name;
        }

        // 静态方法，通过value获取name
        public static String getNameByValue(int value) {
            for (BuildingType type : BuildingType.values()) {
                if (type.value == value) {
                    return type.name;
                }
            }
            throw new IllegalArgumentException("No enum constant with value " + value);
        }

    }


    @Getter
    @AllArgsConstructor
    public enum BusinessStatusEnum {
        PENDING("0039-1", "待签约"),
        SIGNING("0039-2", "签约中"),
        SIGNED("0039-3", "已签约"),
        UNSIGNED("0039-4", "已解约");

        private final String code;
        private final String desc;


        private final static Map<String, BusinessStatusEnum> BY_CODE_MAP =
                Arrays.stream(BusinessStatusEnum.values())
                        .collect(Collectors.toMap(BusinessStatusEnum::getCode, Function.identity()));

        /**
         * 将代码转成枚举
         */
        public static BusinessStatusEnum parse(String code) {
            return parse(code, null);
        }

        /**
         * 将代码转成枚举
         */
        public static BusinessStatusEnum parse(String code, BusinessStatusEnum defaultValue) {
            return BY_CODE_MAP.getOrDefault(code, defaultValue);
        }

        /**
         * 根据代码获取描述
         */
        public static String getDesc(String code) {
            return Optional.ofNullable(parse(code)).map(BusinessStatusEnum::getDesc).orElse(StringUtils.EMPTY);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum HighSeaFlagEnum {

        NO(0, "非公海数据"),

        YES(1, "公海数据");

        private final Integer code;

        private final String desc;

    }

}

package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.PriceApplyDeviceDao;
import com.coocaa.meht.module.web.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.module.web.service.PriceApplyDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 价格申请设备明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class PriceApplyDeviceServiceImpl extends ServiceImpl<PriceApplyDeviceDao, PriceApplyDeviceEntity> implements PriceApplyDeviceService {

    @Autowired
    private PriceApplyDeviceDao priceApplyDeviceDao;

    @Override
    public List<PriceApplyDeviceEntity> findByPriceApplyId(Integer priceApplyId) {
        LambdaQueryWrapper<PriceApplyDeviceEntity> queryWrapper = new QueryWrapper<PriceApplyDeviceEntity>().lambda()
                .eq(PriceApplyDeviceEntity::getApplyId, priceApplyId).orderByAsc(PriceApplyDeviceEntity::getId);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Integer, List<PriceApplyDeviceEntity>> findByPriceApplyIdMap(List<Integer> priceApplyIdList) {
        LambdaQueryWrapper<PriceApplyDeviceEntity> queryWrapper = new QueryWrapper<PriceApplyDeviceEntity>().lambda()
                .in(PriceApplyDeviceEntity::getApplyId, priceApplyIdList).orderByAsc(PriceApplyDeviceEntity::getId);
        List<PriceApplyDeviceEntity> list = this.list(queryWrapper);
        return list.isEmpty()
                ? Collections.emptyMap()
                : list.stream().collect(Collectors.groupingBy(PriceApplyDeviceEntity::getApplyId));
    }

    @Override
    public List<String> getPointByApplyNumber(String applyNumber) {
        return priceApplyDeviceDao.getPointByApplyNumber(applyNumber);
    }
}

package com.coocaa.meht.module.web.controller;

import cn.hutool.core.date.DatePattern;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.web.service.kanban.IKanbanService;
import com.coocaa.meht.module.web.vo.kanban.CityStatisticsItemVO;
import com.coocaa.meht.module.web.vo.kanban.DataAccessVO;
import com.coocaa.meht.module.web.vo.kanban.IndexVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanPointStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.time.LocalDate;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * H5数据看板相关接口
 */
@Slf4j
@RestController
@RequestMapping("/kanban")
@Tag(name = "H5数据看板相关接口", description = "H5数据看板相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanController {

    private final IKanbanService kanbanService;


    /**
     * 用户城市权限
     */
    @Operation(summary = "用户城市权限")
    @GetMapping("/user/cities")
    public ResultTemplate<DataAccessVO> getUserDataAccess() {
        return ResultTemplate.success(kanbanService.getUserDataAccess());
    }

    /**
     * 获取楼宇总量统计信息
     */
    @Operation(summary = "获取楼宇总量统计信息")
    @GetMapping("/building/total-statistics")
    public ResultTemplate<KanbanVO> getBuildingTotalStatisticsInfo(@RequestParam(name = "cityId", required = false, defaultValue = "0") Integer cityId) {
        return ResultTemplate.success(kanbanService.getBuildingTotalStatisticsInfo(cityId));
    }

    /**
     * 获取楼宇新增统计信息
     */
    @Operation(summary = "获取楼宇新增统计信息")
    @GetMapping("/building/increment-statistics")
    public ResultTemplate<KanbanVO> getBuildingIncrementStatisticsInfo(@RequestParam(name = "cityId", required = false,
            defaultValue = "0") Integer cityId) {
        return ResultTemplate.success(kanbanService.getBuildingIncrementStatisticsInfo(cityId));
    }

    /**
     * 获取合同状态统计信息
     */
    @Operation(summary = "获取合同统计信息")
    @GetMapping("/contract/contract-info")
    public ResultTemplate<KanbanVO> getContractStatusInfo(@RequestParam(name = "cityId", required = false, defaultValue = "0") Integer cityId) {
        return ResultTemplate.success(kanbanService.getContractStatusInfo(cityId));
    }

    /**
     * 获取合同点位状态数据
     */
    @Operation(summary = "获取合同点位状态数据")
    @GetMapping("/contract/point-info")
    public ResultTemplate<KanbanPointStatisticsVO> getPointStatusInfo(@RequestParam(name = "date", required = false)
                                                                      @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
                                                                      @RequestParam(name = "days", required = false) Integer days,
                                                                      @RequestParam(name = "cityId", required = true) Integer cityId) {
        return ResultTemplate.success(kanbanService.getPointStatusInfo(date, days, cityId));
    }

    /**
     * 获取楼宇和项目（商机）转化率信息
     */
    @Operation(summary = "获取楼宇和项目（商机）转化率信息")
    @GetMapping("/building/statistics-info/ratio")
    public ResultTemplate<CityStatisticsItemVO> getBuildingStatisticsRatioInfo(@RequestParam(name = "cityId", required = true) Integer cityId) {
        return ResultTemplate.success(kanbanService.getBuildingStatisticsRatioInfo(cityId));
    }


    /**
     * 执行转化率统计
     */
    @Operation(summary = "执行转化率统计")
    @GetMapping("/execute-statistics")
    public ResultTemplate<List<IndexVO>> executeRatioStatistics() {
        kanbanService.executeRatioStatistics(null);
        return ResultTemplate.success();
    }

    /**
     * 获取设备统计数据
     */
    @Operation(summary = "获取设备统计数据")
    @GetMapping("/device/statistics-info")
    public ResultTemplate<KanbanDeviceStatisticsVO> getDeviceStatisticsInfo(
            @RequestParam(name = "cityId", required = false, defaultValue = "0") Integer cityId,
            @RequestParam(name = "date", required = false) @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate date) {
        return ResultTemplate.success(kanbanService.getDeviceStatisticsInfo(cityId, date));
    }

}

package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.PointPicVo;
import com.coocaa.meht.common.bean.ProjectAddParam;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.aspect.RedisLock;
import com.coocaa.meht.module.web.dto.point.AddWaitingHallVO;
import com.coocaa.meht.module.web.dto.point.DeleteParam;
import com.coocaa.meht.module.web.dto.point.PointDTO;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.dto.point.UpdateTreeParam;
import com.coocaa.meht.module.web.dto.point.WaitingHallDTO;
import com.coocaa.meht.module.web.dto.point.WaitingHallDetail;
import com.coocaa.meht.module.web.dto.point.WaitingHallPointVO;
import com.coocaa.meht.module.web.dto.point.WorkOrderPointQueryDTO;
import com.coocaa.meht.module.web.service.IPointContractSnapshotService;
import com.coocaa.meht.module.web.service.IPointPriceSnapshotService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.WaitingHallService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 等候厅管理
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
@Tag(name = "点位管理")
@RestController
@RequestMapping("/point")
public class PointController {

    @Autowired
    private WaitingHallService waitingHallService;
    @Autowired
    private PointService pointService;
    @Autowired
    private IPointPriceSnapshotService pointPriceSnapshotService;
    @Autowired
    private IPointContractSnapshotService pointContractSnapshotService;

    /**
     * 新增等候厅
     */
    @Operation(summary = "新增等候厅")
    @PostMapping("/waiting-hall")
    @RedisLock(key = "businessCode")
    public Result<AddWaitingHallVO> addWaitingHall(@Validated @RequestBody WaitingHallDTO waitingRoom) {
        return Result.ok(waitingHallService.add(waitingRoom));
    }

    @Operation(summary = "编辑等候厅")
    @PutMapping("/waiting-hall")
    @RedisLock(key = "waitingHallId")
    public Result<Void> updateWaitingHall(@Validated @RequestBody WaitingHallDTO waitingRoom) {
        waitingHallService.updateWaitingHall(waitingRoom);
        return Result.ok();
    }

    @Operation(summary = "根据等候厅id查询等候厅详情")
    @GetMapping("/waiting-hall/{id}/{businessCode}")
    public Result<WaitingHallDetail> getWaitingHallById(@PathVariable("id") Integer id, @PathVariable("businessCode") String businessCode) {
        return Result.ok(waitingHallService.getWaitingHallById(id, businessCode));
    }

    @Operation(summary = "分别按照楼栋，单元，楼层，等候厅进行删除")
    @DeleteMapping("/delete")
    public Result<Void> delete(@Validated @RequestBody DeleteParam param) {
        waitingHallService.delete(param);
        return Result.ok();
    }

    @Operation(summary = "新增点位")
    @PostMapping("/add")
    @RedisLock(key = "waitingHallId")
    public Result<Void> addPoint(@Validated @RequestBody PointDTO pointAddDTO) {
        pointService.addPoint(pointAddDTO);
        return Result.ok();
    }

    @Operation(summary = "修改点位")
    @PutMapping("/update")
    @RedisLock(key = "pointId")
    public Result<Void> updatePoint(@Validated @RequestBody PointDTO pointAddDTO) {
        if (StringUtils.isBlank(pointAddDTO.getCode())) {
            throw new ServerException("点位编码不能为空");
        }
        pointService.updatePoint(pointAddDTO);
        return Result.ok();
    }

    @Operation(summary = "修改树上的各个节点")
    @PutMapping("/update/tree")
    @RedisLock(key = "businessCode")
    public Result<Void> updateTree(@Validated @RequestBody UpdateTreeParam param) {
        waitingHallService.updateTree(param);
        return Result.ok();
    }


    /*@Operation(summary = "根据点位方案id查询树信息")
    @GetMapping("/tree/{pointPlanId}")
    public Result<List<PointTreeVO>> getTreeByPointPlanId(@PathVariable("pointPlanId") Integer pointPlanId) {

        return Result.ok(waitingHallService.getTreeByPointPlanId(pointPlanId));
    }*/

    @Operation(summary = "根据点位方案id查询树信息")
    @PostMapping("/list")
    public Result<ProjectPointVO> listPoint(@RequestBody PointDetailParam param) {
        if (StringUtils.isBlank(param.getBusinessCode())) {
            throw new ServerException("商机编码不能为空");
        }
        return Result.ok(pointService.listBuildingPoint(param));
    }

    @Operation(summary = "根据buildingNo查询所有点位")
    @PostMapping("/{buildingNo}/list")
    public Result<ProjectPointVO> listPointByBuildingNo(@PathVariable("buildingNo") String buildingNo) {
        return Result.ok(pointService.listPointByBuildingNo(buildingNo));
    }

    @Operation(summary = "价格申请的快照树信息")
    @PostMapping("/list/price")
    public Result<ProjectPointVO> listPricePoint(@RequestBody PointDetailParam param) {

        return Result.ok(pointPriceSnapshotService.listBuildingPoint(param));
    }

    @Operation(summary = "合同的快照树信息")
    @PostMapping("/list/contract")
    public Result<ProjectPointVO> listContractPoint(@RequestBody PointDetailParam param) {

        return Result.ok(pointContractSnapshotService.listBuildingPoint(param));
    }

    /**
     * 点位方案ppt
     */
    @Operation(summary = "点位方案ppt")
    @PostMapping("/export-ppt")
    public Result<String> ppt(@RequestBody PointDetailParam param) {
        return Result.ok(pointService.pptPoint(param));
    }

    /**
     * 工单方案ppt
     */
    @Operation(summary = "工单方案ppt")
    @PostMapping("/work-order-export-ppt")
    public Result<String> workOrderPpt(@RequestBody List<WorkOrderPointQueryDTO> param) {
        return Result.ok(pointService.workOrderPpt(param));
    }

    /**
     * 清洗等候厅与商机的关系数据
     */
    @Operation(summary = "清洗等候厅与商机的关系数据")
    @GetMapping("/cleaningWaitingHallBusiness")
    public Result<String> cleaningWaitingHallBusiness(@RequestParam(value = "buildingRatingNo") String buildingRatingNo,
                                                      @RequestParam(value = "businessCode") String businessCode) {
        pointService.cleaningWaitingHallBusiness(buildingRatingNo, businessCode);
        return Result.ok();
    }

    /**
     * todo 同步数据用，后期删除
     *
     * @return
     */
    @Operation(summary = "查询所有的等候厅及点位")
    @GetMapping("/findWaitingHalls-list")
    public ResultTemplate<WaitingHallPointVO> findWaitingHallAndPoint() {
        return ResultTemplate.success(pointService.findWaitingHallAndPoint());
    }

    /**
     * todo 同步数据用，后期删除
     * 查询楼宇信息用于生成项目信息
     */
    @GetMapping("/getProjectAddParam")
    public ResultTemplate<ProjectAddParam> getProjectAddParam(@RequestParam(value = "buildingNo") String buildingNo) {
        return ResultTemplate.success(waitingHallService.getProjectAddParam(buildingNo));
    }

    /**
     * todo 同步数据用，后期删除
     * 查询所有的点位图片
     */
    @GetMapping("/findPointPicList")
    public ResultTemplate<List<PointPicVo>> findPointPicList() {
        return ResultTemplate.success(pointService.findPointPicList());
    }


}

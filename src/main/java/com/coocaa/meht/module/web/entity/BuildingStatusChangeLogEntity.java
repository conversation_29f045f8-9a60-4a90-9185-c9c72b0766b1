package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @file BuildingStatusChangeLogEntity
 * @date 2024/12/30 20:27
 * @description This is a java file.
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("building_status_change_log")
public class BuildingStatusChangeLogEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据类型
     */
    private String type = "";

    /**
     * 数据类型子类型
     */
    private Integer subType = 0;

    /**
     * 业务ID (楼宇,客户,商机,...)
     */
    private Long bizId = 0L;

    /**
     * 业务编码 (楼宇,客户,商机,...)
     */
    private String bizCode = "";

    /**
     * 业务状态(字典xx)
     */
    private String status = "";

    /**
     * 状态变更时间
     */
    private LocalDateTime changeTime = null;

    /**
     * 状态变更操作人
     */
    private Long operator = 0L;

    /**
     * 状态变更操作人工号
     */
    private String operatorWno = "";

    /**
     * 状态变更操作人姓名
     */
    private String operatorName = "";

    /**
     * 补充内容
     */
    private String content;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Byte deleteFlag = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Getter
    @AllArgsConstructor
    public enum BizType {
        BUILDING("0042-1", "楼宇"),
        RATING("0042-2", "评级"),
        CUSTOMER("0042-3", "客户"),
        BUSINESS("0042-4", "商机"),
        CONTRACT("0042-5", "合同");

        private final String code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum BizStatus {
        /** 待洽谈 */
        WAIT_NEGOTIATION("0043-1", "待洽谈"),

        /** 初步洽谈 */
        PRELIMINARY_NEGOTIATION("0043-2", "初步洽谈"),

        /** 达成意向 */
        INTENTION_ACCOMPLISHED("0043-3", "达成意向"),

        /** 方案报价 */
        QUOTATION("0043-4", "方案报价"),

        /** 合同流程 */
        CONTRACT_PROCESS("0043-5", "合同流程"),

        /** 成交 */
        DEAL("0043-6", "成交"),

        /** 解约 */
        DISCARD("0043-7", "解约"),

        /** 无效 */
        INVALID("0043-8", "无效");

        private final String code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum RatingApplicationStatus {
        /** 待审核  7.28描述调整，待审核改为审核中*/
        WAIT_APPROVED("0046-1", "审核中"),
        /** 已通过 */
        APPROVED("0046-2", "已通过"),
        /** 不通过 */
        NOT_APPROVED("0046-3", "不通过"),
        /** 已驳回 */
        REJECTED("0046-4", "已驳回"),
        /** 已放弃 */
        ABANDONED("0046-5", "已放弃");

        private final String code;
        private final String desc;
    }
}

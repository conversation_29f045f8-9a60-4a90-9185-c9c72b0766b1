package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.dataimport.pojo.PointPlanUpdateStatusBO;
import com.coocaa.meht.module.web.dao.PointPlanMapper;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.service.PointPlanService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PointPlanServiceImpl extends ServiceImpl<PointPlanMapper, PointPlanEntity> implements PointPlanService {
    @Override
    public List<PointPlanEntity> listByBusinessCode(List<String> businessCodeList) {
        return lambdaQuery().in(PointPlanEntity::getBusinessCode, businessCodeList)
                .list();
    }

    @Override
    public PointPlanEntity getByBusinessCode(String businessCode) {
        return lambdaQuery().eq(PointPlanEntity::getBusinessCode, businessCode)
                .last("LIMIT 1")
                .one();
    }

    @Override
    public void updatePointPlanStatus(PointPlanUpdateStatusBO bo) {
        if (CollUtil.isEmpty(bo.getPointPlanIds()) || StrUtil.isBlank(bo.getStatus())) {
            return;
        }
        this.lambdaUpdate().in(PointPlanEntity::getId, bo.getPointPlanIds())
                .set(PointPlanEntity::getStatus, bo.getStatus())
                .update();
    }
}
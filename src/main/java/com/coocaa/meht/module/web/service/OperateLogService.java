package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.entity.OperateLogEntity;
import com.coocaa.meht.module.web.enums.BusinessTypeEnum;

/**
 * 操作日志服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
public interface OperateLogService extends IService<OperateLogEntity> {

    /**
     * 保存操作日志
     *
     * @param businessType
     * @param businessNo
     * @param description
     * @param OperateType
     * @param changeDesc
     */
    void saveOperateLog(Integer businessType, String businessNo, String description, String OperateType, String changeDesc);
}

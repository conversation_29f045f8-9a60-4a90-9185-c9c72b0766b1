package com.coocaa.meht.module.web.controller;

import cn.hutool.core.collection.CollUtil;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.dto.BuildingBrandDto;
import com.coocaa.meht.module.web.dto.convert.BuildingBrandConvert;
import com.coocaa.meht.module.web.entity.BuildingBrandEntity;
import com.coocaa.meht.module.web.service.BuildingBrandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 楼宇品牌
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-22
 */
@Slf4j
@RestController
@RequestMapping("/building/brands")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingBrandController {
    private final BuildingBrandService buildingBrandService;

    /**
     * 楼宇品牌-列表
     */
    @GetMapping
    @Reqlog(value = "楼宇品牌列表", type = Reqlog.LogType.SELECT)
    public Result<List<BuildingBrandDto>> listAllBrands(@RequestParam(name = "status", required = false) Integer status) {
        List<BuildingBrandEntity> brands = buildingBrandService.lambdaQuery()
                .eq(Objects.nonNull(status), BuildingBrandEntity::getStatus, status)
                .orderByAsc(BuildingBrandEntity::getRank).orderByDesc(BuildingBrandEntity::getId)
                .list();
        return Result.ok(CollUtil.isEmpty(brands)
                ? Collections.emptyList()
                : BuildingBrandConvert.INSTANCE.toList(brands));
    }

    /**
     * 楼宇品牌-新增
     */
    @PostMapping
    @Reqlog(value = "楼宇品牌新增", type = Reqlog.LogType.INSERT)
    public Result<Boolean> createBrand(@RequestBody BuildingBrandDto brand) {
        BuildingBrandEntity entity = BuildingBrandConvert.INSTANCE.toEntity(brand);
        entity.setStatus(1);
        return Result.ok(buildingBrandService.save(entity));
    }

    /**
     * 楼宇品牌-修改
     */
    @PutMapping("/{id}")
    @Reqlog(value = "楼宇品牌修改", type = Reqlog.LogType.UPDATE)
    public Result<Boolean> updateBrand(@PathVariable(name = "id") Integer id, @RequestBody BuildingBrandDto brand) {
        BuildingBrandEntity entity = BuildingBrandConvert.INSTANCE.toEntity(brand);
        entity.setId(id);
        entity.setUpdateTime(null);
        return Result.ok(buildingBrandService.updateById(entity));
    }

    /**
     * 楼宇品牌-删除
     */
    @DeleteMapping("/{id}")
    @Reqlog(value = "楼宇品牌新增", type = Reqlog.LogType.DELETE)
    public Result<Boolean> deleteBrand(@PathVariable(name = "id") Integer id) {
        boolean result = buildingBrandService.lambdaUpdate()
                .set(BuildingBrandEntity::getStatus, 0)
                .eq(BuildingBrandEntity::getId, id)
                .update();
        return Result.ok(result);
    }
}
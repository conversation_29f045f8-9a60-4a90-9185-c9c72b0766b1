package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Created by fengke on 2024/11/15.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("city_coefficient")
public class CityCoefficientEntity extends BaseEntity{
    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String region;

    /**
     * 系数值
     */
    private BigDecimal coefficient;


    /**
     * 行政区域编码
     */
    private Integer adCode;
}

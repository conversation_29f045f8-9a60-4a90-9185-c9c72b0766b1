package com.coocaa.meht.module.web.service;

import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.module.web.dto.BusinessOpportunityDto;
import com.coocaa.meht.module.web.dto.crm.CrmCustomerResultDto;
import com.coocaa.meht.module.web.dto.req.BusinessReq;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;

import java.util.List;

public interface DataHandlerService {

    /**
     * 楼宇信息处理
     * @param entity
     */
    void handlerBuildingRating(BuildingRatingEntity entity, LoginUser user);

    /**
     * crm 定时推送
     * @return
     */
    CrmCustomerResultDto crmHandlerBuildingRating(String buildingNo);

    /**
     * 查询crm userId
     * @param username
     * @param phone
     * @return
     */
    String getCrmOwnerUserId(String username, String phone);


    /**
     * 同步推送到crm的楼宇数据未关联 customerId
     */
    void syncCrmCustomerId();

    void fixOwnerUserId();

    /**
     * 获取token
     * @return
     */
    String getToken(boolean forceRefresh);

    /**
     * 添加商机业务
     */
    void addBusiness(BusinessReq req);

    /**
     * 完善商机业务
     */
    void updateBusiness(BusinessReq req);

    /**
     * 获取商机业务
     */
    List<BusinessOpportunityDto> getCustomerBusiness(String buildingNo);

    /**
     * 获取场景类型
     */
    List<CodeNameVO> getSceneTypeEnum();
}

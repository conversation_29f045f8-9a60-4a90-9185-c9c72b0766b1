package com.coocaa.meht.module.web.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
@AllArgsConstructor
public enum PropertyCompanyTypeEnum implements IEnumType<Integer> {
    ENTERPRISE(1, "企业"),
    PERSON(2, "个人");

    private final Integer code;
    private final String desc;

    private final static Map<Integer, PropertyCompanyTypeEnum> BY_CODE_MAP =
            Arrays.stream(PropertyCompanyTypeEnum.values())
                    .collect(Collectors.toMap(PropertyCompanyTypeEnum::getCode, Function.identity()));

    /**
     * 将代码转成枚举
     */
    public static PropertyCompanyTypeEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static PropertyCompanyTypeEnum parse(Integer code, PropertyCompanyTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(PropertyCompanyTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}

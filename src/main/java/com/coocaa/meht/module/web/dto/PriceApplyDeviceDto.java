package com.coocaa.meht.module.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 价格申请设备
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-28
 */
@Data
public class PriceApplyDeviceDto {
    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空")
    @Schema(description = "设备类型")
    private String type;

    /**
     * 设备尺寸
     */
    @NotBlank(message = "设备尺寸不能为空")
    @Schema(description = "设备尺寸")
    private String size;

    /**
     * 设备数量
     */
    @NotNull(message = "设备数量不能为空")
    @Schema(description = "设备数量")
    private Integer quantity;

    /**
     * 签约单价(元/台/年)
     */
    @NotNull(message = "签约单价不能为空")
    @DecimalMin(value = "0.0", message = "签约单价不能小于0")
    @Schema(description = "签约单价(元/台/年)")
    private BigDecimal signPrice;

    /**
     * 安装位置
     */
    @NotBlank(message = "安装位置不能为空")
    @Schema(description = "安装位置")
    private String location;

    /**
     * 安装位置 JSON
     */
    @Schema(description = "安装位置 JSON")
    private String locationDetail;

    /**
     * 设备点位
     */
    @Schema(description = "设备点位")
    private List<PriceApplyDetailDto.DevicePointDto> points;

    /**
     * 激励单价(元/台/月)
     */
    @NotNull(message = "激励单价不能为空")
    @DecimalMin(value = "0.0", message = "激励单价不能小于0")
    @Schema(description = "激励单价(元/台/月)")
    private BigDecimal incentivePrice;
}

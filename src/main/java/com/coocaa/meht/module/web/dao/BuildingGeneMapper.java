/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 楼宇基因数据访问层
 * 继承MyBatis-Plus的BaseMapper实现基础的CRUD操作
 * 提供对building_gene表的数据库操作接口
 * 
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface BuildingGeneMapper extends BaseMapper<BuildingGeneEntity> {
} 
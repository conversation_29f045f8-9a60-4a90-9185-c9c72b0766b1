package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.module.web.dao.PointContractSnapshotMapper;
import com.coocaa.meht.module.web.dto.convert.PointConvert;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.PointDetailParam;
import com.coocaa.meht.module.web.dto.point.ProjectPointVO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PointContractSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointPicContractSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.IPointContractSnapshotService;
import com.coocaa.meht.module.web.service.IPointPicContractSnapshotService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.service.PointService;
import com.google.common.collect.Lists;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 点位合同快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
public class PointContractSnapshotServiceImpl extends ServiceImpl<PointContractSnapshotMapper, PointContractSnapshotEntity> implements IPointContractSnapshotService {

    @Autowired
    private BuildingRatingService buildingRatingService;
    @Autowired
    private ConverterFactory converterFactory;
    @Autowired
    private PointConvert pointConvert;
    @Autowired
    private IPointPicContractSnapshotService pointPicContractSnapshotService;
    @Autowired
    private PointService pointService;
    @Autowired
    private PointPlanService planService;
    @Override
    public List<PointContractSnapshotEntity> listByBuildingNos(Collection<String> buildingNos) {
        if(CollectionUtil.isNotEmpty(buildingNos)){
            return lambdaQuery().in(PointContractSnapshotEntity::getBuildingRatingNo,buildingNos).list();
        }
        return new ArrayList<>();
    }

    @Override
    public ProjectPointVO listBuildingPoint(PointDetailParam param) {
        ProjectPointVO vo = new ProjectPointVO();
        String buildingNo = param.getBuildingNo();
        BuildingRatingEntity entity = buildingRatingService.getByBuildingNo(buildingNo);
        String buildingName = entity.getBuildingName();
        vo.setProjectName(buildingName);
        Integer pointPlanId = param.getPointPlanId();
        List<PointContractSnapshotEntity> list = listByPointPlanId(pointPlanId);

        if(CollectionUtil.isNotEmpty(list)){
            Set<String> codes = list.stream().map(PointContractSnapshotEntity::getCode).collect(Collectors.toSet());
            Map<String, String> pointDeviceSize = pointService.lambdaQuery().select(PointEntity::getCode, PointEntity::getDeviceSize)
                    .in(PointEntity::getCode, codes)
                    .list().stream().collect(Collectors.toMap(PointEntity::getCode, e -> e.getDeviceSize()));
            //方案状态
            PointPlanEntity planEntity = planService.getById(pointPlanId);
            String pointStatus = planEntity.getStatus();
            vo.setPointPlanStatus(pointStatus);
            List<PointDetail> detail = pointConvert.toPointDetailsByContract(list);
            detail.forEach(e->{
                e.setDeviceSize(pointDeviceSize.get(e.getPointCode()));
            });
            converterFactory.convert(detail);
            vo.setPointDetails(detail);
            setPics(detail);
        }
        converterFactory.convert(Lists.newArrayList(vo));
        return vo;
    }

    private void setPics(List<PointDetail> detail) {
        List<Integer> pointIds = detail.stream().map(PointDetail::getPointId)
                .collect(Collectors.toList());
        List<PointPicContractSnapshotEntity> entityList = pointPicContractSnapshotService.listByPointIds(pointIds);
        if(CollectionUtil.isNotEmpty(entityList)){
            Map<Integer, List<PointPicContractSnapshotEntity>> map =
                    entityList.stream().collect(Collectors.groupingBy(PointPicContractSnapshotEntity::getPointId));
            detail.forEach(e->{
                List<PointPicContractSnapshotEntity> pics = map.get(e.getPointId());
                if(CollectionUtil.isNotEmpty(pics)){
                    e.setPointPics(pics.stream().map(PointPicContractSnapshotEntity::getPic)
                            .collect(Collectors.toList()));
                }

            });
        }
    }
    private List<PointContractSnapshotEntity> listByPointPlanId(Integer pointPlanId) {
        return lambdaQuery().eq(PointContractSnapshotEntity::getPointPlanId,pointPlanId)
                .list();
    }
}
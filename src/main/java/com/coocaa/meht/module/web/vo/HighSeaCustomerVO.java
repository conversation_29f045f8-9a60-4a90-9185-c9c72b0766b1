package com.coocaa.meht.module.web.vo;

import com.coocaa.meht.converter.Convert;
import com.coocaa.meht.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Data
public class HighSeaCustomerVO {

    @Schema(description = "楼宇id")
    private Long id;

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "楼宇类型字典")
    @Convert(type = ConvertType.BUILDING_TYPE)
    private Integer buildingType;

    @Schema(description = "楼宇类型")
    private String buildingTypeName;

    @Schema(description = "详细地址")
    private String mapAddress;

    @Schema(description = "掉入公海时间")
    private LocalDateTime enterSeaTime;

}

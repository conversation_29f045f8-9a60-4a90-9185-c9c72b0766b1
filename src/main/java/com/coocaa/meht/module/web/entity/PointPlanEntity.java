package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("point_plan")
public class PointPlanEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String buildingRatingNo;
    /**
     * 签约状态字典0037
     * */
    private String status;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    /**
     * 商机编码
     * */
    private String businessCode;
} 
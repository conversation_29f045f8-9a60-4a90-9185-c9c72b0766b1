package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.CityCoefficientDao;
import com.coocaa.meht.module.web.entity.CityRentEntity;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by fengke on 2024/11/15.
 */
@Service
public class CityCoefficientServiceImpl extends ServiceImpl<CityCoefficientDao, CityCoefficientEntity> implements CityCoefficientService {

    @Override
    public CityCoefficientEntity getCoefficient(String adCode) {
        List<CityCoefficientEntity> list = this.listByMap(Maps.of("ad_code", adCode));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}

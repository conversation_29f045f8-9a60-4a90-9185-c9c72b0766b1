package com.coocaa.meht.module.web.dto.property;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
public class PropertyCompanyPersonParam {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 物业公司id
     */
    @Schema(description = "物业公司id", type = "Int", example = "1")
    private Integer companyId;

    /**
     * 角色
     */
    @NotBlank(message = "联系人角色不能为空")
    @Schema(description = "联系人角色", type = "String", example = "项目负责人")
    private String role;

    /**
     * 姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    @Size(min = 2, max = 8, message = "联系人姓名长度必须在2-8之间")
    @Schema(description = "联系人姓名", type = "String", example = "张三")
    private String name;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Size(min = 2, max = 11, message = "手机号码长度必须在2-8之间")
    @Schema(description = "手机号码", type = "String", example = "183****4368")
    private String phone;

    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱", type = "String", example = "1")
    private String email;
}

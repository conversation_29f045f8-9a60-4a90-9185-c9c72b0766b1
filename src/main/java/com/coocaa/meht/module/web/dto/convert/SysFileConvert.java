package com.coocaa.meht.module.web.dto.convert;


import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.web.dto.PriceApplyDetailDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface SysFileConvert {

    SysFileConvert INSTANCE = Mappers.getMapper(SysFileConvert.class);

    PriceApplyDetailDto.FilesDetailDto toFilesDetailDto(SysFileEntity sysFileEntity);

    List<PriceApplyDetailDto.FilesDetailDto> toFilesDetailDto(List<SysFileEntity> sysFileEntityList);
}

package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 点位图片价格快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("point_pic_price_snapshot")
public class PointPicPriceSnapshotEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 点位id
     */
    private Integer pointId;

    /**
     * cos地址
     */
    private String pic;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 
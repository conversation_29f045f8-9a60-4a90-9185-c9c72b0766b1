package com.coocaa.meht.module.web.vo.proxy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description 楼宇品牌
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BuildingBrandPointVO {
    /**
     * 品牌编号
     */
    @Schema(description = "品牌编号")
    private String brandCode;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区")
    private String county;

    @Schema(description = "地图id")
    private String mapNo;

    @Schema(description = "等级")
    private String projectLevel;

    @Schema(description = "省份")
    private String province;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String building;
    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇类型")
    private Integer buildingType;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 维度
     */
    @Schema(description = "维度")
    private String latitude;

    /**
     * 设备数
     */
    @Schema(description = "设备数")
    private Integer total;


    /**
     * 设备数
     */
    @Schema(description = "是否是大屏 [1:小屏, 2:大屏, 3:大小屏]")
    private Integer largeScreen;

    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingNo;
}

package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.PointPicPriceSnapshotMapper;
import com.coocaa.meht.module.web.entity.PointPicPriceSnapshotEntity;
import com.coocaa.meht.module.web.service.IPointPicPriceSnapshotService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 点位价格图片快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
public class PointPicPriceSnapshotServiceImpl extends ServiceImpl<PointPicPriceSnapshotMapper, PointPicPriceSnapshotEntity> implements IPointPicPriceSnapshotService {

    @Override
    public List<PointPicPriceSnapshotEntity> listByPointIds(List<Integer> pointIds) {
        if(CollectionUtil.isNotEmpty(pointIds)){
            return lambdaQuery().in(PointPicPriceSnapshotEntity::getPointId,pointIds).list();
        }
        return new ArrayList<>();
    }
}
package com.coocaa.meht.module.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.BuildingPropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyPersonMapper;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.dto.convert.BuildingPropertyCompanyConvert;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyPersonConvert;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.IBuildingPropertyCompanyService;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingPropertyCompanyServiceImpl extends ServiceImpl<BuildingPropertyCompanyMapper, BuildingPropertyCompanyEntity> implements IBuildingPropertyCompanyService {

    private final PropertyCompanyMapper companyMapper;
    private final PropertyCompanyPersonMapper personMapper;

    @Override
    public List<BuildingPropertyCompanyVO> getDetail(String code, String buildingNo) {
        List<BuildingPropertyCompanyVO> resultVOS = new ArrayList<>();
        // code存在则查该商机下物业，如果有客户id则查客户下所有物业
        // 商机物业关联表
        List<BuildingPropertyCompanyEntity> entities = this.list(new LambdaQueryWrapper<BuildingPropertyCompanyEntity>()
                .eq(StringUtils.isNotBlank(code), BuildingPropertyCompanyEntity::getProjectCode, code)
                .eq(StringUtils.isNotBlank(buildingNo), BuildingPropertyCompanyEntity::getBuildingNo, buildingNo));
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        // 物业
        List<Integer> buildingCompanyIds = entities.stream().map(BuildingPropertyCompanyEntity::getPropertyId).toList();
        Map<Integer, PropertyCompanyEntity> companyMap = companyMapper.selectList(new LambdaQueryWrapper<PropertyCompanyEntity>()
                .eq(PropertyCompanyEntity::getStatus, BooleFlagEnum.YES.getCode())
                .in(PropertyCompanyEntity::getId, buildingCompanyIds)).stream()
                .collect(Collectors.toMap(PropertyCompanyEntity::getId, c -> c));
        if (CollectionUtils.isEmpty(companyMap)) {
            return Collections.emptyList();
        }
        // 物业联系人
        Map<Integer, List<PropertyCompanyPersonEntity>> personMap = personMapper.selectList(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                        .in(PropertyCompanyPersonEntity::getCompanyId, companyMap.keySet())).stream()
                .collect(Collectors.groupingBy(PropertyCompanyPersonEntity::getCompanyId));
        // 组装
        entities.forEach(entity -> {
            if (Objects.nonNull(companyMap.get(entity.getPropertyId()))) {
                BuildingPropertyCompanyVO resultVO = new BuildingPropertyCompanyVO();
                BeanUtils.copyProperties(entity, resultVO);
                BeanUtils.copyProperties(companyMap.get(entity.getPropertyId()), resultVO);
                resultVO.setPersonVOS(PropertyCompanyPersonConvert.INSTANCE.toVOs(personMap.getOrDefault(entity.getPropertyId(), Collections.emptyList())));
                resultVOS.add(resultVO);
            }
        });
        return resultVOS;
    }

    @Override
    public Integer saveBuildingProperty(BuildingPropertyCompanyParam param) {
        BuildingPropertyCompanyEntity entity = BuildingPropertyCompanyConvert.INSTANCE.toEntity(param);
        // 若该商机存在关联物业则修改,不存在则新增
        int count = this.baseMapper.update(new LambdaUpdateWrapper<BuildingPropertyCompanyEntity>().eq(BuildingPropertyCompanyEntity::getProjectCode, param.getProjectCode())
                .set(BuildingPropertyCompanyEntity::getPropertyId, param.getPropertyId())
                .set(BuildingPropertyCompanyEntity::getPropertyName, param.getPropertyName()));
        if (count < 1) {
            this.saveOrUpdate(entity);
        }
        return entity.getId();
    }
}

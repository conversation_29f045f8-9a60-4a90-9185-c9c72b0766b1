package com.coocaa.meht.module.web.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 业务类型枚举
 * @since 2025-04-29
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {



    /**
     * 楼宇
     */
    BUILDING_RATING(1, "楼宇评级"),

    /**
     * 价格申请
     */
    PRICE_APPLY(2, "价格申请"),
    /**
     * 完善评级
     */
    COMPLETE_RATING(3, "完善评级");



    /**
     * 类型值
     * -- GETTER --
     *  获取value

     */
    @Getter
    private final Integer value;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static BusinessTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (BusinessTypeEnum type : BusinessTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }

        return null;
    }
}
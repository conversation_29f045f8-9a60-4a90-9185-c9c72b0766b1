/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.ValidationGroups;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.module.building.convert.BuildingGeneConvert;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.web.dao.BuildingGeneMapper;
import com.coocaa.meht.module.web.dto.BuildingGeneDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ValidatorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 楼宇基因服务实现类
 * 实现楼宇基因相关的业务操作
 * 提供楼宇基因信息的增删改查等基础服务的具体实现
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildingGeneServiceImpl extends ServiceImpl<BuildingGeneMapper, BuildingGeneEntity> implements BuildingGeneService {


    private final LargeScreenProperties largeScreenProperties;

    private final ValidatorFactory validatorFactory;

    private final CodeNameHelper codeNameHelper;


    private final BuildingScreenService buildingScreenService;

    private final IBuildingMetaService buildingMetaService;

    @Override
    public BuildingGeneVO getBuildingGeneByNo(String buildingRatingNo) {
        if (StrUtil.isBlank(buildingRatingNo)) {
            return null;
        }
        BuildingGeneEntity buildingGene = this.lambdaQuery().eq(BuildingGeneEntity::getBuildingRatingNo, buildingRatingNo).one();
        if (Objects.isNull(buildingGene)) {
            return null;
        }
        BuildingGeneVO vo = BeanUtil.copyProperties(buildingGene, BuildingGeneVO.class);
        if (StringUtils.isNotBlank(buildingGene.getCompetitiveMediaInfo())) {
            List<String> mediaInfoList = Arrays.asList(buildingGene.getCompetitiveMediaInfo().split(","));
            vo.setCompetitiveMediaInfos(mediaInfoList);
        }
        return vo;
    }

    @Override
    public Boolean isScreen(BuildingGeneEntity entity) {
        if (entity == null || StrUtil.isBlank(entity.getSpec())) {
            return false;
        }
        List<String> codeList = JSON.parseArray(entity.getSpec(), String.class);
        List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();
        return CollUtil.isNotEmpty(CollectionUtil.intersection(largeDeviceKey, codeList));
    }

    @Override
    public Boolean isScreen(String buildingRatingNo) {
        if (StrUtil.isBlank(buildingRatingNo)) {
            return false;
        }
        BuildingGeneEntity buildingGene = this.lambdaQuery().eq(BuildingGeneEntity::getBuildingRatingNo, buildingRatingNo).one();
        return this.isScreen(buildingGene);
    }

    @Override
    public List<String> validateGene(BuildingGeneDTO dto) {
        if (dto == null) {
            throw new ServerException("校验对象有误");
        }

        // 1. 根据大小屏决定使用哪个校验组
        Class<?>[] validationGroups;
        BuildingRatingEntity buildingRatingEntity = new BuildingRatingEntity();
        buildingRatingEntity.setBuildingNo(dto.getBuildingRatingNo());
        Boolean largeScreen = buildingScreenService.isLargeScreen(buildingRatingEntity);
        if (largeScreen) {
                validationGroups = new Class<?>[]{ValidationGroups.Small.class, ValidationGroups.Large.class};
        } else {
            validationGroups = new Class<?>[]{ValidationGroups.Small.class};
        }

        log.info("执行大屏-{}校验，楼间距: {}, 挑高: {}", largeScreen, dto.getBuildingSpacing(), dto.getBuildingCeilingHeight());
        // 2. 执行校验
        Set<ConstraintViolation<BuildingGeneDTO>> validate = validatorFactory.getValidator().validate(dto, validationGroups);

        if (CollectionUtil.isEmpty(validate)) {
            return new ArrayList<>();
        }
        return validate.stream().map(ConstraintViolation::getMessage).toList();
    }

    @Override
    public BuildingGeneEntity saveOrUpdate(String buildingNo, RatingApplyDto param) {
        BuildingGeneEntity geneEntity = BuildingGeneConvert.INSTANCE.toBuildingGeneByDto(param);

        geneEntity.setBuildingRatingNo(buildingNo);
        fillGeneFiled(param, geneEntity);

        BuildingGeneEntity existGene = lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.nonNull(existGene)) {
            geneEntity.setId(existGene.getId());
            updateById(geneEntity);
        } else {
            save(geneEntity);
        }
        return geneEntity;
    }

    @Override
    public BuildingGeneVO detail(Integer id) {
        BuildingGeneEntity buildingGene = this.getById(id);
        BuildingGeneVO buildingGeneVO = BeanUtil.copyProperties(buildingGene, BuildingGeneVO.class);
        if (StringUtils.isNotBlank(buildingGene.getCompetitiveMediaInfo())) {
            List<String> mediaInfoList = Arrays.asList(buildingGene.getCompetitiveMediaInfo().split(","));
            buildingGeneVO.setCompetitiveMediaInfos(mediaInfoList);
            // 按逗号截取
            Map<String, String> mediaTypeDictMapping = codeNameHelper.getDictMapping(mediaInfoList);

            // 使用String.join方法进行字符串拼接
            String res = String.join("，", mediaTypeDictMapping.values());
            buildingGeneVO.setCompetitiveMediaInfoName(res);
        }
        return buildingGeneVO;
    }

    private void fillGeneFiled(RatingApplyDto dto, BuildingGeneEntity buildingGene) {
        // 日租金
        if (Objects.nonNull(dto.getDailyPriceInput()) && dto.getDailyPriceInput().compareTo(BigDecimal.ZERO) != 0) {
            buildingGene.setDailyPrice(dto.getDailyPriceInput());
        } else {
            buildingGene.setDailyPrice(dto.getDailyPrice());
        }

        // 目标点位数 - 添加空指针检查
        if (dto.getTargetPointCount() != null) {
            buildingGene.setTargetPointCount(dto.getTargetPointCount());
        }

        // 楼宇数量转换
        if (StringUtils.isNotBlank(dto.getBuildingNumberInput())) {
            try {
                buildingGene.setMaxFloorCount(Integer.valueOf(dto.getBuildingNumberInput().trim()));
            } catch (NumberFormatException e) {
                log.error("楼宇数量转换异常, input: {}", dto.getBuildingNumberInput(), e);
            }
        }

        //竞煤信息
        if (CollectionUtils.isNotEmpty(dto.getCompetitiveMediaInfos())) {
            buildingGene.setCompetitiveMediaInfo(String.join(",", dto.getCompetitiveMediaInfos()));
        }

        // 行业信息
        if (CollectionUtils.isNotEmpty(dto.getForbiddenIndustry())) {
            if (dto.getForbiddenIndustry().contains("无") && dto.getForbiddenIndustry().size() > 1){
                throw new ServerException("禁止行业选择无不能再选其他的行业");
            }
            buildingGene.setForbiddenIndustry(String.join(",", dto.getForbiddenIndustry()));

            //修改meta
            buildingMetaService.lambdaUpdate()
                    .set(BuildingMetaEntity::getForbiddenIndustry, String.join(",", dto.getForbiddenIndustry()))
                    .eq(BuildingMetaEntity::getBuildingRatingNo, dto.getBuildingNo())
                    .update();
        }
    }

}
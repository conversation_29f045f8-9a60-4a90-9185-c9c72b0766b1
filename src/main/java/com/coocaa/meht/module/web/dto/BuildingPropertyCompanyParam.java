package com.coocaa.meht.module.web.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
public class BuildingPropertyCompanyParam {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 楼宇编码
     */
    @NotBlank(message = "楼宇编码不能为空")
    @Schema(description = "楼宇编码", type = "String", example = "1")
    private String buildingNo;

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空")
    @Schema(description = "项目编号", type = "String", example = "1")
    private String projectCode;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Schema(description = "项目名称", type = "String", example = "xx项目")
    private String projectName;

    /**
     * 物业公司id
     */
    @NotBlank(message = "物业公司id不能为空")
    @Schema(description = "物业公司id", type = "Int", example = "1")
    private Integer propertyId;

    /**
     * 物业公司名称
     */
    @Schema(description = "物业公司名称", type = "String", example = "xx公司")
    private String propertyName;


}

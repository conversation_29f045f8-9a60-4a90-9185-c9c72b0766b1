package com.coocaa.meht.module.web.service.impl;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.BuildingMetaImgRelationMapper;
import com.coocaa.meht.module.web.dto.BuildingMetaImgListDto;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 楼宇主数据图片管理信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class BuildingMetaImgRelationServiceImpl extends ServiceImpl<BuildingMetaImgRelationMapper, BuildingMetaImgRelationEntity> implements IBuildingMetaImgRelationService {

    @Autowired
    private BuildingMetaImgRelationMapper buildingMetaImgRelationMapper;
    @Override
    public BuildingMetaImgListDto getImgByBuildingNo(String buildingNo) {
        BuildingMetaImgListDto dto=new BuildingMetaImgListDto();
     List<BuildingMetaImgRelationEntity> imgEntityList=   buildingMetaImgRelationMapper.selectList(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery().eq(BuildingMetaImgRelationEntity::getBuildingMetaNo,buildingNo));
       if(CollectionUtils.isEmpty(imgEntityList)){
           return dto;
       }
        imgEntityList.stream().forEach(entity->{
//            if(BuildingMetaImgTypeEnum.EXTERIOR_PIC)
        });
        return dto;
    }
}

package com.coocaa.meht.module.web.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-16
 */
@TableName(value ="operate_log")
@Data
public class OperateLogEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据类型(1:楼宇)
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 业务ID
     */
    @TableField(value = "biz_id")
    private String bizId;

    /**
     * 描述说明
     */
    @TableField(value = "description")
    private String description;

    /**
     * 操作类型
     */
    @TableField(value = "operate_type")
    private String operateType;

    /**
     * 变更内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 状态变更操作人
     */
    @TableField(value = "creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.sys.dao.SysRequestLogDao;
import com.coocaa.meht.module.sys.dao.SysRequestLogDescDao;
import com.coocaa.meht.module.sys.entity.SysRequestLogDescEntity;
import com.coocaa.meht.module.sys.entity.SysRequestLogEntity;
import com.coocaa.meht.module.sys.service.SysRequestLogService;
import com.coocaa.meht.utils.IpUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 请求日志
 */
@Service
@RequiredArgsConstructor
public class SysRequestLogServiceImpl extends ServiceImpl<SysRequestLogDao, SysRequestLogEntity> implements SysRequestLogService {

    private final SysRequestLogDescDao sysRequestLogDescDao;

    /*@Override
    public PageResult<SysLogLoginVO> page(SysLogLoginQuery query) {
        Page<SysLogLoginEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
        return new PageResult<>(SysLogLoginConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
        return null;
    }*/

    @Async
    @Override
    public void saveAsync(SysRequestLogEntity entity) {
        if (entity.getErrorLog() == null) {
            entity.setErrorLog("");
        }
        if (entity.getErrorLog().length() > 60000) {
            entity.setErrorLog(entity.getErrorLog().substring(0, 60000));
        }
        SysRequestLogDescEntity desc = new SysRequestLogDescEntity();
        desc.setRequestParam(entity.getRequestParam());
        desc.setErrorLog(entity.getErrorLog());
        baseMapper.insert(entity);
        desc.setId(entity.getId());
        sysRequestLogDescDao.insert(desc);
    }

}
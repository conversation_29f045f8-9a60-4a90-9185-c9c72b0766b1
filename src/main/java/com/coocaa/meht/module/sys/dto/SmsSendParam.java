package com.coocaa.meht.module.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @program: cheese-authority
 * @ClassName SmsSendParam
 * @description:
 * @author: zhangbinxian
 * @create: 2025-05-08 14:39
 * @Version 1.0
 **/
@Data
public class SmsSendParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -5282278450491235949L;

    /**
     * 模板id
     */
    @NotEmpty(message = "模板id不能为空")
    @Schema(description = "模板id")
    private String templateId;

    /**
     * 手机号码
     */
    @NotEmpty(message = "手机号不能为空")
    @Schema(description = "手机号码")
    private String[] phoneNumbers;

    /**
     * 模板参数
     */
    @Schema(description = "模板参数")
    private String[] templateParams;

    /**
     * 签名
     */
    @Schema(description = "签名")
    @NotEmpty(message = "签名不能为空")
    private String signName;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @NotEmpty(message = "短信类型不能为空")
    private String smsType;

    /**
     *  REDIS前缀  用于区分不同业务的验证码
     *  示例：sale_plan_
     */
    @Schema(description = "验证码REDIS前缀  用于区分不同业务的验证码")
    private String redisPrefix;

    @Schema(description = "验证码redisKey")
    private String redisKey;

    /**
     * 类型为验证码的时候传入验证码并且不能为空
     */
    @Schema(description = "类型为验证码的时候传入验证码并且不能为空")
    private String code;

    /**
     * 验证码有效分钟数
     */
    @Schema(description = "验证码有效分钟数")
    private Integer codeMinutes = 0;

    @Schema(description = "发送频率redisKey")
    private String limitKey;

    @Schema(description = "发送频率有效分钟数, 发送频率有效分钟数小于等于0为没有发送频率校验")
    private Integer limitMinutes = 0;

    @Schema(description = "发送次数, 发送次数小于等于0为没有发送次数校验")
    private Integer limitCount = 0;

    @Schema(description = "发送次数redisKey")
    private String limitCountKey;

    @Schema(description = "发送次数有效分钟数")
    private Integer limitCountMinutes;
}

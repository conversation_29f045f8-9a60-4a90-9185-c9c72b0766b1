package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.sys.dao.SysFileDao;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.utils.FtpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 附件管理
 */
@Slf4j
@Service
public class SysFileServiceImpl extends ServiceImpl<SysFileDao, SysFileEntity> implements SysFileService {

    /**
     * 根据url查询
     *
     * @param url
     * @return
     */
    @Override
    public SysFileEntity getByUrl(String url) {
        return this.baseMapper.getByUrl(url);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysFileEntity uploadByte(String originalFilename, byte[] byt) {
        if (StringUtils.isBlank(originalFilename)) {
            throw new ServerException("文件名称不能为空");
        }
        int suffixIdx = originalFilename.lastIndexOf(".");
        if (suffixIdx < 0) {
            throw new ServerException("暂不支持当前的文件类型");
        }
        String suffix = originalFilename.substring(suffixIdx + 1);
        String filename = getFileId() + "." + suffix;
        SysFileEntity entity = new SysFileEntity().setUrl(FtpUtils.getPath(filename))
                .setName(originalFilename)
                .setSize((long) byt.length)
                .setAttachmentType(suffix);
        this.save(entity);
        ByteArrayInputStream bais = new ByteArrayInputStream(byt);
        FtpUtils.upload(filename, bais);
        return entity;
    }

    /**
     * 通用的文件上传
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysFileEntity upload(MultipartFile file) {
        if (file.getOriginalFilename() == null) {
            throw new ServerException("文件名称不能为空");
        }
        int suffixIdx = file.getOriginalFilename().lastIndexOf(".");
        if (suffixIdx < 0) {
            throw new ServerException("暂不支持当前的文件类型");
        }
        String suffix = file.getOriginalFilename().substring(suffixIdx + 1);
        if (!FtpUtils.FILE_TYPE.contains(suffix)) {
            throw new ServerException("暂不支持当前的文件类型");
        }
        String filename = getFileId() + "." + suffix;
        SysFileEntity entity = new SysFileEntity().setUrl(FtpUtils.getPath(filename))
                .setName(file.getOriginalFilename())
                .setSize(file.getSize())
                .setAttachmentType(file.getContentType());
        this.save(entity);
        try {
            FtpUtils.upload(filename, file.getInputStream());
        } catch (IOException e) {
            log.error("", e);
            throw new ServerException("上传失败，文件异常");
        }
        return entity;
    }

    @Override
    public List<SysFileEntity> getByUrls(Collection<String> urls) {
        if (CollectionUtils.isNotEmpty(urls)) {
            return this.lambdaQuery().in(SysFileEntity::getUrl, urls).list();
        }
        return Collections.emptyList();
    }

    @Override
    public void delete(Collection<String> urls) {
        if (CollectionUtils.isNotEmpty(urls)) {
            this.remove(new QueryWrapper<SysFileEntity>().in("url", urls));
        }
    }

    @Override
    public List<SysFileEntity> getBySysFileIdList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) return Collections.emptyList();
        LambdaQueryWrapper<SysFileEntity> queryWrapper = new QueryWrapper<SysFileEntity>().lambda().in(SysFileEntity::getId, ids);
        return this.list(queryWrapper);
    }

    private String getFileId() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

}
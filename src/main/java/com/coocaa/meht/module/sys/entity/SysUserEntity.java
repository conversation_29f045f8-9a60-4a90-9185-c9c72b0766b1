package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user")
public class SysUserEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工号
     */
    private String empCode;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 性别: 0男, 1女, 2未知
     */
    private Integer gender;
    /**
     * 入职日期
     */
    private LocalDateTime joinedDate;
    /**
     * 离职日期
     */
    private LocalDateTime terminationDate;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 飞书user_id
     */
    private String fsUserId;
    /**
     * 飞书open_id
     */
    private String fsOpenId;
    /**
     * 飞书union_id
     */
    private String fsUnionId;
    /**
     * 北森ID
     */
    private String beisenId;
    /**
     * 北森岗位名称
     */
    private String beisenPostName;
    /**
     * 员工状态  0：离职   1：在职
     */
    @TableField("`status`")
    private Integer status;
    /**
     * 员工类型 1：正式员工 2：外包员工 3：实习生 4:未知
     */
    private Integer empType;
    /**
     * 绩效评定人
     */
    private String performanceEvaluator;
    /**
     * 绩效评定人工
     */
    private String performanceEvaluatorCode;
    /**
     * 主组织id
     */
    private String orgCode;
    /**
     * 考核部门
     */
    private String assessDept;
    /**
     * 岗位编码
     */
    private String poCode;
    /**
     * 岗位名称
     */
    private String poName;
    /**
     * 删除标识: 0正常, 1已删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    public void setEmpTypeByTypeText(String employmentTypeText) {
        if (StringUtils.hasText(employmentTypeText)) {
            switch (employmentTypeText) {
                case "正式员工":
                    this.setEmpType(1);
                    break;
                case "外包员工":
                    this.setEmpType(2);
                    break;
                case "实习生":
                    this.setEmpType(3);
                    break;
                default:
                    this.setEmpType(4);
            }
        }
    }

}
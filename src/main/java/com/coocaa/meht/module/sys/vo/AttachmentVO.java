package com.coocaa.meht.module.sys.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 附件VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class AttachmentVO {
    /**
     * 附件ID
     */
    @Schema(description = "附件ID")
    private Long id;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称")
    private String name;

    /**
     * 附件地址
     */
    @Schema(description = "附件路径")
    private String url;

    /**
     * 附件大小
     */
    @Schema(description = "附件大小")
    private Long size;

    /**
     * 类型
     */
    @Schema(description = "附件名称")
    private String type;
}

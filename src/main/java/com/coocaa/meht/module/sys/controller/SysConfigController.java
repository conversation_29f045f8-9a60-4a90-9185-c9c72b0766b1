package com.coocaa.meht.module.sys.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.sys.service.SysConfigService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统配置
 */
@RestController
@RequestMapping("/sys/config")
@AllArgsConstructor
public class SysConfigController {

    @Resource
    private SysConfigService sysConfigService;

    @Anonymous
    @GetMapping("/getTimeOut")
    @Reqlog(value = "获取时间参数", type = Reqlog.LogType.SELECT)
    public Result<String> info() {
        String timeOut  = sysConfigService.getVal("out_time");
        return Result.ok(timeOut);
    }

    @Anonymous
    @GetMapping("/fs/appid")
    @Reqlog(value = "获取飞书appid", type = Reqlog.LogType.SELECT)
    public Result<String> getFsAppId() {
        String fsAppId = sysConfigService.getFsAppId();
        return Result.ok(fsAppId);
    }



}

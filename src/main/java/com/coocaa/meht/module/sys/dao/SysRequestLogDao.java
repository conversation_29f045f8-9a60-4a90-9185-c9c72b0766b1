package com.coocaa.meht.module.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.sys.entity.SysRequestLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 登录日志
 *
 * <AUTHOR>
 */
@Mapper
public interface SysRequestLogDao extends BaseMapper<SysRequestLogEntity> {
    @Select("select dt,CONVERT(count(*),DECIMAL(12,2)) v from (\n" +
            "select date(create_time) dt,user_code  from sys_request_log  where user_code !='' and \n" +
            "create_time >= NOW() - INTERVAL 30 DAY group by date(create_time),user_code ) tt group by tt.dt")
    List<Map<String,Object>> getUv();
}
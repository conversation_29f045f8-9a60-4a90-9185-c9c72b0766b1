package com.coocaa.meht.module.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.sys.entity.SysRoleMenuEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色与菜单对应关系
 */
@Mapper
public interface SysRoleMenuDao extends BaseMapper<SysRoleMenuEntity> {

	/**
	 * 根据角色编码，获取菜单ID列表
	 */
	@Select("select distinct menu_id from sys_role_menu where role_code = #{roleCode};")
	List<Long> getMenuIds(String roleCode);
}
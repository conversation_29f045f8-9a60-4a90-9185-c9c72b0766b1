package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024-11-06 17:48
 */
@Data
@Accessors(chain = true)
@TableName("sys_token")
public class StsTokenEntity implements Serializable {
    private static final long serialVersionUID = 7848799728504887070L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String userCode;
    private String platform;
    private String token;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}

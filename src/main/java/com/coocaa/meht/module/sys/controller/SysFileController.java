package com.coocaa.meht.module.sys.controller;

import cn.hutool.core.util.StrUtil;
import com.coocaa.ad.common.core.filter.SensitiveProperties;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.api.tencent.TencentCosService;
import com.coocaa.meht.module.sys.dto.AttachmentParam;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.vo.AttachmentVO;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.FtpUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.FileNameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 附件管理
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sys/file")
public class SysFileController {
    private final SysFileService sysFileService;
    private final TencentCosService tencentCosService;
    private final SensitiveProperties sensitiveProperties;

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @PostMapping("/upload")
    @Reqlog(value = "文件上传", type = Reqlog.LogType.INSERT)
    public Result<SysFileEntity> upload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }

        // 特殊处理附件名称是否包含敏感词
        checkFileName(file);

        return Result.ok(sysFileService.upload(file));
    }


    /**
     * cos文件上传
     * 逐步放弃，要与前端协调整改
     *
     * @param files
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadCos")
    @Reqlog(value = "文件上传", type = Reqlog.LogType.INSERT)
    public Result<?> uploadCos(MultipartFile[] files) throws IOException {
        if (files == null || ArrayUtils.isEmpty(files)) {
            return Result.error("请选择需要上传的文件");
        }

        // 特殊处理附件名称是否包含敏感词
        checkFileName(files);

        List<Map<String, Object>> mapList = tencentCosService.uploadCos(files);
        for (Map<String, Object> map : mapList) {
            SysFileEntity sysFileEntity = new SysFileEntity();
            sysFileEntity.setName(Converts.toStr(map.get("name"), ""));
            sysFileEntity.setUrl(Converts.toStr(map.get("url"), ""));
            sysFileEntity.setSize(Converts.toLong(map.get("size"), 0L));
            sysFileEntity.setAttachmentType(Converts.toStr(map.get("attachmentType"), ""));
            sysFileService.save(sysFileEntity);
            map.put("id", sysFileEntity.getId());
        }
        return Result.ok(mapList);
    }

    /**
     * 前端将文件上传COS后获取url
     *
     * @param params 附件参数
     * @return 附件信息
     */
    @PostMapping("/upload-after-cos")
    @Reqlog(value = "文件上传", type = Reqlog.LogType.INSERT)
    public Result<List<AttachmentVO>> uploadAfterCos(@RequestBody @Validated List<AttachmentParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Result.ok(Collections.emptyList());
        }

        // 参数检查
        for (AttachmentParam param : params) {
            if (StringUtils.isBlank(param.getName())) {
                throw new BusinessException("附件名称不能为空");
            }

            if (StringUtils.isBlank(param.getUrl())) {
                throw new BusinessException("附件url不能为空");
            }
        }

        // 用于保存到数据库
        List<SysFileEntity> attachments = params.stream().map(param -> {
            String type = param.getType();
            if (StringUtils.isBlank(type)) {
                type = FileNameUtils.getExtension(param.getUrl());
            }

            return new SysFileEntity()
                    .setName(param.getName())
                    .setUrl(param.getUrl())
                    .setSize(param.getSize())
                    .setAttachmentType(type);
        }).toList();


        // 保存数据
        try {
            boolean saved = sysFileService.saveBatch(attachments);
            if (!saved) {
                return Result.ok(Collections.emptyList());
            }
        } catch (Exception ex) {
            log.warn("保存附件数据失败", ex);
            throw new BusinessException("保存附件失败");
        }


        // 用于返回给前端
        List<AttachmentVO> vos = attachments.stream().map(item -> {
            AttachmentVO vo = new AttachmentVO();
            vo.setId(item.getId());
            vo.setName(item.getName());
            vo.setUrl(item.getUrl());
            vo.setSize(item.getSize());
            vo.setType(item.getAttachmentType());
            return vo;
        }).toList();

        return Result.ok(vos);
    }


    /**
     * 下载
     *
     * @param url
     * @param response
     * @throws Exception
     */
    @GetMapping("/download")
    @Reqlog(value = "文件下载", type = Reqlog.LogType.SELECT)
    public void downLoadFile(String url, HttpServletResponse response) throws Exception {
        SysFileEntity attachment = sysFileService.getByUrl(url);
        if (attachment != null) {
            String filename = URLEncoder.encode(attachment.getName(), "UTF-8");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + filename);
            response.addHeader("Content-Length", attachment.getSize().toString());
            FtpUtils.download(url, response.getOutputStream());
        }
    }


    /**
     * 检查文件名是否包含敏感词
     */
    private void checkFileName(MultipartFile... files) throws BusinessException {
        // 特殊处理附件名称是否包含敏感词
        StringBuilder sb = new StringBuilder();
        for (MultipartFile file : files) {
            sb.append(file.getOriginalFilename());
        }
        String fileNames = StrUtil.replace(sb.toString(), " ", "");
        List<String> sensitiveWords = StrUtil.split(sensitiveProperties.getWords(), ",", true, true);
        for (String word : sensitiveWords) {
            if (fileNames.contains(word)) {
                throw new BusinessException(sensitiveProperties.getPromptWords());
            }
        }
    }

}
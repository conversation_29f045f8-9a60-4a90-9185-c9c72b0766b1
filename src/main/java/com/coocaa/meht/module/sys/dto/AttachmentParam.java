package com.coocaa.meht.module.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 附件参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Data
public class AttachmentParam {
    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空")
    @Schema(description = "附件名称")
    private String name;

    /**
     * 附件地址
     */
    @NotBlank(message = "附件路径不能为空")
    @Schema(description = "附件路径")
    private String url;

    /**
     * 附件大小
     */
    @Schema(description = "附件大小")
    private Long size;

    /**
     * 类型
     */
    @Schema(description = "附件名称")
    private String type;
}

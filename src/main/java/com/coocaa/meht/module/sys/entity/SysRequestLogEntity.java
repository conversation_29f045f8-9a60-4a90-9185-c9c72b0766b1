package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 请求日志
 *
 * <AUTHOR>
 * @Date 2023-12-06 20:03
 */
@Data
@Accessors(chain = true)
@TableName("sys_request_log")
public class SysRequestLogEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户工号
     */
    private String userCode;
    /**
     * 类型：0登录，1查看，2新增，3修改，4删除
     */
    private Integer typesOf;
    /**
     * 标题
     */
    private String title;
    /**
     * 请求url
     */
    private String requestUrl;
    /**
     * 耗时（ms）
     */
    private Integer time;
    /**
     * 是否失败：0否，1是
     */
    @TableField("`status`")
    private Integer status;
    /**
     * IP
     */
    private String ip;
    /**
     * 地点
     */
    private String address;
    /**
     * 浏览器
     */
    private String browser;
    /**
     * 系统
     */
    private String os;
    /**
     * 平台
     */
    private String platform;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private String requestParam;

    @TableField(exist = false)
    private String errorLog;
}
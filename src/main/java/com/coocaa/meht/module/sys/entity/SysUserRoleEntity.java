package com.coocaa.meht.module.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户角色关系
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@TableName("sys_user_role")
public class SysUserRoleEntity extends BaseEntity {
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 工号
	 */
	private String userCode;
	/**
	 * 角色编码
	 */
	private String roleCode;
}
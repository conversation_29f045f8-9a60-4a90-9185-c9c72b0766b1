package com.coocaa.meht.module.sys.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.coocaa.meht.module.sys.dto.SysBuildingRatingDto;
import com.coocaa.meht.module.sys.dto.SysPersonnelApprovalDto;
import com.coocaa.meht.module.sys.service.SysBusinessService;
import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dao.PersonnelApprovalDao;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PersonnelApprovalEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;

/**
 * @program: cheese-meht
 * @ClassName SysBusinessServiceImpl
 * @description:
 * @create: 2024-12-24 11:44
 * @Version 1.0
 **/
@Service
public class SysBusinessServiceImpl implements SysBusinessService {

    @Autowired
    BuildingRatingDao buildingRatingDao;

    @Autowired
    PersonnelApprovalDao personnelApprovalDao;

    @Override
    public void savePersonnel(SysPersonnelApprovalDto personnelApprovalDto) {
        PersonnelApprovalEntity entity = new PersonnelApprovalEntity();
        BeanUtils.copyProperties(personnelApprovalDto, entity);
        personnelApprovalDao.insert(entity);
    }

    @Override
    public void updateApprovalInfo(SysPersonnelApprovalDto dto) {
        LambdaUpdateWrapper<PersonnelApprovalEntity> updateWrapper = new UpdateWrapper<PersonnelApprovalEntity>()
                .lambda().eq(PersonnelApprovalEntity::getThreeOrg, dto.getThreeOrg())
                .set(PersonnelApprovalEntity:: getApprovalName, dto.getApprovalName())
                .set(PersonnelApprovalEntity:: getApprovalCode, dto.getApprovalCode());
        personnelApprovalDao.update(updateWrapper);
    }

    @Override
    public void updateBuildingRating(SysBuildingRatingDto dto) {
        List<String> list = Arrays.asList(dto.getBuildingNos().split(","));
        QueryWrapper<PersonnelApprovalEntity> wrapper = new QueryWrapper<>();
        wrapper.in("building_no", list);
        LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new UpdateWrapper<BuildingRatingEntity>().lambda().in(BuildingRatingEntity::getBuildingNo, list);
        if(ObjectUtil.isNotEmpty(dto.getStatus())){
            updateWrapper.set(BuildingRatingEntity::getStatus, dto.getStatus());
        }
        if(ObjectUtil.isNotEmpty(dto.getBuildingStatus())){
            updateWrapper.set(BuildingRatingEntity::getBuildingStatus, dto.getBuildingStatus());
        }
        buildingRatingDao.update(updateWrapper);
    }
}

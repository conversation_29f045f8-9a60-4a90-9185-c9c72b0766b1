package com.coocaa.meht.module.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.common.LoginResultVO;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.UserAuthVO;
import com.coocaa.meht.common.bean.TokenResultVO;
import com.coocaa.meht.module.sys.dto.*;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户管理
 */
public interface SysUserService extends IService<SysUserEntity> {

    /**
     * 发送验证码
     *
     * @param phone
     * @param type
     * @return
     */
    UserAuthVO captcha(String phone, String type);

    /**
     * 域登录
     *
     * @param dto
     * @return
     */
    SysLoginTokenDto loginByEmail(SysEmailLoginDto dto);

    /**
     * 飞书登录
     *
     * @param dto
     * @return
     */
    LoginResultVO loginByFsCode(SysFsLoginDto dto);

    /**
     * 手机验证码登录
     *
     * @param dto
     * @return
     */
    LoginResultVO loginByPhone(SysPhoneLoginDto dto);

    TokenResultVO getTokenByRefreshToken(String refreshToken);

    /**
     * 退出
     *
     * @param accessToken
     */
    void logout(String accessToken);

    /**
     * 查询用户
     *
     * @param code
     * @return
     */
    SysUserEntity getByCode(String code);

    /**
     * 查询用户名称
     *
     * @param userCode
     * @return
     */
    String getName(String userCode);

    /**
     * 获取飞书ID
     *
     * @param userCode
     * @return
     */
    String getFsUserId(String userCode);
    String getFsOpenId(String userCode);

    Map<String, SysUserDto> getNameMaps(List<String> userCodes);

    Map<String, SysUserDto> getUserNameMaps(List<String> userCodes);

    /**
     * 同步用户
     */
    void syncUser();

    /**
     * 通过员工编号获取  用户的基本信息
     * @param empCode 员工编号
     * @return
     */
    LoginUser getUserByEmpCode(String empCode);


    List<LoginUser> listUserByEmpCode(List<String> empCodeList);

    /**
     * 通过手机号获取用户基本信息
     *
     * @param phone
     * @param wno   工号
     * @return
     */
    LoginUser getUserByPhone(String phone, String wno);

    Map<String, LoginUser> getUsers(Collection<String> wnos);

    void buildUserInfo(String token);

    void updateFeiShuUser(FeiShuUserInfoDto feiShuUserInfoDto);
}
package com.coocaa.meht.module.sys.controller;

import cn.hutool.core.util.ObjectUtil;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.sys.dto.SysBuildingRatingDto;
import com.coocaa.meht.module.sys.dto.SysPersonnelApprovalDto;
import com.coocaa.meht.module.sys.service.SysBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务审批
 *
 * @ClassName SysBusinessController
 * @description:
 * @author: 业务开放接口
 * @create: 2024-12-24 11:37
 * @Version 1.0
 **/
@RestController
@RequestMapping("/business")
public class SysBusinessController {

    @Value("${business_auth}")
    String businessAuth;

    @Autowired
    SysBusinessService sysBusinessService;

    /**
     * 添加员工审批列表
     * @param dto
     * @return
     */
    @Anonymous
    @PostMapping("/savePersonnel")
    public Result savePersonnel(@RequestBody SysPersonnelApprovalDto dto){
        if(ObjectUtil.isEmpty(dto.getCode())) {
            return Result.error("缺少必填参数");
        }
        checkAuth(dto.getCode());
        sysBusinessService.savePersonnel(dto);
        return Result.ok();
    }

    /**
     * 修改审批人信息
     * @param dto
     * @return
     */
    @Anonymous
    @PostMapping("/updateApprovalInfo")
    public Result updateApprovalInfo(@RequestBody SysPersonnelApprovalDto dto){
        if(ObjectUtil.isEmpty(dto.getApprovalCode()) || ObjectUtil.isEmpty(dto.getApprovalName())
                || ObjectUtil.isEmpty(dto.getThreeOrg())|| ObjectUtil.isEmpty(dto.getCode())) {
            return Result.error("缺少必填参数");
        }
        checkAuth(dto.getCode());
        sysBusinessService.updateApprovalInfo(dto);
        return Result.ok();
    }

    @Anonymous
    @PostMapping("/updateBuildingRating")
    public Result updateBuildingRating(@RequestBody SysBuildingRatingDto dto){
        if(ObjectUtil.isEmpty(dto.getBuildingNos()) || ObjectUtil.isEmpty(dto.getCode())) {
            return Result.error("缺少必填参数");
        }
        checkAuth(dto.getCode());
        sysBusinessService.updateBuildingRating(dto);
        return Result.ok();
    }

    /**
     * 核对密钥
     * @param code
     * @return
     */
    public boolean checkAuth(String code){
        if(code.equals(businessAuth)){
            return true;
        }
        return false;
    }

}

package com.coocaa.meht.module.sys.enums;

import com.coocaa.meht.common.exception.CommonException;

public enum SmsType {

    NOTIFICATION("通知"),
    VERIFICATION_CODE("验证码");
    private final String description;

    SmsType(String description) {
        this.description = description;
    }

    public static SmsType fromType(String smsType) {
        for (SmsType type : SmsType.values()) {
            if (type.name().equals(smsType)) {
                return type;
            }
        }
        throw new CommonException("暂不支持当前短信发送类型");
    }
}

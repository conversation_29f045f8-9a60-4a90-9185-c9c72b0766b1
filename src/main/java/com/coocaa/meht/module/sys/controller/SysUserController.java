package com.coocaa.meht.module.sys.controller;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.sys.dao.SysRequestLogDao;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.groovy.util.Maps;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 用户管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/user")
public class SysUserController {
    //private final SysUserService sysUserService;

    @Resource
    private SysRequestLogDao sysRequestLogDao;

    @Resource
    private FeignAuthorityRpc authorityRpc;

    /**
     * 用户信息
     *
     * @return
     */
    @GetMapping("/info")
    @Reqlog(value = "登录用户信息", type = Reqlog.LogType.SELECT)
    public Result<Map<String, Object>> info() {
        LoginUser user = SecurityUser.getUser();
        return Result.ok(Maps.of("id", user.getId(),
                "code", user.getUserCode(),
                "name", user.getUserName(),
                "avatar", user.getAvatar(),
                // 权限系统cheese_authority用户id
                "authorityUserId", UserThreadLocal.getUserId()
        ));
    }

    @Anonymous
    @GetMapping("/getUv")
    public Result<List<Map<String,Object>>> getUv(){
       List<Map<String,Object>> result = sysRequestLogDao.getUv();
       return Result.ok(result);
    }

    /**
     * 模糊查询用户
     * @return
     */
    @GetMapping("/list-simple")
    public Result<?> getDetail(@RequestParam("name") String name){
        ResultTemplate<?> resultTemplate = authorityRpc.listSimple(name);
        return Result.ok(resultTemplate.getData());
    }

}
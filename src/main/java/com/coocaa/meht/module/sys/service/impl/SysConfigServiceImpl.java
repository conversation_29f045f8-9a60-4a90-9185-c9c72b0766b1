package com.coocaa.meht.module.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.sys.dao.SysConfigDao;
import com.coocaa.meht.module.sys.entity.SysConfigEntity;
import com.coocaa.meht.module.sys.service.SysConfigService;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 参数管理
 */
@Service
@RequiredArgsConstructor
public class SysConfigServiceImpl extends ServiceImpl<SysConfigDao, SysConfigEntity> implements SysConfigService {

    private static final String SYSTEM_PARAMS_KEY = "meht:sys:config";

    @Autowired
    private final RedisUtils redisUtils;

    @Value("${fsAppId}")
    private String fsAppId;

    @PostConstruct
    @Override
    public void init() {
        redisUtils.delete(SYSTEM_PARAMS_KEY);
        List<SysConfigEntity> list = this.list();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, Object> map = list.stream().filter(ele -> ele.getStatus() == 0)
                    .collect(Collectors.toMap(SysConfigEntity::getKey, SysConfigEntity::getValue));
            redisUtils.hMSet(SYSTEM_PARAMS_KEY, map, -1);
        }
    }

    @Override
    public String getVal(String key) {
        return Converts.toStr(redisUtils.hGet(SYSTEM_PARAMS_KEY, key));
    }

    @Override
    public String getVal(String key, String defaultVal) {
        String val = Converts.toStr(redisUtils.hGet(SYSTEM_PARAMS_KEY, key));
        if (val != null) {
            return val;
        }
        SysConfigEntity entity = getByKey(key);
        if (entity == null) {
            entity = new SysConfigEntity();
            entity.setKey(key).setValue(defaultVal);
            this.save(entity);
        } if (entity.getStatus() != 0) {
            return defaultVal;
        }
        redisUtils.hSet(SYSTEM_PARAMS_KEY, entity.getKey(), entity.getValue(), -1);
        return entity.getValue();
    }

    @Override
    public String getValDb(String key) {
        SysConfigEntity entity = getByKey(key);
        return entity != null && entity.getStatus() == 0 ? entity.getValue() : null;
    }

    @Override
    public void setValue(String key, String value) {
        if (StringUtils.isNotBlank(key)) {
            SysConfigEntity entity = getByKey(key);
            if (entity == null) {
                entity = new SysConfigEntity();
                entity.setKey(key).setValue(value == null ? "" : value);
                this.save(entity);
            } else {
                entity.setKey(key).setValue(value == null ? "" : value);
                entity.setStatus(0);
                this.updateById(entity);
            }
            redisUtils.hSet(SYSTEM_PARAMS_KEY, entity.getKey(), entity.getValue(), -1);
        }
    }

    @Override
    public String getFsAppId() {

        return fsAppId;
    }

    private SysConfigEntity getByKey(String key) {
        return this.getOne(new QueryWrapper<SysConfigEntity>().eq("`key`", key));
    }

}
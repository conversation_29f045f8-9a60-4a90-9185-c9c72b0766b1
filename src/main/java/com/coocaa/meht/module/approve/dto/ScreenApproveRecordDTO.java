package com.coocaa.meht.module.approve.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ScreenApproveRecordDTO", description = "本地审核节点")
public class ScreenApproveRecordDTO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 业务主键
     */
    @Schema(description = "业务主键")
    private String naturalKey;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String approveUser;
    /**
     * 审批人
     */
    @Schema(description = "审批人-名称")
    private String approveUserName;
    /**
     * 审批人
     */
    private Integer approveUserId;

    /**
     * 审批意见
     */
    @Schema(description = "审批意见")
    private String remark;

    /**
     * 审批状态
     */
    private Integer status;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    private Boolean deleteFlag;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer approveLevel;

    /**
     * 审批场景类型 BUILDING(1, "楼宇"),
     * PRICE_APPLY(2, "价格申请");
     */
    private Integer sceneType;

    /**
     * 操作类型：1-新建数据审核，2-完善评级审核
     */
    private Integer operateType;

    /**
     * 复核系数
     */
    @Schema(description = "复核系数")
    private BigDecimal finalCoefficient;

    /**
     * 规则编码
     */
    @Schema(description = "规则编码")
    private Integer ruleCode;

    /**
     * 审批实例编码
     */
    @Schema(description = "审批实例编码")
    private String instanceCode;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String version;

    /**
     * 节点ID
     */
    @Schema(description = "节点ID")
    private Integer nodeId;

    /**
     * 是否为审批节点，0：提交人节点，1：审批节点，2：结束节点
     */
    @Schema(description = "是否为审批节点，0：提交人节点，1：审批节点，2：结束节点")
    private Integer approvalFlag;
    /**
     * 审批结果，字典0138
     */
    @Schema(description = "审批结果，字典0138")
    private String approvalResult;
    /**
     * 审批结果，字典0138
     */
    @Schema(description = "审批结果，字典0138")
    private String approvalResultName;
    /**
     * 任务状态（字典0139）
     */
    @Schema(description = "任务状态（字典0139）")
    private String nodeStatus;
    /**
     * 业务类型字典0163
     */
    @Schema(description = "业务类型字典0163")
    private String approveType;

    /**
     * 取消原因（字典0140）
     */
    private String cancelReason;

    @Schema(description = "是否为最新节点")
    private Boolean latestNode;
}

package com.coocaa.meht.module.approve.service;

import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;

import java.util.List;

/**
 * 审批处理服务接口
 * 处理审批提交、操作等流程
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalProcessService {

    /**
     * 提交审批
     *
     * @param dto 审批数据
     * @return 审批结果，包含审批实例ID
     */
    String submit(ApprovalDTO dto);

    /**
     * 审批通过
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    String approve(ApprovalOperationDTO dto);

    /**
     * 审批拒绝
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    String reject(ApprovalOperationDTO dto);

    /**
     * 撤销审批
     *
     * @param dto 审批操作数据
     * @return 操作结果
     */
    String revoke(ApprovalOperationDTO dto);


    /**
     * 同步审批实例和节点数据
     *
     * @param instanceCodeList 实例编码列表
     * @return 同步结果
     */
    Boolean syncInstanceAndNodes(List<String> instanceCodeList);
}
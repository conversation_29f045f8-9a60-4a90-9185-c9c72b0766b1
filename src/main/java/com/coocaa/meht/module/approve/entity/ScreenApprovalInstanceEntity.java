package com.coocaa.meht.module.approve.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.meht.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 审批实例实体类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("screen_approval_instance")
public class ScreenApprovalInstanceEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 审批实例编码
     */
    @TableField("instance_code")
    private String instanceCode;
    /**
     * 审批实例编码
     */
    @TableField("rule_code")
    private Integer ruleCode;

    /**
     * 业务唯一标识
     */
    @TableField("biz_code")
    private String bizCode;


    /**
     * 场景类型
     */
    @TableField("approve_type")
    private String approvalType;

    /**
     * 表单数据(JSON格式)
     */
    @TableField("form_data")
    private String formData;

    /**
     * 审批状态
     */
    @TableField("approve_status")
    private String approveStatus;

    /**
     * 审批结果
     */
    @TableField("approve_result")
    private String approveResult;

    /**
     * 提交人
     */
    @TableField("submit_user")
    private String submitUser;

    /**
     * 版本号
     */
    @TableField("version")
    private String version;

    /**
     * 审批提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;
} 
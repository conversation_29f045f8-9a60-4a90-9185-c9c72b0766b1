package com.coocaa.meht.module.approve.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.module.approve.adapter.ApprovalAdapter;
import com.coocaa.meht.module.approve.dao.ScreenApprovalInstanceMapper;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.entity.ScreenApprovalInstanceEntity;
import com.coocaa.meht.module.approve.enums.ApprovalResultEnum;
import com.coocaa.meht.module.approve.enums.ApproveCancelReason;
import com.coocaa.meht.module.approve.enums.ApproveStatusEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.service.AbstractApprovalService;
import com.coocaa.meht.module.approve.service.ApprovalProcessService;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategyFactory;
import com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.coocaa.meht.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.coocaa.meht.module.approve.config.ApproveThreadPoolConfig.APPROVAL_EXECUTOR;

/**
 * 审批处理服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
public class ApprovalProcessServiceImpl extends AbstractApprovalService implements ApprovalProcessService {

    private final ApprovalAdapter approvalAdapter;
    private final ApprovalStrategyFactory strategyFactory;
    private final ScreenApprovalInstanceMapper instanceMapper;
    private final ScreenApproveRecordMapper screenApproveRecordMapper;
    private final Executor approvalExecutor;
    private final RedisUtils redisUtils;


    private static final int DEFAULT_SYNC_DAY = -7;

    /**
     * @param strategyFactory
     * @param approvalAdapter
     * @param instanceMapper
     * @param screenApproveRecordMapper
     * @param approvalExecutor
     * @param redisUtils
     */
    public ApprovalProcessServiceImpl(ApprovalStrategyFactory strategyFactory,
                                      ApprovalAdapter approvalAdapter,
                                      ScreenApprovalInstanceMapper instanceMapper,
                                      ScreenApproveRecordMapper screenApproveRecordMapper,
                                      @Qualifier(APPROVAL_EXECUTOR) Executor approvalExecutor, RedisUtils redisUtils) {
        super(strategyFactory, approvalAdapter);
        this.approvalAdapter = approvalAdapter;
        this.strategyFactory = strategyFactory;
        this.instanceMapper = instanceMapper;
        this.screenApproveRecordMapper = screenApproveRecordMapper;
        this.approvalExecutor = approvalExecutor;
        this.redisUtils = redisUtils;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(ApprovalDTO dto) {
        log.info("提交审批: {}", JSON.toJSONString(dto));
        beforeSubmit(dto);
        // 使用模板方法处理审批
        return processApproval(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String approve(ApprovalOperationDTO dto) {
        log.info("审批通过: {}", JSON.toJSONString(dto));
        return handleApprovalOperation(dto, strategy -> {
            strategy.beforeApprove(dto);
            String approveStatus = approvalAdapter.approve(dto);
            submitUpdateTask(dto.getInstanceCode());
            strategy.afterApprove(dto);
            if (ApprovalResultEnum.FINISHED.getCode().equals(approveStatus)) {
                strategy.onFinish(dto.getBusinessKey(), dto);
            }
            return approveStatus;
        }, "审批通过失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String reject(ApprovalOperationDTO dto) {
        log.info("审批拒绝: {}", JSON.toJSONString(dto));
        return handleApprovalOperation(dto, strategy -> {
            strategy.beforeRejected(dto.getBusinessKey(), dto);
            String rejectStatus = approvalAdapter.reject(dto.getInstanceCode(), dto.getOpinion());
            submitUpdateTask(dto.getInstanceCode());
            strategy.onRejected(dto.getBusinessKey(), dto);
            return rejectStatus;
        }, "审批拒绝失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String revoke(ApprovalOperationDTO dto) {
        log.info("撤销审批: {}", JSON.toJSONString(dto));
        checkRatingInstance(dto);

        return handleApprovalOperation(dto, strategy -> {
            strategy.beforeRevoke(dto);
            String status = approvalAdapter.revoke(dto.getInstanceCode());
            submitUpdateTask(dto.getInstanceCode());
            strategy.onRevoke(dto.getBusinessKey(), dto);
            return status;
        }, "撤销审批失败");
    }

    /**
     * 检查评级审批流程是否生成
     *
     * @param dto
     */
    private void checkRatingInstance(ApprovalOperationDTO dto) {
        String key = String.format("rating:complete:%s", dto.getBusinessKey());
        if (redisUtils.get(key) != null) {
            throw new ApprovalBusinessException("评级分数计算中，请稍后");
        }
    }

    /**
     * 审批操作的统一处理模板方法
     *
     * @param dto            操作数据
     * @param operationLogic 具体操作逻辑
     * @param failureMessage 失败时的错误信息前缀
     * @return 审批状态
     */
    private String handleApprovalOperation(ApprovalOperationDTO dto,
                                           ApprovalOperationLogic operationLogic,
                                           String failureMessage) {
        // 步骤1：数据准备与校验
        findAndPrepareInstanceData(dto);

        // 步骤2：获取策略
        ApprovalStrategy strategy = getStrategyByInstanceCode(dto.getInstanceCode());

        // 步骤3：执行具体操作
        try {
            return operationLogic.execute(strategy);
        } catch (ApprovalBusinessException e) {
            log.warn(failureMessage, e);
            throw new ApprovalBusinessException(failureMessage + ": " + e.getMessage(), e);
        } catch (Exception e) {
            log.error(failureMessage, e);
            throw e;
        }
    }

    /**
     * 根据DTO准备审批实例数据
     *
     * @param dto 操作数据
     */
    private void findAndPrepareInstanceData(ApprovalOperationDTO dto) {
        validateOperationDTO(dto);
        if (StrUtil.isBlank(dto.getInstanceCode())) {
            ScreenApprovalInstanceEntity existInstance = instanceMapper.selectByBusinessKey(dto.getBusinessKey(), null,
                    dto.getVersion(), List.of(ApprovalResultEnum.PENDING.getCode(), ApprovalResultEnum.PROCESSING.getCode()));
            if (existInstance == null) {
                throw new ApprovalBusinessException("未成功发起流程");
            }
            dto.setInstanceCode(existInstance.getInstanceCode());
        } else {
            ScreenApprovalInstanceEntity existInstance = instanceMapper.selectByInstanceCode(dto.getInstanceCode());
            if (existInstance == null) {
                throw new ApprovalBusinessException("未成功发起流程");
            }
            dto.setBusinessKey(existInstance.getBizCode());
            dto.setVersion(existInstance.getVersion());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncInstanceAndNodes(List<String> instanceCodeList) {
        if (CollUtil.isEmpty(instanceCodeList)) {
            LambdaQueryWrapper<ScreenApprovalInstanceEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.select(ScreenApprovalInstanceEntity::getInstanceCode);
            queryWrapper.gt(ScreenApprovalInstanceEntity::getCreateTime, DateUtil.offsetDay(new Date(), DEFAULT_SYNC_DAY));
            List<String> list = instanceMapper.selectList(queryWrapper).stream()
                    .map(ScreenApprovalInstanceEntity::getInstanceCode)
                    .filter(StringUtils::isNotBlank).distinct()
                    .toList();
            instanceCodeList.addAll(list);
        }
        instanceCodeList.forEach(this::submitUpdateTask);
        return true;
    }

    private void submitUpdateTask(String instanceCode) {
        CompletableFuture.runAsync(() -> updateLocalInstanceAndRecords(instanceCode), approvalExecutor)
                .exceptionally(ex -> {
                    log.error("异步更新本地实例和审核记录时发生异常, instanceCode: {}", instanceCode, ex);
                    return null;
                });
    }

    private void updateLocalInstanceAndRecords(String instanceCode) {
        updateInstanceFromCenter(instanceCode);
        updateLocalRecordsFromNodes(instanceCode);
    }

    /**
     * 校验审批操作数据
     *
     * @param dto 审批操作数据
     */
    private void validateOperationDTO(ApprovalOperationDTO dto) {
        Objects.requireNonNull(dto, "审批操作数据不能为空");
        if (StringUtils.isBlank(dto.getInstanceCode()) && StringUtils.isBlank(dto.getBusinessKey())) {
            throw new ApprovalBusinessException("审批实例编码与业务编码不能同时为空");
        }
    }

    /**
     * 根据实例编码获取对应的审批策略
     *
     * @param instanceCode 实例编码
     * @return 审批策略
     */
    private ApprovalStrategy getStrategyByInstanceCode(String instanceCode) {
        if (StringUtils.isEmpty(instanceCode)) {
            throw new ApprovalBusinessException("实例编码不能为空");
        }

        ScreenApprovalInstanceEntity instance = instanceMapper.selectByInstanceCode(instanceCode);
        if (instance == null) {
            log.warn("未找到审批实例: {}", instanceCode);
            throw new ApprovalBusinessException("未找到审批实例: " + instanceCode);
        }

        ApprovalStrategy strategy = strategyFactory.getStrategy(instance.getApprovalType());
        if (strategy == null) {
            log.warn("未找到对应的审批策略, approvalType: {}", instance.getApprovalType());
        }
        return strategy;
    }

    private void beforeSubmit(ApprovalDTO dto) {
        Objects.requireNonNull(dto, "审批数据不能为空");

        if (StringUtils.isEmpty(dto.getApprovalType())) {
            throw new ApprovalBusinessException("审批类型不能为空");
        }

        if (StringUtils.isEmpty(dto.getBusinessKey())) {
            throw new ApprovalBusinessException("业务唯一标识不能为空");
        }
        // 检查是否已存在审批实例
        List<String> todoStatus = List.of(ApprovalResultEnum.PENDING.getCode(), ApprovalResultEnum.PROCESSING.getCode());
        ScreenApprovalInstanceEntity existInstance = instanceMapper.getScreenApprovalInstance(dto.getBusinessKey(),
                dto.getApprovalType(), dto.getVersion(), todoStatus);

        if (existInstance != null) {
            log.warn("已存在审批实例, businessKey: {}, sceneType: {}, version: {}", dto.getBusinessKey(),
                    dto.getApprovalType(), dto.getVersion());
            throw new ApprovalBusinessException("已存在审批实例，请勿重复提交");
        }

    }


    private void updateLocalRecordsFromNodes(String instanceCode) {
        if (StrUtil.isBlank(instanceCode)) {
            return;
        }
        // 2. 从审批中心获取最新节点并更新本地记录
        List<InnerApproveNodeVO> nodes = approvalAdapter.getNodes(instanceCode);
        if (CollUtil.isEmpty(nodes)) {
            log.warn("未从审批中心获取到任何节点信息, instanceCode: {}", instanceCode);
            return;
        }
        log.info("根据审批中心节点批量更新本地数据, nodeCount: {}", nodes.size());
        for (InnerApproveNodeVO node : nodes) {
            LambdaUpdateWrapper<ScreenApproveRecordEntity> wrapper = Wrappers.<ScreenApproveRecordEntity>lambdaUpdate()
                    .eq(ScreenApproveRecordEntity::getNodeId, node.getId())
                    .set(ScreenApproveRecordEntity::getRemark, node.getComment())
                    .set(ScreenApproveRecordEntity::getApprovalFlag, node.getApprovalFlag())
                    .set(ScreenApproveRecordEntity::getApprovalResult, node.getApprovalResult())
                    .set(ScreenApproveRecordEntity::getApproveTime, node.getEndTime())
                    .set(ScreenApproveRecordEntity::getSubmitTime, node.getStartTime())
                    .set(ScreenApproveRecordEntity::getCancelReason, node.getCancelReason())
                    .set(ScreenApproveRecordEntity::getNodeStatus, node.getNodeStatus());
            // 撤回时补充备注
            if (node.getApprovalFlag() == 1
                    && ApproveCancelReason.APPLICANT_WITHDRAWN.getCode().equals(node.getCancelReason())
                    && ApproveStatusEnum.CANCELED.getCode().equals(node.getNodeStatus())) {
                wrapper.set(ScreenApproveRecordEntity::getRemark, "提交人撤回");
            }
            screenApproveRecordMapper.update(null, wrapper);
        }
//        测试环境数据库不支持此方式
//        screenApproveRecordMapper.batchUpdateNodeStatus(nodes);
    }

    /**
     * 根据审批中心数据，同步本地审批实例的状态和结果
     *
     * @param instanceCode 审批实例的唯一编码
     */
    public void updateInstanceFromCenter(String instanceCode) {
        log.info("开始从审批中心同步实例状态, instanceCode: {}", instanceCode);
        if (StrUtil.isBlank(instanceCode)) {
            log.warn("同步实例失败，instanceCode为空。");
            return;
        }

        try {
            // 1. 从审批中心获取最新详情
            ApprovalDetailVO remoteDetail = approvalAdapter.getDetail(instanceCode);
            Objects.requireNonNull(remoteDetail, "从审批中心获取实例详情失败或实例不存在, instanceCode: " + instanceCode);

            // 2. 查询本地是否存在该实例
            ScreenApprovalInstanceEntity localInstance = instanceMapper.selectByInstanceCode(instanceCode);
            Objects.requireNonNull(localInstance, "本地数据库中未找到对应的审批实例, instanceCode: " + instanceCode);

            String remoteStatus = remoteDetail.getApprovalStatus();
            String remoteResult = remoteDetail.getApprovalResult();


            // 4. 使用UpdateWrapper精确更新指定字段
            LambdaUpdateWrapper<ScreenApprovalInstanceEntity> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(ScreenApprovalInstanceEntity::getInstanceCode, instanceCode)
                    .set(ScreenApprovalInstanceEntity::getApproveStatus, remoteStatus)
                    .set(ScreenApprovalInstanceEntity::getSubmitTime, remoteDetail.getCreateTime())
                    .set(ScreenApprovalInstanceEntity::getApproveResult, remoteResult);

            int updatedRows = instanceMapper.update(null, updateWrapper);
            if (updatedRows > 0) {
                log.info("成功同步实例状态, instanceCode: {}. Status: [本地]{} -> [远程]{},"
                                + " Result: [本地]{} -> [远程]{}", instanceCode, localInstance.getApproveStatus(),
                        remoteStatus, localInstance.getApproveResult(), remoteResult);
            } else {
                log.warn("同步实例状态失败，更新数据库未影响任何行, instanceCode: {}", instanceCode);
            }

        } catch (Exception e) {
            log.error("从审批中心同步实例状态时发生异常, instanceCode: {}", instanceCode, e);
            throw new ApprovalBusinessException("同步实例状态失败: " + e.getMessage(), e);
        }
    }


    @FunctionalInterface
    private interface ApprovalOperationLogic {
        String execute(ApprovalStrategy strategy);
    }

} 
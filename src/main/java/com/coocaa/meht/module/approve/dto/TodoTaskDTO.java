package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 待办任务数据传输对象
 *
 * <AUTHOR>
 * @description
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "待办任务数据传输对象")
public class TodoTaskDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审批实例编码
     */
    @Schema(description = "审批实例编码")
    private String instanceCode;

    /**
     * 业务唯一标识
     */
    @Schema(description = "业务唯一标识")
    private String businessKey;

    /**
     * 审批类型
     */
    @Schema(description = "审批类型")
    private String approvalType;

    /**
     * 审批类型名称
     */
    @Schema(description = "审批类型名称")
    private String typeName;


    /**
     * 审核人
     */
    @Schema(description = "审核人")
    private String approveUser;


    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String submitUser;
    /**
     * 提交人名称
     */
    @Schema(description = "提交人名称")
    private String submitUserName;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    private LocalDateTime approveTime;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String version;

    /**
     * 楼宇地区
     */
    @Schema(description = "楼宇地区")
    private String city;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 审批结果
     */
    @Schema(description = "审批结果")
    private String approveResult;

    /**
     * 审批结果-名称
     */
    @Schema(description = "审批结果-名称")
    private String approveResultName;

    /**
     * 价格申请ID
     */
    @Schema(description = "价格申请ID")
    private String priceApplyId;

    /**
     * 节点ID
     */
    @Schema(description = "节点ID")
    private Integer nodeId;
}
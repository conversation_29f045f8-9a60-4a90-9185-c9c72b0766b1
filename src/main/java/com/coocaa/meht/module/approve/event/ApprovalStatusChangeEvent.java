package com.coocaa.meht.module.approve.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 审批状态变更事件
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalStatusChangeEvent {

    /**
     * 审批实例编码
     */
    private String instanceCode;

    /**
     * 业务唯一标识
     */
    private String businessKey;

    /**
     * 审批类型
     */
    private String approvalType;

    /**
     * 审批状态
     */
    private String status;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 审批时间
     */
    private LocalDateTime approvalTime;

    /**
     * 版本号
     */
    private String version;
} 
package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 审批操作数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "审批操作数据传输对象")
public class ApprovalOperationDTO {

    /**
     * 审批实例编码
     */
    @Schema(description = "审批实例编码")
    private String instanceCode;

    /**
     * 业务唯一标识
     */
    @Schema(description = "业务唯一标识")
    private String businessKey;

    /**
     * 审批节点id
     */
    @Schema(description = "审批节点id")
    private Integer taskId;

    /**
     * 审批意见
     */
    @Schema(description = "审批意见")
    private String opinion;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private String operator;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String version;

    /**
     * 审批表单额外数据
     */
    @Schema(description = "审批表单额外数据")
    private Map<String, Object> extData;
} 
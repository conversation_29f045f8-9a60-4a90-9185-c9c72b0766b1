package com.coocaa.meht.module.approve.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.approve.entity.ScreenApprovalInstanceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批实例记录Mapper
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface ScreenApprovalInstanceMapper extends BaseMapper<ScreenApprovalInstanceEntity> {
    /**
     * 根据实例ID查询审批实例
     *
     * @param instanceCode 实例ID
     * @return 审批实例
     */
    default ScreenApprovalInstanceEntity selectByInstanceCode(@Param("instanceCode") String instanceCode) {
        LambdaQueryWrapper<ScreenApprovalInstanceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApprovalInstanceEntity::getInstanceCode, instanceCode);
        return selectOne(queryWrapper);
    }

    /**
     * 根据业务键查询审批实例
     *
     * @param businessKey  业务键
     * @param approvalType 场景类型
     * @param version      版本号
     * @return 审批实例
     */
    default ScreenApprovalInstanceEntity selectByBusinessKey(
            @Param("businessKey") String businessKey,
            @Param("sceneType") String approvalType,
            @Param("version") String version,
            @Param("status") List<String> status) {
        return getScreenApprovalInstance(businessKey, approvalType, version, status);
    }

    default ScreenApprovalInstanceEntity getScreenApprovalInstance(@Param("businessKey") String businessKey,
                                                                   @Param("sceneType") String approvalType,
                                                                   @Param("version") String version,
                                                                   @Param("status") List<String> status) {
        LambdaQueryWrapper<ScreenApprovalInstanceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenApprovalInstanceEntity::getBizCode, businessKey);
        queryWrapper.eq(StrUtil.isNotBlank(approvalType), ScreenApprovalInstanceEntity::getApprovalType, approvalType);
        queryWrapper.eq(StrUtil.isNotBlank(version), ScreenApprovalInstanceEntity::getVersion, version);
        queryWrapper.in(CollUtil.isNotEmpty(status), ScreenApprovalInstanceEntity::getApproveStatus, status);
        queryWrapper.last(" LIMIT 1");
        return selectOne(queryWrapper);
    }

    /**
     * 根据业务键和场景类型查询审批实例
     *
     * @param businessKey  业务键
     * @param approvalType 审批类型
     * @param version      版本号
     * @return 审批实例
     */
    default ScreenApprovalInstanceEntity selectByBusinessKeyAndApproveType(
            @Param("businessKey") String businessKey,
            @Param("sceneType") String approvalType,
            @Param("version") String version) {
        return getScreenApprovalInstance(businessKey, approvalType, version, List.of());
    }
}
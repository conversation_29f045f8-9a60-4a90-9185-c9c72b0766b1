package com.coocaa.meht.module.approve.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.idempotent.RepeatSubmit;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.ScreenApproveRecordDTO;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.dto.TodoListQueryDTO;
import com.coocaa.meht.module.approve.dto.TodoTaskDTO;
import com.coocaa.meht.module.approve.facade.ApprovalCenterFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 审批中台控制器
 * 提供审批相关的API接口
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Tag(name = "审批中心", description = "提供审批流程相关接口")
@RestController
@RequestMapping("/approval-center")
@RequiredArgsConstructor
public class ApprovalCenterController {

    private final ApprovalCenterFacade approvalCenterFacade;

    /**
     * 提交审批
     */
    @Operation(summary = "提交审批", description = "提交审批申请")
    @PostMapping("/submit")
    @RepeatSubmit
    public Result<String> submitApproval(@RequestBody ApprovalDTO dto) {
        return Result.ok(approvalCenterFacade.submitApproval(dto));
    }

    /**
     * 审批通过
     */
    @Operation(summary = "审批通过", description = "审批通过操作")
    @PostMapping("/approve")
    @RepeatSubmit
    public Result<String> approveTask(@RequestBody ApprovalOperationDTO dto) {
        return Result.ok(approvalCenterFacade.approveTask(dto));
    }

    /**
     * 审批拒绝
     */
    @Operation(summary = "审批拒绝", description = "审批拒绝操作")
    @PostMapping("/reject")
    @RepeatSubmit
    public Result<String> rejectTask(@RequestBody ApprovalOperationDTO dto) {
        return Result.ok(approvalCenterFacade.rejectTask(dto));
    }

    /**
     * 撤销审批
     */
    @Operation(summary = "撤销审批", description = "撤销审批操作")
    @PostMapping("/revoke")
    @RepeatSubmit
    public Result<String> revokeApproval(@RequestBody ApprovalOperationDTO dto) {
        return Result.ok(approvalCenterFacade.revokeApproval(dto));
    }

    /**
     * 查询待办列表
     */
    @Operation(summary = "查询待办列表", description = "查询当前用户的待办审批任务")
    @PostMapping("/todo")
    public Result<PageResponseVO<TodoTaskDTO>> todoList(@RequestBody PageRequestVO<TodoListQueryDTO> queryDTO) {
        if (queryDTO.getQuery() == null) {
            queryDTO.setQuery(new TodoListQueryDTO());
        }
        queryDTO.getQuery().setApproveUser(UserThreadLocal.getUser().getWno());
        IPage<TodoTaskDTO> page = approvalCenterFacade.queryTodoList(queryDTO).getData();
        return Result.ok(new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords()));
    }

    /**
     * 查询已办列表
     */
    @Operation(summary = "查询已办列表", description = "查询当前用户的已办审批任务")
    @PostMapping("/done")
    public Result<PageResponseVO<TodoTaskDTO>> doneList(@RequestBody PageRequestVO<TodoListQueryDTO> queryDTO) {
        if (queryDTO.getQuery() == null) {
            queryDTO.setQuery(new TodoListQueryDTO());
        }
        queryDTO.getQuery().setApproveUser(UserThreadLocal.getUser().getWno());
        IPage<TodoTaskDTO> page = approvalCenterFacade.queryDoneList(queryDTO).getData();
        return Result.ok(new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords()));
    }

    /**
     * 查询审批详情
     */
    @Operation(summary = "查询审批详情", description = "根据审批实例编码查询审批详情")
    @GetMapping("/detail")
    public Result<ApprovalDetailVO> detail(@RequestParam String instanceCode) {
        return approvalCenterFacade.queryDetail(instanceCode);
    }

    /**
     * 根据业务键查询审批详情
     */
    @Operation(summary = "根据业务键查询审批详情", description = "根据业务唯一标识和场景类型查询审批详情")
    @GetMapping("/detail/business")
    public Result<ApprovalDetailVO> queryDetailByBusinessKey(
            @Parameter(description = "业务唯一标识") @RequestParam String businessKey,
            @Parameter(description = "审批类型") @RequestParam String approveType,
            @Parameter(description = "版本号") @RequestParam(required = false) String version) {
        return approvalCenterFacade.queryDetailByBusinessKey(businessKey, approveType, version);
    }

    /**
     * 分页查询审批列表
     */
    @Operation(summary = "分页查询审批列表", description = "根据查询条件分页查询审批列表")
    @PostMapping("/list")
    public Result<PageResponseVO<TodoTaskDTO>> queryApprovalList(@RequestBody PageRequestVO<TodoListQueryDTO> queryDTO) {
        if (queryDTO.getQuery() == null) {
            queryDTO.setQuery(new TodoListQueryDTO());
        }
        if (StrUtil.isBlank(queryDTO.getQuery().getType())) {
            return Result.ok();
        }
        queryDTO.getQuery().setApproveUser(UserThreadLocal.getUser().getWno());
        if ("todo".equals(queryDTO.getQuery().getType())) {
            return todoList(queryDTO);
        }
        if ("done".equals(queryDTO.getQuery().getType())) {
            return doneList(queryDTO);
        }
        return Result.ok();
    }

    /**
     * 审批-用户待办统计
     */
    @Operation(summary = "审批-用户待办统计")
    @PostMapping("/deal-count")
    public Result<List<TaskDealCountVO>> getTaskDealCount(@RequestBody TaskDealCountParam param) {
        return approvalCenterFacade.getTaskDealCount(param);
    }

    /**
     * 飞书跳转参数
     * @param instanceCode
     * @param type  1通知审核人  2通知抄送人  3通知提交人
     * @return
     */
    @Operation(summary = "飞书跳转参数")
    @GetMapping("/detail-params")
    public Result<Object> getDetailParams(@RequestParam("instanceCode") String instanceCode,
                                          @RequestParam(value = "type", required = false) Integer type) {
        if (type == null) {
            type = 1;
        }
        return Result.ok(approvalCenterFacade.getDetailParams(instanceCode, type));
    }

    /**
     * 从审批中心同步实例及节点
     */
    @Operation(summary = "从审批中心同步实例及节点")
    @PostMapping("/sync-instance")
    public Result<Boolean> syncInstanceAndNodes(@RequestBody List<String> instanceCodeList) {
        return Result.ok(approvalCenterFacade.syncInstanceAndNodes(instanceCodeList));
    }


    /**
     * 查询本地节点记录
     */
    @Operation(summary = "查询本地节点记录")
    @GetMapping("/local-records")
    public Result<List<ScreenApproveRecordDTO>> queryLocalNodes(@RequestParam("businessKey") String businessKey) {
        return approvalCenterFacade.queryLocalNodes(businessKey);
    }
} 
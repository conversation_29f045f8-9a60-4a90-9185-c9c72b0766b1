package com.coocaa.meht.module.approve.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 审批事件发布器
 * 负责发布审批状态变更事件到所有支持的监听器
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApprovalEventPublisher {

    private final List<ApprovalEventListener> listeners;

    /**
     * 发布审批状态变更事件
     *
     * @param event 审批状态变更事件
     */
    public void publish(ApprovalStatusChangeEvent event) {
        if (event == null) {
            log.warn("审批状态变更事件为空，无法发布");
            return;
        }

        log.info("发布审批状态变更事件: {}", event);

        String approvalType = event.getApprovalType();
        if (approvalType == null) {
            log.warn("审批状态变更事件场景类型为空，无法确定监听器");
            return;
        }

        boolean processed = false;
        for (ApprovalEventListener listener : listeners) {
            if (listener.support(approvalType)) {
                try {
                    log.debug("调用监听器处理事件: {}", listener.getClass().getSimpleName());
                    listener.onStatusChanged(event);
                    processed = true;
                } catch (Exception e) {
                    log.error("监听器处理事件异常: {}", listener.getClass().getSimpleName(), e);
                }
            }
        }

        if (!processed) {
            log.warn("未找到支持场景类型 {} 的监听器", approvalType);
        }
    }
} 
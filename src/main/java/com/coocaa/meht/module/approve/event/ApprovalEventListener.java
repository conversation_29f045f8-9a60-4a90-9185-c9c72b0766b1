package com.coocaa.meht.module.approve.event;

/**
 * 审批事件监听器接口
 * 用于处理审批状态变更事件
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalEventListener {

    /**
     * 判断是否支持处理指定审批类型的事件
     *
     * @param approvalType 审批类型类型
     * @return 是否支持
     */
    boolean support(String approvalType);

    /**
     * 处理审批状态变更事件
     *
     * @param event 状态变更事件
     */
    void onStatusChanged(ApprovalStatusChangeEvent event);
} 
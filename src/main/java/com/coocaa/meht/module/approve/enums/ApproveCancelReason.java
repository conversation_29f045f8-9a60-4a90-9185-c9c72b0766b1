package com.coocaa.meht.module.approve.enums;

import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-06-16
 */
@Getter
@AllArgsConstructor
public enum ApproveCancelReason implements IEnumType<String> {
    /**
     * 前置节点已拒绝
     */
    PRE_NODE_REJECTED("0140-1", "前置节点已拒绝"),

    /**
     * 申请人撤回
     */
    APPLICANT_WITHDRAWN("0140-2", "申请人撤回"),

    /**
     * 前置节点已驳回
     */
    PRE_NODE_DISMISSED("0140-3", "前置节点已驳回"),

    /**
     * 驳回转取消
     */
    DISMISSED_TO_CANCEL("0140-4", "驳回转取消");

    private final String code;
    private final String desc;
}

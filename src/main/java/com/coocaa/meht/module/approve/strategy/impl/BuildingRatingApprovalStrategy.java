package com.coocaa.meht.module.approve.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.enums.ApproveFieldTypeEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.building.entity.BuildingScreenEntity;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.RatingService;
import com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 楼宇评级审批策略实现
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingRatingApprovalStrategy implements ApprovalStrategy {


    private final ScreenApproveRecordMapper screenApproveRecordMapper;

    private final BuildingRatingService buildingRatingService;

    private final BuildingScreenService buildingScreenService;

    private final RedisUtils redisUtils;

    @Lazy
    @Autowired
    private RatingService ratingService;


    @Override
    public String getApproveType() {
        return ApprovalTypeEnum.BUILDING_APPROVAL.getCode();
    }

    @Override
    public void validateData(ApprovalDTO data) {
        log.info("验证楼宇审批数据: {}", data);
    }

    @Override
    public List<InnerApproveTemplateParam> buildFormData(ApprovalDTO data) {
        log.info("构建楼宇审批表单数据");
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, data.getBusinessKey())
                .one();

        InnerApproveTemplateParam cityParam = new InnerApproveTemplateParam();
        cityParam.setKey("city");
        cityParam.setValue(ratingEntity.getMapCity());
        cityParam.setType(ApproveFieldTypeEnum.CHARACTER.getCode());

        BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, data.getBusinessKey())
                .one();

        InnerApproveTemplateParam largeScreenParam = new InnerApproveTemplateParam();
        if (screenEntity == null) {
            largeScreenParam.setValue("small");
        } else {
            String isLargeScreen = buildingScreenService.isLargeScreen(screenEntity.getSpec()) ? "large" : "small";
            largeScreenParam.setValue(isLargeScreen);
        }
        largeScreenParam.setKey("screenType");
        largeScreenParam.setType(ApproveFieldTypeEnum.CHARACTER.getCode());

        // 构建楼宇审批的表单数据
        return List.of(cityParam, largeScreenParam);
    }

    @Override
    public void onFinish(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理楼宇审批通过, businessKey: {}, operationDTO: {}", businessKey, operationDTO);

        try {
            String opinion = operationDTO.getOpinion();
            String operator = operationDTO.getOperator();
            log.info("楼宇审批流程通过 - 流程实例编码{} 审批意见: {}, 操作人: {}", operationDTO.getInstanceCode(), opinion, operator);
            ratingService.handleApproved(businessKey);
        } catch (Exception e) {
            log.error("处理楼宇审批通过异常", e);
            throw new ApprovalBusinessException("处理楼宇审批通过失败: " + e.getMessage());
        }
    }

    @Override
    public void onRejected(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理楼宇审批拒绝, businessKey: {}, operationDTO: {}", businessKey, operationDTO);

        try {
            String opinion = operationDTO.getOpinion();
            String operator = operationDTO.getOperator();
            log.info("楼宇审批拒绝 - 审批意见: {}, 操作人: {}", opinion, operator);
            ratingService.handleRejected(businessKey);
        } catch (Exception e) {
            log.error("处理楼宇审批拒绝异常", e);
            throw new ApprovalBusinessException("处理楼宇审批拒绝失败: " + e.getMessage());
        }
    }

    @Override
    public void onRevoke(String businessKey, ApprovalOperationDTO operationDTO) {
        log.info("处理楼宇撤回, businessKey: {}, operationDTO: {}", businessKey, operationDTO);

        try {
            String opinion = operationDTO.getOpinion();
            String operator = operationDTO.getOperator();
            log.info("楼宇审批撤回 - 审批意见: {}, 操作人: {}", opinion, operator);
            ratingService.handleRevoke(businessKey);
        } catch (Exception e) {
            log.error("处理楼宇撤回异常", e);
            throw new ApprovalBusinessException("处理楼宇撤回失败: " + e.getMessage());
        }
    }

    @Override
    public void beforeApprove(ApprovalOperationDTO operationDTO) {
        log.info("楼宇审批通过前回调, operationDTO: {}", operationDTO);

        BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, operationDTO.getBusinessKey())
                .one();
        if (Objects.isNull(screenEntity)) {
            log.warn("数据异常，building_screen表数据不存在, buildingNo: {}", operationDTO.getBusinessKey());
            throw new ApprovalBusinessException("数据异常");
        }

        if (!buildingScreenService.isLargeScreen(screenEntity.getSpec())) {
            // 小屏忽略校验
            return;
        }

        // 大屏复核系数校验
        if (CollUtil.isEmpty(operationDTO.getExtData()) || Objects.isNull(operationDTO.getExtData().get("finalCoefficient"))) {
            throw new ApprovalBusinessException("未选择大屏复核系数");
        }
    }

    @Override
    public void afterApprove(ApprovalOperationDTO operationDTO) {
        log.info("楼宇审批通过后回调, operationDTO: {}", operationDTO);
        // 实现审批通过后的回调逻辑
        if (operationDTO.getExtData() != null) {
            BuildingScreenEntity screenEntity = buildingScreenService.lambdaQuery()
                    .eq(BuildingScreenEntity::getBuildingRatingNo, operationDTO.getBusinessKey())
                    .one();
            if (Objects.isNull(screenEntity) || !buildingScreenService.isLargeScreen(screenEntity.getSpec())) {
                return;
            }

            Object finalCoefficientObj = operationDTO.getExtData().get("finalCoefficient");
            if (finalCoefficientObj != null && StrUtil.isNotBlank(finalCoefficientObj.toString())) {
                try {
                    BigDecimal finalCoefficient = new BigDecimal(finalCoefficientObj.toString());

                    // 设置复核系数
                    buildingScreenService.lambdaUpdate()
                            .set(BuildingScreenEntity::getFinalCoefficient, finalCoefficient)
                            .eq(BuildingScreenEntity::getBuildingRatingNo, operationDTO.getBusinessKey())
                            .update();

                    ScreenApproveRecordEntity screenApproveRecordEntity = screenApproveRecordMapper.selectByTaskId(operationDTO.getTaskId());
                    if (screenApproveRecordEntity == null) {
                        log.warn("未找到与taskId对应的审批记录, taskId: {}", operationDTO.getTaskId());
                        return;
                    }
                    screenApproveRecordEntity.setFinalCoefficient(finalCoefficient);
                    screenApproveRecordMapper.updateById(screenApproveRecordEntity);
                    log.info("更新楼宇审批记录-系数: {}, taskId: {}", screenApproveRecordEntity.getFinalCoefficient(), operationDTO.getTaskId());
                } catch (NumberFormatException e) {
                    log.warn("finalCoefficient格式无效，无法转换为BigDecimal: [{}], taskId: {}", finalCoefficientObj, operationDTO.getTaskId());
                }
            }
        }
    }

    @Override
    public void beforeSubmit(ApprovalDTO dto) {
        log.info("楼宇审批策略-预处理, dto: {}", dto);
    }

    @Override
    public void beforeRevoke(ApprovalOperationDTO operationDTO) {
        String key = String.format("rating:complete:%s", operationDTO.getBusinessKey());
        if (redisUtils.get(key) != null) {
            throw new ApprovalBusinessException("评级分数计算中，请稍后");
        }
    }

    @Override
    public void afterSubmit(ApprovalDTO dto, String instanceCode) {
        log.info("楼宇审批策略-提交审核流程成功后触发, dto: {}, instanceCode: {}", dto, instanceCode);
        ScreenApproveRecordEntity screenApproveRecordEntity = screenApproveRecordMapper.selectFirstAuditRecord(dto.getBusinessKey(), instanceCode);
        if (screenApproveRecordEntity == null) {
            log.error("楼宇审批策略-提交审核流程成功后触发-未找到审批记录, dto: {}, instanceCode: {}", dto, instanceCode);
        }

    }
}
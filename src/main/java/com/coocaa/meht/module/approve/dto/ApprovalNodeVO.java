package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 审批节点视图对象
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "审批节点视图对象")
public class ApprovalNodeVO {

    /**
     * 节点ID
     */
    @Schema(description = "节点ID")
    private Integer nodeId;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String approver;

    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private String status;

    /**
     * 审批意见
     */
    @Schema(description = "审批意见")
    private String opinion;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    /**
     * 是否当前节点
     */
    @Schema(description = "是否当前节点")
    private Boolean isCurrent;

    /**
     * 节点序号
     */
    @Schema(description = "节点序号")
    private Integer orderNum;
} 
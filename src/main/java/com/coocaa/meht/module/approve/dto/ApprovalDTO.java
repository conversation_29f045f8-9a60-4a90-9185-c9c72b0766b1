package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 审批数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "审批数据传输对象")
public class ApprovalDTO {
    /**
     * 业务数据
     */
    List<InnerApproveTemplateParam> form;
    /**
     * 审批类型
     *
     * @see com.coocaa.meht.module.approve.enums.ApprovalTypeEnum
     */
    @Schema(description = "审批类型")
    private String approvalType;
    /**
     * 业务唯一标识
     */
    @Schema(description = "业务唯一标识")
    private String businessKey;
    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String version;
    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String submitter;
    /**
     * 审批表单额外数据
     */
    @Schema(description = "审批表单额外数据")
    private Map<String, Object> extData;

} 
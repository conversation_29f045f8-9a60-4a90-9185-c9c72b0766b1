package com.coocaa.meht.module.approve.adapter;

import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;

import java.util.List;

/**
 * 审批中心适配器接口
 * 定义与审批中心交互的标准方法
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalAdapter {
    /**
     * 提交审批申请
     *
     * @param type        审批类型
     * @param businessKey 业务唯一标识
     * @param formData    审批表单数据
     * @param version     版本号
     * @param submitter   提交人
     * @return 审批实例ID
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    String submit(String type, String businessKey, List<InnerApproveTemplateParam> formData, String version, String submitter);


    /**
     * 审批通过操作
     *
     * @param dto 审批参数
     * @return 当前流程状态
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    String approve(ApprovalOperationDTO dto);

    /**
     * 审批拒绝操作
     *
     * @param instanceCode 审批实例ID
     * @param opinion      审批意见
     * @return 当前流程状态
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    String reject(String instanceCode, String opinion);

    /**
     * 获取审批详情
     *
     * @param instanceCode 审批实例ID
     * @return 审批详情
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    ApprovalDetailVO getDetail(String instanceCode);

    /**
     * 获取审批节点信息
     *
     * @param instanceCode 审批实例ID
     * @return 审批节点列表
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    List<InnerApproveNodeVO> getNodes(String instanceCode);

    /**
     * 获取用户待办统计
     *
     * @param param a
     * @return {@link List}<{@link TaskDealCountVO}>
     */
    List<TaskDealCountVO> getTaskDealCount(TaskDealCountParam param);

    /**
     * 申请人撤回审批申请
     *
     * @param instanceCode 审批实例ID
     * @return 当前流程状态
     * @throws ApprovalBusinessException 当调用审批中心失败时抛出
     */
    String revoke(String instanceCode);
} 
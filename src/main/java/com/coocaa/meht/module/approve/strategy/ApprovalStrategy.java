package com.coocaa.meht.module.approve.strategy;

import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;

import java.util.List;

/**
 * 审批策略接口
 * 定义不同类型审批的差异化业务逻辑
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface ApprovalStrategy {
    /**
     * 获取策略类型标识
     *
     * @return 审批类型标识
     */
    String getApproveType();

    /**
     * 验证审批数据的有效性
     *
     * @param data 审批数据
     * @throws ApprovalBusinessException 当数据校验失败时抛出
     */
    void validateData(ApprovalDTO data);

    /**
     * 构建提交到审批中心的表单数据
     *
     * @param data 审批数据
     * @return 转换后的表单数据
     */
    List<InnerApproveTemplateParam> buildFormData(ApprovalDTO data);


    /**
     * 处理流程通完成后的业务逻辑
     *
     * @param businessKey  业务唯一标识
     * @param operationDTO 审批操作数据
     */
    void onFinish(String businessKey, ApprovalOperationDTO operationDTO);

    /**
     * 处理审批拒绝后的业务逻辑
     *
     * @param businessKey  业务唯一标识
     * @param operationDTO 审批操作数据
     */
    void onRejected(String businessKey, ApprovalOperationDTO operationDTO);

    /**
     * 处理审批拒绝前的业务逻辑
     *
     * @param businessKey  业务唯一标识
     * @param operationDTO 审批操作数据
     */
    default void beforeRejected(String businessKey, ApprovalOperationDTO operationDTO) {

    }


    /**
     * 处理撤回前的业务逻辑
     *
     * @param operationDTO 审批操作数据
     */
    default void beforeRevoke(ApprovalOperationDTO operationDTO) {

    }

    /**
     * 处理撤回后的业务逻辑
     *
     * @param businessKey  业务唯一标识
     * @param operationDTO 审批操作数据
     */
    void onRevoke(String businessKey, ApprovalOperationDTO operationDTO);

    /**
     * 业务预处理钩子方法
     * 提交审批前触发
     * 策略可实现特定的业务预处理逻辑
     *
     * @param dto 审批数据
     */
    default void beforeSubmit(ApprovalDTO dto) {
        // 默认空实现，子类可覆盖
    }

    /**
     * 业务流程提交后处理钩子方法
     * 提交审核流程成功后触发
     * 策略可实现特定的业务后处理逻辑
     *
     * @param dto          审批数据
     * @param instanceCode 审批实例编码
     */
    default void afterSubmit(ApprovalDTO dto, String instanceCode) {
        // 默认空实现，子类可覆盖
    }

    /**
     * 审批提交前回调钩子
     * 每次审核前都会触发
     *
     * @param operationDTO 审批操作数据
     */
    default void beforeApprove(ApprovalOperationDTO operationDTO) {
        // 默认空实现，子类可覆盖
    }

    /**
     * 审批通过后回调钩子
     * 在调用onApproved后执行
     * 每次审核通过都会触发
     *
     * @param operationDTO 审批操作数据
     */
    default void afterApprove(ApprovalOperationDTO operationDTO) {
        // 默认空实现，子类可覆盖
    }


}
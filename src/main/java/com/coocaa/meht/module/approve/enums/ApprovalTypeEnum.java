package com.coocaa.meht.module.approve.enums;

import cn.hutool.core.util.StrUtil;
import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批场景类型枚举  字典0163
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Getter
@AllArgsConstructor
public enum ApprovalTypeEnum implements IEnumType<String> {
    BUILDING_APPROVAL("0164-1", "楼宇评级审批"),
    PRICE_APPROVAL("0164-2", "价格申请审批"),
    COMPLETE_BUILDING_APPROVAL("0164-3", "完善评级审批");

    private final String code;
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ApprovalTypeEnum getByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        for (ApprovalTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        for (ApprovalTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }


} 
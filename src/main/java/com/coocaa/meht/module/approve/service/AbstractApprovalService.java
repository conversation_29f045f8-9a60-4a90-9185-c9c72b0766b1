package com.coocaa.meht.module.approve.service;

import com.coocaa.meht.module.approve.adapter.ApprovalAdapter;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategy;
import com.coocaa.meht.module.approve.strategy.ApprovalStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 审批服务抽象类
 * 使用模板方法模式定义审批处理流程
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public abstract class AbstractApprovalService {

    private final ApprovalStrategyFactory strategyFactory;
    private final ApprovalAdapter approvalAdapter;

    /**
     * 处理审批提交的模板方法
     * 定义了标准的审批处理流程
     *
     * @param dto 审批请求数据
     * @return 审批结果，包含审批实例ID
     */
    @Transactional(rollbackFor = Exception.class)
    public String processApproval(ApprovalDTO dto) {
        if (dto == null) {
            throw new ApprovalBusinessException("审批请求数据不能为空");
        }
        log.info("开始处理审批请求: {}", dto);
        try {
            // 1. 获取策略
            ApprovalStrategy strategy = strategyFactory.getStrategy(dto.getApprovalType());

            if (strategy == null) {
                log.error("不支持的审批类型: {}", dto.getApprovalType());
                throw new ApprovalBusinessException("不支持的审批类型");
            }

            // 2. 数据校验
            log.debug("执行数据校验");
            strategy.validateData(dto);

            // 3. 构建表单
            log.debug("构建审批表单");
            List<InnerApproveTemplateParam> formData = strategy.buildFormData(dto);

            // 4. 预处理业务逻辑
            log.debug("执行业务预处理");
            strategy.beforeSubmit(dto);

            // 5. 提交审批
            log.debug("提交到审批中心");
            String instanceCode = submitToApprovalSystem(dto, formData);
            log.info("审批提交成功，实例ID: {}", instanceCode);

            // 6. 后处理
            log.debug("执行业务后处理");
            strategy.afterSubmit(dto, instanceCode);

            return instanceCode;
        } catch (ApprovalBusinessException e) {
            log.warn("审批业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("审批处理异常", e);
            throw new ApprovalBusinessException("审批处理异常: " + e.getMessage());
        }
    }

    /**
     * 提交到审批系统
     *
     * @param dto      审批数据
     * @param formData 表单数据
     * @return 审批实例编码
     */
    private String submitToApprovalSystem(ApprovalDTO dto, List<InnerApproveTemplateParam> formData) {
        return approvalAdapter.submit(dto.getApprovalType(),
                dto.getBusinessKey(), formData, dto.getVersion(), dto.getSubmitter());
    }

}
package com.coocaa.meht.module.approve.enums;

import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批字段类型枚举
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Getter
@AllArgsConstructor
public enum ApproveFieldTypeEnum implements IEnumType<String> {
    /**
     * 数字类型
     */
    NUMBER("number", "数字"),

    /**
     * 字符串类型
     */
    CHARACTER("character", "字符串");


    private final String code;
    private final String desc;
}
package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 待办/已办列表查询DTO
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Data
@Schema(description = "待办/已办列表查询DTO")
public class TodoListQueryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码")
    private String approveUser;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 发起人 (对应 submitUser)
     */
    @Schema(description = "发起人")
    private String submitUser;

    @Schema(description = "关键字-模糊查询字段")
    private String keyword;

    /**
     * 审批类型 (对应 approvalType)
     *
     * @see com.coocaa.meht.module.approve.enums.ApprovalTypeEnum
     */
    @Schema(description = "审批类型  [0164-1 楼宇评级审批]  [0164-2 价格审批]  [0164-3 楼宇完善评级审批] ")
    @NotBlank(message = "审批类型不能为空")
    private String approvalType;

    /**
     * 待办/已办类型
     */
    @Schema(description = "待办/已办类型  [todo-待办]  [done-已办] ")
    private String type;
} 
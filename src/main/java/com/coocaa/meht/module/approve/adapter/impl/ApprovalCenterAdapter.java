package com.coocaa.meht.module.approve.adapter.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.module.approve.adapter.ApprovalAdapter;
import com.coocaa.meht.module.approve.config.ApprovalRuleConfig;
import com.coocaa.meht.module.approve.dao.ScreenApprovalInstanceMapper;
import com.coocaa.meht.module.approve.dto.ApprovalDetailVO;
import com.coocaa.meht.module.approve.dto.ApprovalOperationDTO;
import com.coocaa.meht.module.approve.dto.InnerApproveTemplateParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountParam;
import com.coocaa.meht.module.approve.dto.TaskDealCountVO;
import com.coocaa.meht.module.approve.entity.ScreenApprovalInstanceEntity;
import com.coocaa.meht.module.approve.enums.ApprovalResultEnum;
import com.coocaa.meht.module.approve.enums.ApproveStatusEnum;
import com.coocaa.meht.module.approve.exception.ApprovalBusinessException;
import com.coocaa.meht.module.web.dao.ScreenApproveRecordMapper;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignInnerApprovalRpc;
import com.coocaa.meht.rpc.dto.ApprovalInitiateParam;
import com.coocaa.meht.rpc.dto.InnerApproveOpinionParam;
import com.coocaa.meht.rpc.dto.RollbackCancelParam;
import com.coocaa.meht.rpc.dto.TaskDealCountRpcParam;
import com.coocaa.meht.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.meht.rpc.vo.InnerApproveNodeVO;
import com.coocaa.meht.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.meht.rpc.vo.InnerTaskOperateVO;
import com.coocaa.meht.rpc.vo.TaskDealCountRpcVO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 审批中心适配器实现
 * 实现与审批中心的交互
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Slf4j
@Service("approvalCenterAdapter")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ApprovalCenterAdapter implements ApprovalAdapter {

    private final FeignInnerApprovalRpc feignInnerApprovalRpc;
    private final ScreenApprovalInstanceMapper screenApprovalInstanceMapper;
    private final ApprovalRuleConfig approvalRuleConfig;
    private final ScreenApproveRecordMapper screenApproveRecordMapper;
    private final FeignAuthorityRpc authorityRpc;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(String type, String businessKey, List<InnerApproveTemplateParam> formData,
                         String version, String submitter) {
        try {
            validateSubmissionPrerequisites(type, businessKey, formData, version);
            Integer ruleCode = getRuleCodeForType(type);
            return initiateAndSaveApproval(businessKey, formData, version, type, ruleCode);
        } catch (ApprovalBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交审批失败, businessKey: {}, type: {}", businessKey, type, e);
            throw new ApprovalBusinessException("提交审批失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String approve(ApprovalOperationDTO dto) {
        String instanceCode = dto.getInstanceCode();
        String opinion = dto.getOpinion();
        findAndValidateInstance(instanceCode);

        // 设置待审核节点
        setPendingNodeId(dto, instanceCode);

        InnerInstanceTaskVO task = getTask(instanceCode);
        if (task.getUserId() == null || !task.getUserId().equals(UserThreadLocal.getUserId())) {
            throw new ApprovalBusinessException("当前用户与审核人员不符");
        }

        InnerApproveOpinionParam param = buildOpinionParam(instanceCode, opinion, task.getTaskId(), "同意");
        InnerTaskOperateVO resultVo = RpcUtils.unBox(feignInnerApprovalRpc.agreeApproval(param));
        return resultVo.getApprovalStatus();
    }

    private void setPendingNodeId(ApprovalOperationDTO dto, String instanceCode) {
        List<InnerApproveNodeVO> nodes = RpcUtils.unBox(feignInnerApprovalRpc.queryNodes(instanceCode));
        if (CollUtil.isEmpty(nodes)) {
            log.warn("从审批中心未获取到任何节点信息, instanceCode: {}", instanceCode);
            throw new ApprovalBusinessException("未找到任何审批节点");
        }
        InnerApproveNodeVO pendingNode = nodes.stream()
                .filter(node -> node != null && ApproveStatusEnum.PENDING.getCode().equals(node.getNodeStatus()))
                .findFirst()
                .orElseThrow(() -> {
                    log.error("在审批实例中未找到待处理(PENDING)的审批节点, instanceCode: {}", instanceCode);
                    return new ApprovalBusinessException("未找到待处理的审批节点");
                });
        dto.setTaskId(pendingNode.getId());
    }

    private InnerInstanceTaskVO getTask(String instanceCode) {
        InnerInstanceTaskVO task;
        try {
            task = RpcUtils.unBox(feignInnerApprovalRpc.queryTask(instanceCode));
            if (task == null) {
                throw new ApprovalBusinessException("无待审核节点");
            }
        } catch (ApprovalBusinessException e) {
            throw new ApprovalBusinessException("无待审核节点");
        }
        return task;
    }

    @Override
    public String reject(String instanceCode, String opinion) {
        findAndValidateInstance(instanceCode);
        InnerInstanceTaskVO task = getTask(instanceCode);
        if (task == null) {
            throw new ApprovalBusinessException("无待审核节点");
        }
        if (task.getUserId() == null || !task.getUserId().equals(UserThreadLocal.getUserId())) {
            throw new ApprovalBusinessException("当前用户与审核人员不符");
        }

        InnerApproveOpinionParam param = buildOpinionParam(instanceCode, opinion, task.getTaskId(), "拒绝");
        InnerTaskOperateVO innerTaskOperateVO = RpcUtils.unBox(feignInnerApprovalRpc.rejectApproval(param));
        return innerTaskOperateVO.getApprovalStatus();
    }

    @Override
    public ApprovalDetailVO getDetail(String instanceCode) {
        try {
            InnerApproveInstanceVO innerDetail = RpcUtils.unBox(feignInnerApprovalRpc.queryDetail(instanceCode));
            return BeanUtil.copyProperties(innerDetail, ApprovalDetailVO.class);
        } catch (ApprovalBusinessException e) {
            throw e;
        }
    }

    @Override
    public String revoke(String instanceCode) {
        findAndValidateInstance(instanceCode);
        InnerApproveInstanceVO innerApproveInstanceVO = RpcUtils.unBox(feignInnerApprovalRpc.queryDetail(instanceCode));
        if (innerApproveInstanceVO == null) {
            throw new ApprovalBusinessException("无此审批实例");
        }
        if (!ApprovalResultEnum.PENDING.getCode().equals(innerApproveInstanceVO.getApprovalStatus())) {
            throw new ApprovalBusinessException("单据正在审核中，无法撤回");
        }

        RollbackCancelParam param = new RollbackCancelParam();
        param.setInstanceCode(instanceCode);
        InnerTaskOperateVO resultVo = RpcUtils.unBox(feignInnerApprovalRpc.revoke(param));
        return resultVo.getApprovalStatus();
    }

    @Override
    public List<InnerApproveNodeVO> getNodes(String instanceCode) {
        validateInstanceCode(instanceCode);
        try {
            return RpcUtils.unBox(feignInnerApprovalRpc.queryNodes(instanceCode));
        } catch (Exception e) {
            log.error("获取审批节点失败, instanceCode: {}", instanceCode, e);
            throw new ApprovalBusinessException("获取审批节点失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TaskDealCountVO> getTaskDealCount(TaskDealCountParam param) {
        TaskDealCountRpcParam rpcParam = TaskDealCountRpcParam.builder()
                .userId(param.getUserId())
                .codes(param.getCodes())
                .build();

        List<TaskDealCountRpcVO> data = RpcUtils.unBox(feignInnerApprovalRpc.getTaskCount(rpcParam));

        return data.stream().map(rpcVo -> {
            TaskDealCountVO vo = new TaskDealCountVO();
            vo.setCode(rpcVo.getCode());
            vo.setCount(rpcVo.getCount());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 校验提交审批的前置条件
     */
    private void validateSubmissionPrerequisites(String type, String businessKey,
                                                 List<InnerApproveTemplateParam> formData, String version) {
        if (!StringUtils.hasText(type)) {
            throw new ApprovalBusinessException("审批类型不能为空");
        }
        if (!StringUtils.hasText(businessKey)) {
            throw new ApprovalBusinessException("业务唯一标识不能为空");
        }
        if (CollUtil.isEmpty(formData)) {
            throw new ApprovalBusinessException("审批表单数据不能为空");
        }
        ScreenApprovalInstanceEntity existingInstance = screenApprovalInstanceMapper
                .selectByBusinessKey(businessKey, type, version,
                        List.of(ApprovalResultEnum.PENDING.getCode(), ApprovalResultEnum.PROCESSING.getCode()));
        if (existingInstance != null) {
            throw new ApprovalBusinessException("该业务已存在审批实例");
        }
    }

    /**
     * 获取审批规则编码
     */
    private Integer getRuleCodeForType(String type) {
        Integer ruleCode = approvalRuleConfig.getRuleCode(type);
        if (ruleCode == null) {
            throw new ApprovalBusinessException("未找到对应的审批规则: " + type);
        }
        return ruleCode;
    }

    /**
     * 发起并保存审批流程
     */
    private String initiateAndSaveApproval(String businessKey, List<InnerApproveTemplateParam> formData,
                                           String version, String type, Integer ruleCode) {
        ApprovalInitiateParam param = buildInitiateParam(businessKey, formData, ruleCode);
        log.info("发起审批开始, param: {}", JSONUtil.toJsonStr(param));
        String instanceCode = RpcUtils.unBox(feignInnerApprovalRpc.initiateApproval(param));
        if (!StringUtils.hasText(instanceCode)) {
            throw new ApprovalBusinessException("审批中心返回的实例编码为空");
        }

        InnerApproveInstanceVO innerInstance = RpcUtils.unBox(feignInnerApprovalRpc.queryDetail(instanceCode));
        if (innerInstance == null) {
            throw new ApprovalBusinessException("审批中心返回的审批实例为空");
        }

        String submitter = UserThreadLocal.getUser().getWno();
        saveApprovalInstance(innerInstance, businessKey, type, param, version, ruleCode, submitter);
        saveApprovalNodes(instanceCode, businessKey, version, ruleCode, type);

        return instanceCode;
    }

    /**
     * 构建审批发起参数
     */
    private ApprovalInitiateParam buildInitiateParam(String businessKey,
                                                     List<InnerApproveTemplateParam> formData, Integer ruleCode) {
        ApprovalInitiateParam param = new ApprovalInitiateParam();
        param.setRuleCode(ruleCode);
        param.setFrom(JSON.toJSONString(formData));
        param.setBusinessKey(businessKey);
        return param;
    }

    /**
     * 保存审批实例到数据库
     */
    private void saveApprovalInstance(InnerApproveInstanceVO innerInstance,
                                      String businessKey,
                                      String type,
                                      ApprovalInitiateParam param,
                                      String version,
                                      Integer ruleCode,
                                      String submitter) {
        ScreenApprovalInstanceEntity instance = toEntity(innerInstance, new EntityBuildParam(businessKey,
                type, param, version, ruleCode));
        String wno = StrUtil.isNotBlank(submitter) ? submitter : UserThreadLocal.getUser().getWno();
        instance.setCreateBy(wno);
        instance.setUpdateBy(wno);
        instance.setSubmitUser(wno);
        screenApprovalInstanceMapper.insert(instance);
        log.info("保存审批实例记录成功, instanceCode: {}", instance.getInstanceCode());
    }

    /**
     * 转换审批实例VO到实体
     */
    private ScreenApprovalInstanceEntity toEntity(InnerApproveInstanceVO instanceVO, EntityBuildParam buildParam) {
        ScreenApprovalInstanceEntity entity = new ScreenApprovalInstanceEntity();
        entity.setInstanceCode(instanceVO.getInstanceCode());
        entity.setBizCode(buildParam.businessKey);
        entity.setApprovalType(buildParam.type);
        entity.setFormData(JSON.toJSONString(buildParam.param));
        entity.setApproveStatus(instanceVO.getApprovalStatus());
        entity.setApproveResult(instanceVO.getApprovalResult());
        entity.setRuleCode(buildParam.ruleCode);
        entity.setVersion(buildParam.version);
        return entity;
    }

    /**
     * 保存审批节点记录
     */
    private void saveApprovalNodes(String instanceCode, String businessKey, String version, Integer ruleCode, String type) {
        List<InnerApproveNodeVO> nodes = RpcUtils.unBox(feignInnerApprovalRpc.queryNodes(instanceCode));
        if (CollUtil.isNotEmpty(nodes)) {
            NodeRecordBuildParam buildParam = new NodeRecordBuildParam(businessKey, version, ruleCode, instanceCode, type);
            List<ScreenApproveRecordEntity> screenApproveRecordEntities = node2Record(nodes, buildParam);
            screenApproveRecordMapper.insert(screenApproveRecordEntities);
            log.info("保存审批节点记录成功, instanceCode: {}, nodeCount: {}", instanceCode, screenApproveRecordEntities.size());
        }
    }

    /**
     * 构建审批节点记录
     */
    private List<ScreenApproveRecordEntity> node2Record(List<InnerApproveNodeVO> nodes, NodeRecordBuildParam buildParam) {
        if (CollUtil.isEmpty(nodes)) {
            return Lists.newArrayList();
        }
        List<Integer> userIds = nodes.stream().map(InnerApproveNodeVO::getUserId).toList();
        Map<Integer, CodeNameVO> userMap = RpcUtils.unBox(authorityRpc.listUserByIds(userIds))
                .stream().collect(Collectors.toMap(CodeNameVO::getId, e -> e, (k1, k2) -> k1));
        String wno = UserThreadLocal.getUser().getWno();
        List<ScreenApproveRecordEntity> records = new ArrayList<>();
        for (InnerApproveNodeVO node : nodes) {
            ScreenApproveRecordEntity record = new ScreenApproveRecordEntity();
            record.setApproveUser(userMap.get(node.getUserId()).getWno());
            record.setApproveUserId(node.getUserId());
            record.setApproveLevel(node.getRank());
            record.setNaturalKey(buildParam.bizCode);
            record.setRuleCode(buildParam.ruleCode).setInstanceCode(buildParam.instanceCode);
            record.setRemark(node.getComment());
            record.setVersion(buildParam.version).setNodeId(node.getId());
            record.setApprovalFlag(node.getApprovalFlag());
            record.setApprovalResult(node.getApprovalResult());
            record.setNodeStatus(node.getNodeStatus());
            record.setApproveType(buildParam.type);
            record.setSubmitTime(node.getStartTime());
            record.setCancelReason(node.getCancelReason());
            if (node.getApprovalFlag() == 0) {
                record.setApproveTime(node.getStartTime());
            }
            record.setCreateBy(wno);
            record.setUpdateBy(wno);
            records.add(record);
        }
        return records;
    }

    /**
     * 校验审批实例ID是否为空
     */
    private void validateInstanceCode(String instanceCode) {
        if (!StringUtils.hasText(instanceCode)) {
            throw new ApprovalBusinessException("审批实例ID不能为空");
        }
    }

    /**
     * 查找并校验本地审批实例是否存在
     */
    private ScreenApprovalInstanceEntity findAndValidateInstance(String instanceCode) {
        validateInstanceCode(instanceCode);
        ScreenApprovalInstanceEntity instance = screenApprovalInstanceMapper.selectByInstanceCode(instanceCode);
        if (instance == null) {
            throw new ApprovalBusinessException("审批实例不存在: " + instanceCode);
        }
        return instance;
    }

    /**
     * 构建审批意见参数
     */
    private InnerApproveOpinionParam buildOpinionParam(String instanceCode, String opinion,
                                                       Integer taskId, String defaultOpinion) {
        InnerApproveOpinionParam param = new InnerApproveOpinionParam();
        param.setInstanceCode(instanceCode);
        param.setTaskId(taskId);
        param.setComment(StringUtils.hasText(opinion) ? opinion : defaultOpinion);
        return param;
    }

    /**
     * 封装构建审批实例实体的参数
     */
    private static class EntityBuildParam {
        private final String businessKey;
        private final String type;
        private final ApprovalInitiateParam param;
        private final String version;
        private final Integer ruleCode;

        EntityBuildParam(String businessKey, String type, ApprovalInitiateParam param, String version, Integer ruleCode) {
            this.businessKey = businessKey;
            this.type = type;
            this.param = param;
            this.version = version;
            this.ruleCode = ruleCode;
        }
    }

    /**
     * 封装构建审批记录的参数
     */
    private static class NodeRecordBuildParam {
        private final String bizCode;
        private final String version;
        private final Integer ruleCode;
        private final String instanceCode;
        private final String type;

        NodeRecordBuildParam(String bizCode, String version, Integer ruleCode, String instanceCode, String type) {
            this.bizCode = bizCode;
            this.version = version;
            this.ruleCode = ruleCode;
            this.instanceCode = instanceCode;
            this.type = type;
        }
    }
} 
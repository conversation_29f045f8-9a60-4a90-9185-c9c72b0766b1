package com.coocaa.meht.module.approve.exception;

/**
 * 审批同步异常
 * 用于表示数据同步过程中的异常
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public class ApprovalSyncException extends RuntimeException {

    private final String instanceCode;

    /**
     * 审批同步异常
     *
     * @param instanceCode 实例编码
     * @param message      异常信息
     */
    public ApprovalSyncException(String instanceCode, String message) {
        super(message);
        this.instanceCode = instanceCode;
    }

    /**
     * 审批同步异常
     *
     * @param instanceCode 实例编码
     * @param message      异常信息
     */
    public ApprovalSyncException(String instanceCode, String message, Throwable cause) {
        super(message, cause);
        this.instanceCode = instanceCode;
    }

    /**
     * 审批同步异常
     */
    public String getInstanceCode() {
        return instanceCode;
    }
} 
package com.coocaa.meht.module.approve.enums;

import com.coocaa.meht.module.web.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 站内审批结果
 * @since 2025-06-16
 */
@Getter
@AllArgsConstructor
public enum ApproveActionEnum implements IEnumType<String> {

    /**
     * 同意
     */
    PASS("0138-1", "同意"),

    /**
     * 拒绝
     */
    REJECT("0138-2", "拒绝"),

    /**
     * 驳回
     */
    DISMISS("0138-3", "驳回");

    private final String code;
    private final String desc;


    /**
     * 根据code获取描述
     *
     * @param approveResult
     * @return
     */
    public static String getDescByCode(String approveResult) {

        for (ApproveActionEnum value : values()) {
            if (value.getCode().equals(approveResult)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
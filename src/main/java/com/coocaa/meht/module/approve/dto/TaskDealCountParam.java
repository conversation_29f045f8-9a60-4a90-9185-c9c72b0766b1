package com.coocaa.meht.module.approve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 任务处理统计参数
 *
 * <AUTHOR>
 * @description 任务处理统计参数
 * @since 2025-06-17
 */

@Schema(description = "任务处理统计参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDealCountParam {

    @Schema(description = "查询用户，不传默认为登陆人")
    private Integer userId;

    @Schema(description = "审批规则编号数组", required = true)
    private List<Integer> codes;
} 